<?php
/**
 * Layout principale per l'area amministrativa
 */
?>
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? htmlspecialchars($pageTitle) . ' - Admin Panel' : 'Admin Panel' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom Admin CSS -->
    <style>
        #sidebar {
            min-width: 250px;
            max-width: 250px;
            min-height: 100vh;
            background: #343a40;
            color: #fff;
            transition: all 0.3s;
        }

        #sidebar.active {
            margin-left: -250px;
        }

        #sidebar .sidebar-header {
            padding: 20px;
            background: #2c3136;
        }

        #sidebar ul.components {
            padding: 20px 0;
        }

        #sidebar ul li a {
            padding: 10px 20px;
            font-size: 1.1em;
            display: block;
            color: #fff;
            text-decoration: none;
            transition: all 0.3s;
        }

        #sidebar ul li a:hover,
        #sidebar ul li a.active {
            background: #2c3136;
        }

        #sidebar ul li a i {
            margin-right: 10px;
        }

        #content {
            width: 100%;
            min-height: 100vh;
            transition: all 0.3s;
        }

        @media (max-width: 768px) {
            #sidebar {
                margin-left: -250px;
            }
            #sidebar.active {
                margin-left: 0;
            }
        }

        /* Stili aggiornati per il dropdown utente */
        .user-dropdown {
            position: relative;
            padding: 8px 15px;
        }

        .user-dropdown .btn {
            color: #333;
            background: transparent;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            font-size: 0.9rem;
        }

        .user-dropdown .btn i {
            font-size: 1.2rem;
        }

        .user-dropdown .dropdown-menu {
            position: absolute;
            right: 0;
            left: auto;
            margin-top: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.08);
            min-width: 200px;
        }

        .user-dropdown .dropdown-item {
            padding: 8px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #333;
            transition: all 0.2s;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .user-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
            color: #6c757d;
        }

        .navbar-dark .user-dropdown .btn {
            color: #fff;
        }

        .navbar-dark .user-dropdown .btn:hover {
            background-color: rgba(255,255,255,0.1);
            border-radius: 4px;
        }

        /* Rimuovi la freccetta del dropdown */
        .user-dropdown .btn.dropdown-toggle::after {
            display: none;
        }
    </style>
</head>
<body>
    <div class="d-flex">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <?php
                $config = require_once ROOT_PATH . '/config/app.php';
                if (!empty($config['logo_path']) && file_exists(ROOT_PATH . '/public/' . $config['logo_path'])) {
                    echo '<img src="' . htmlspecialchars(BASE_URL . $config['logo_path']) . '" alt="Logo Admin" height="40" class="mb-2">';
                } else {
                    require_once VIEWS_DIR . '/components/default-logo.php';
                    echo getDefaultLogo();
                }
                ?>
                <h5 class="mb-0">Admin Panel</h5>
            </div>

            <ul class="list-unstyled components">
                <li>
                    <a href="<?= htmlspecialchars(BASE_URL . 'admin') ?>" class="<?= $_SERVER['REQUEST_URI'] === '/admin' ? 'active' : '' ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>
                <li>
                    <a href="<?= htmlspecialchars(BASE_URL . 'admin/users') ?>" class="<?= strpos($_SERVER['REQUEST_URI'], 'admin/users') !== false ? 'active' : '' ?>">
                        <i class="fas fa-users-cog"></i>
                        Gestione Utenti
                    </a>
                </li>
                <li>
                    <a href="<?= htmlspecialchars(BASE_URL . 'admin/config') ?>" class="<?= strpos($_SERVER['REQUEST_URI'], 'admin/config') !== false ? 'active' : '' ?>">
                        <i class="fas fa-cogs"></i>
                        Configurazione
                    </a>
                </li>
                <li>
                    <a href="<?= htmlspecialchars(BASE_URL . 'BackupCompleto.php') ?>">
                        <i class="fas fa-database"></i>
                        Backup Completo
                    </a>
                </li>
                <li>
                    <a href="<?= htmlspecialchars(BASE_URL) ?>">
                        <i class="fas fa-home"></i>
                        Dashboard Utente
                    </a>
                </li>
            </ul>

            <div class="px-3 mt-auto mb-3">
                <!-- Rimosso il nome utente -->
            </div>
        </nav>

        <!-- Page Content -->
        <div id="content" class="bg-light">
            <!-- Top Navbar -->
            <nav class="navbar navbar-expand-lg navbar-dark bg-dark border-bottom px-4">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-link text-light">
                        <i class="fas fa-bars"></i>
                    </button>

                    <div class="ms-auto">
                        <div class="user-dropdown">
                            <button class="btn" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle"></i>
                                <span><?= htmlspecialchars($_SESSION['user']['username'] ?? 'Admin') ?></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li>
                                    <a class="dropdown-item" href="<?= htmlspecialchars(BASE_URL . 'admin/profile') ?>">
                                        <i class="fas fa-user-cog me-2"></i>
                                        Profilo
                                    </a>
                                </li>
                                <li>
                                    <a id="manualeLink" class="dropdown-item" href="<?= htmlspecialchars(BASE_URL . 'docs/manuale.html') ?>" target="_blank">
                                        <i class="fas fa-book me-2"></i>
                                        Manuale d'Uso
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="<?= htmlspecialchars(BASE_URL . 'logout') ?>">
                                        <i class="fas fa-sign-out-alt me-2"></i>
                                        Logout
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="p-4">
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?= htmlspecialchars($_SESSION['success']) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        <?php unset($_SESSION['success']); ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= htmlspecialchars($_SESSION['error']) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        <?php unset($_SESSION['error']); ?>
                    </div>
                <?php endif; ?>

                <!-- Content will be injected here -->
                <?php if (isset($content)) echo $content; ?>
            </main>
        </div>
    </div>

    <!-- Modal Manuale d'Uso -->
    <div class="modal fade" id="manualeModal" tabindex="-1" aria-labelledby="manualeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="manualeModalLabel">Manuale d'Uso</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <iframe id="manualeFrame" src="" style="width: 100%; height: 80vh; border: none;"></iframe>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Sidebar Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Inizializza tutti i dropdown di Bootstrap
            var dropdownElementList = [].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));
            var dropdownList = dropdownElementList.map(function(dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });

            // Sidebar toggle
            document.getElementById('sidebarCollapse').addEventListener('click', function() {
                document.getElementById('sidebar').classList.toggle('active');
            });

            // Responsive sidebar
            function checkWidth() {
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.add('active');
                } else {
                    document.getElementById('sidebar').classList.remove('active');
                }
            }

            window.addEventListener('resize', checkWidth);
            checkWidth();

            // Gestione apertura manuale in modale
            document.getElementById('manualeLink').addEventListener('click', function(e) {
                e.preventDefault();
                var manualeUrl = this.getAttribute('href');
                document.getElementById('manualeFrame').src = manualeUrl;
                var manualeModal = new bootstrap.Modal(document.getElementById('manualeModal'));
                manualeModal.show();
            });
        });
    </script>
</body>
</html>
