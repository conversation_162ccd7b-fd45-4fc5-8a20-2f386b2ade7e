<?php
/**
 * Quick Guide Button Component
 * @param string $guideTitle - Title of the guide
 * @param string $guideContent - Content of the guide in HTML format
 */
?>

<button type="button" 
        class="btn btn-sm btn-outline-info d-flex align-items-center" 
        data-bs-toggle="modal" 
        data-bs-target="#quickGuideModal"
        title="Guida Rapida">
    <i class="fas fa-question-circle"></i>
</button>

<!-- Quick Guide Modal -->
<div class="modal fade" id="quickGuideModal" tabindex="-1" aria-labelledby="quickGuideModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title" id="quickGuideModalLabel">
                    <i class="fas fa-book me-2 text-primary"></i><?= isset($guideTitle) ? $guideTitle : 'Guida Rapida' ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-start">
                <?php if (isset($guideContent)): ?>
                    <?= $guideContent ?>
                <?php else: ?>
                    <div class="guide-section mb-4">
                        <h6 class="fw-bold text-start">
                            <i class="fas fa-info-circle me-2 text-primary"></i>Introduzione
                        </h6>
                        <p class="text-start">Questa sezione ti aiuta a navigare e utilizzare le funzionalità principali dell'applicazione.</p>
                    </div>
                    
                    <div class="guide-section mb-4">
                        <h6 class="fw-bold text-start">
                            <i class="fas fa-star me-2 text-warning"></i>Funzionalità Principali
                        </h6>
                        <ul class="list-unstyled ps-3 text-start">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Gestione completa dei clienti (privati e aziende)</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Creazione e monitoraggio progetti</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Gestione pratiche amministrative</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Upload e organizzazione documenti</li>
                        </ul>
                    </div>

                    <div class="guide-section">
                        <h6 class="fw-bold text-start">
                            <i class="fas fa-lightbulb me-2 text-warning"></i>Suggerimenti
                        </h6>
                        <ul class="list-unstyled ps-3 text-start">
                            <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Usa i filtri per trovare rapidamente ciò che cerchi</li>
                            <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Mantieni aggiornati i dati dei clienti</li>
                            <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Verifica sempre lo stato dei progetti</li>
                            <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Controlla regolarmente le notifiche</li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Chiudi
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.modal-body {
    padding: 1.5rem;
}

.guide-section {
    margin-bottom: 1.5rem;
}

.guide-section:last-child {
    margin-bottom: 0;
}

.guide-section h6 {
    margin-bottom: 1rem;
}

.guide-section ul li {
    margin-bottom: 0.5rem;
}

.guide-section ul li:last-child {
    margin-bottom: 0;
}

/* Fix button alignment */
.guide-btn {
    height: 38px;
    line-height: 24px;
    padding: 6px 12px;
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
}

.btn {
    height: 38px;
    line-height: 24px;
    padding: 6px 12px;
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
}

.btn i {
    display: inline-flex;
    align-items: center;
}
</style>
