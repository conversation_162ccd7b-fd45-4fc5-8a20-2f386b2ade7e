<?php
$pageTitle = "Dettagli Cliente";
include VIEWS_DIR . '/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Intestazione -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="page-title">
            <i class="fas fa-user"></i>
            <?php if ($cliente['tipo_cliente'] === 'privato'): ?>
                <?= htmlspecialchars($cliente['cognome'] . ' ' . $cliente['nome']) ?>
            <?php else: ?>
                <?= htmlspecialchars($cliente['ragione_sociale']) ?>
            <?php endif; ?>
        </h2>
        <a href="<?= BASE_URL ?>clienti" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Torna alla Lista
        </a>
    </div>

    <!-- Informazioni Cliente -->
    <div class="card mb-4">
        <div class="card-header">
            <h4><i class="fas fa-info-circle"></i> Informazioni Cliente</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Tipo Cliente:</strong> 
                        <span class="badge <?= $cliente['tipo_cliente'] === 'privato' ? 'bg-primary' : 'bg-success' ?>">
                            <?= ucfirst($cliente['tipo_cliente']) ?>
                        </span>
                    </p>
                    <p><strong>Email:</strong> <?= htmlspecialchars($cliente['email']) ?></p>
                    <p><strong>Telefono:</strong> <?= htmlspecialchars($cliente['telefono']) ?></p>
                    <?php if ($cliente['tipo_cliente'] === 'privato'): ?>
                        <p><strong>Nome:</strong> <?= htmlspecialchars($cliente['nome']) ?></p>
                        <p><strong>Cognome:</strong> <?= htmlspecialchars($cliente['cognome']) ?></p>
                        <p><strong>Codice Fiscale:</strong> <?= htmlspecialchars($cliente['codice_fiscale']) ?></p>
                    <?php else: ?>
                        <p><strong>Ragione Sociale:</strong> <?= htmlspecialchars($cliente['ragione_sociale']) ?></p>
                        <p><strong>Partita IVA:</strong> <?= htmlspecialchars($cliente['partita_iva']) ?></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <p><strong>Indirizzo:</strong> <?= htmlspecialchars($cliente['indirizzo']) ?></p>
                    <p><strong>Città:</strong> <?= htmlspecialchars($cliente['citta']) ?></p>
                    <p><strong>Provincia:</strong> <?= htmlspecialchars($cliente['provincia']) ?></p>
                    <?php if (!empty($cliente['note'])): ?>
                        <p><strong>Note:</strong> <?= htmlspecialchars($cliente['note']) ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Progetti -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4><i class="fas fa-project-diagram"></i> Progetti</h4>
            <a href="<?= BASE_URL ?>progetti/nuovo/<?= $cliente['id'] ?>" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Nuovo Progetto
            </a>
        </div>
        <div class="card-body">
            <?php if (empty($progetti)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Nessun progetto trovato per questo cliente.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Nome Progetto</th>
                                <th>Data Inizio</th>
                                <th>Data Fine Prevista</th>
                                <th>Stato</th>
                                <th class="text-end">Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($progetti as $progetto): ?>
                                <tr>
                                    <td><?= htmlspecialchars($progetto['nome_progetto']) ?></td>
                                    <td><?= date('d/m/Y', strtotime($progetto['data_inizio'])) ?></td>
                                    <td><?= date('d/m/Y', strtotime($progetto['data_fine_prevista'])) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $progetto['stato'] === 'completato' ? 'success' : 
                                                            ($progetto['stato'] === 'in_corso' ? 'warning' : 'secondary') ?>">
                                            <?= ucfirst(str_replace('_', ' ', $progetto['stato'])) ?>
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        <div class="btn-group">
                                            <a href="<?= BASE_URL ?>progetti/dettagli/<?= $progetto['id'] ?>" class="btn btn-sm btn-outline-primary" title="Visualizza">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= BASE_URL ?>progetti/modifica/<?= $progetto['id'] ?>" class="btn btn-sm btn-outline-neutral" title="Modifica">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="javascript:void(0)" onclick="confirmDeleteProgetto(<?= $progetto['id'] ?>)" class="btn btn-sm btn-outline-danger" title="Elimina">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Pratiche -->
    <div class="card">
        <div class="card-header">
            <h4><i class="fas fa-folder-open"></i> Pratiche</h4>
        </div>
        <div class="card-body">
            <?php if (empty($pratiche)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Nessuna pratica trovata per questo cliente.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Numero Pratica</th>
                                <th>Progetto</th>
                                <th>Data Apertura</th>
                                <th>Data Scadenza</th>
                                <th>Stato</th>
                                <th class="text-end">Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pratiche as $pratica): ?>
                                <tr>
                                    <td><?= htmlspecialchars($pratica['numero_pratica']) ?></td>
                                    <td><?= htmlspecialchars($pratica['nome_progetto']) ?></td>
                                    <td><?= date('d/m/Y', strtotime($pratica['data_apertura'])) ?></td>
                                    <td><?= !empty($pratica['data_scadenza']) ? date('d/m/Y', strtotime($pratica['data_scadenza'])) : '-' ?></td>
                                    <td>
                                        <span class="badge bg-<?= $pratica['stato'] === 'completata' ? 'success' : 
                                                            ($pratica['stato'] === 'in_corso' ? 'warning' : 'secondary') ?>">
                                            <?= ucfirst(str_replace('_', ' ', $pratica['stato'])) ?>
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        <div class="btn-group">
                                            <a href="<?= BASE_URL ?>pratiche/dettagli/<?= $pratica['id'] ?>" class="btn btn-sm btn-outline-primary" title="Visualizza">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= BASE_URL ?>pratiche/modifica/<?= $pratica['id'] ?>" class="btn btn-sm btn-outline-neutral" title="Modifica">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="javascript:void(0)" onclick="confirmDeletePratica(<?= $pratica['id'] ?>)" class="btn btn-sm btn-outline-danger" title="Elimina">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Definisci BASE_URL per JavaScript
const BASE_URL = '<?= BASE_URL ?>';

function confirmDeleteProgetto(id) {
    Swal.fire({
        title: 'Sei sicuro?',
        text: "Questa azione non può essere annullata!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Sì, elimina!',
        cancelButtonText: 'Annulla'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `${BASE_URL}progetti/elimina/${id}`;
        }
    });
}

function confirmDeletePratica(id) {
    Swal.fire({
        title: 'Sei sicuro?',
        text: "Questa azione non può essere annullata!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Sì, elimina!',
        cancelButtonText: 'Annulla'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `${BASE_URL}pratiche/elimina/${id}`;
        }
    });
}
</script>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>
