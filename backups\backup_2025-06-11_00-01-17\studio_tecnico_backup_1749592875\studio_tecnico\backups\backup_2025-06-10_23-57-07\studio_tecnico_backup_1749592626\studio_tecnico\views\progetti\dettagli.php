<?php
$pageTitle = "Dettagli Progetto";
include VIEWS_DIR . '/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Intestazione -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="page-title">
            <i class="fas fa-project-diagram"></i>
            <?= htmlspecialchars($progetto['nome_progetto']) ?>
        </h2>
        <div>
            <a href="<?= BASE_URL ?>progetti" class="btn btn-outline-primary me-2">
                <i class="fas fa-arrow-left"></i> Torna alla Lista
            </a>
            <a href="<?= BASE_URL ?>progetti/modifica/<?= $progetto['id'] ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Modifica
            </a>
        </div>
    </div>

    <!-- Informazioni Progetto -->
    <div class="row">
        <!-- Col<PERSON>na sinistra -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h4><i class="fas fa-info-circle"></i> Informazioni Progetto</h4>
                </div>
                <div class="card-body">
                    <p><strong>Nome Progetto:</strong> <?= htmlspecialchars($progetto['nome_progetto']) ?></p>
                    <p><strong>Tipo Progetto:</strong> <?= htmlspecialchars($progetto['tipo_progetto'] ?? 'Non specificato') ?></p>
                    <p><strong>Stato:</strong> 
                        <span class="badge bg-<?= $progetto['stato'] === 'completato' ? 'success' : 
                                            ($progetto['stato'] === 'in_corso' ? 'warning' : 'secondary') ?>">
                            <?= ucfirst(str_replace('_', ' ', $progetto['stato'])) ?>
                        </span>
                    </p>
                    <p><strong>Data Inizio:</strong> <?= date('d/m/Y', strtotime($progetto['data_inizio'])) ?></p>
                    <p><strong>Data Fine Prevista:</strong> <?= date('d/m/Y', strtotime($progetto['data_fine_prevista'])) ?></p>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h4><i class="fas fa-map-marker-alt"></i> Ubicazione</h4>
                </div>
                <div class="card-body">
                    <p><strong>Indirizzo:</strong> <?= htmlspecialchars($progetto['indirizzo'] ?? 'Non specificato') ?></p>
                    <p><strong>Comune:</strong> <?= htmlspecialchars($progetto['comune'] ?? 'Non specificato') ?></p>
                    <p><strong>Provincia:</strong> <?= htmlspecialchars($progetto['provincia'] ?? 'Non specificato') ?></p>
                </div>
            </div>
        </div>

        <!-- Colonna destra -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h4><i class="fas fa-user"></i> Informazioni Cliente</h4>
                </div>
                <div class="card-body">
                    <p><strong>Cliente:</strong> 
                        <?php if ($progetto['tipo_cliente'] === 'privato'): ?>
                            <?= htmlspecialchars($progetto['cliente_nome'] . ' ' . $progetto['cliente_cognome']) ?>
                        <?php else: ?>
                            <?= htmlspecialchars($progetto['cliente_ragione_sociale']) ?>
                        <?php endif; ?>
                    </p>
                    <p>
                        <a href="<?= BASE_URL ?>clienti/dettagli/<?= $progetto['cliente_id'] ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-external-link-alt"></i> Vai al Cliente
                        </a>
                    </p>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4><i class="fas fa-folder-open"></i> Pratiche Collegate</h4>
                    <a href="<?= BASE_URL ?>pratiche/nuovo/<?= $progetto['id'] ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Nuova Pratica
                    </a>
                </div>
                <div class="card-body">
                    <?php if (isset($pratiche) && !empty($pratiche)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Numero</th>
                                        <th>Tipo</th>
                                        <th>Stato</th>
                                        <th>Azioni</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pratiche as $pratica): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($pratica['numero_pratica'] ?? '') ?></td>
                                            <td><?= htmlspecialchars(ucfirst(str_replace('_', ' ', $pratica['tipo_pratica'] ?? ''))) ?></td>
                                            <td>
                                                <span class="badge bg-<?= ($pratica['stato'] ?? '') === 'completata' ? 'success' : 
                                                                    (($pratica['stato'] ?? '') === 'in_corso' ? 'warning' : 'secondary') ?>">
                                                    <?= ucfirst(str_replace('_', ' ', $pratica['stato'] ?? '')) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?= BASE_URL ?>pratiche/dettagli/<?= $pratica['id'] ?? '' ?>" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Nessuna pratica associata a questo progetto.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Descrizione -->
    <?php if (!empty($progetto['descrizione'])): ?>
    <div class="card mb-4">
        <div class="card-header">
            <h4><i class="fas fa-align-left"></i> Descrizione</h4>
        </div>
        <div class="card-body">
            <?= nl2br(htmlspecialchars($progetto['descrizione'])) ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>
