<?php
$categorie = (new \App\Models\Allegato())->getCategorie();
?>

<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Allegati</h5>
        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#modalCaricaAllegato">
            <i class="fas fa-plus"></i> Aggiungi Allegato
        </button>
    </div>
    <div class="card-body">
        <div id="allegatiList" class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Nome File</th>
                        <th>Categoria</th>
                        <th>Dimensione</th>
                        <th>Data</th>
                        <th>Azioni</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="5" class="text-center">Caricamento allegati...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Carica Allegato -->
<div class="modal fade" id="modalCaricaAllegato" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Carica Allegato</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="formCaricaAllegato" enctype="multipart/form-data">
                    <?php echo $csrf_field; ?>
                    <input type="hidden" name="pratica_id" value="<?php echo $pratica['id']; ?>">
                    
                    <div class="mb-3">
                        <label for="file" class="form-label">File *</label>
                        <input type="file" class="form-control" id="file" name="file" required>
                        <div class="form-text">
                            Dimensione massima: 10MB<br>
                            Formati supportati: JPG, PNG, PDF, DOC, DOCX, XLS, XLSX
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="categoria" class="form-label">Categoria *</label>
                        <select class="form-select" id="categoria" name="categoria" required>
                            <?php foreach ($categorie as $value => $label): ?>
                                <option value="<?php echo $value; ?>"><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="descrizione" class="form-label">Descrizione</label>
                        <textarea class="form-control" id="descrizione" name="descrizione" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                <button type="button" class="btn btn-primary" onclick="caricaAllegato()">Carica</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    caricaListaAllegati();
});

function caricaListaAllegati() {
    fetch(`<?php echo BASE_URL; ?>allegati/getByPratica/<?php echo $pratica['id']; ?>`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const tbody = document.querySelector('#allegatiList tbody');
                if (data.allegati.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5" class="text-center">Nessun allegato presente</td></tr>';
                    return;
                }

                tbody.innerHTML = data.allegati.map(allegato => `
                    <tr>
                        <td>${allegato.nome_file}</td>
                        <td>${getCategoriaLabel(allegato.categoria)}</td>
                        <td>${allegato.dimensione_formattata}</td>
                        <td>${allegato.data_caricamento_formattata}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="<?php echo BASE_URL; ?>allegati/download/${allegato.id}" 
                                   class="btn btn-outline-primary" 
                                   title="Scarica">
                                    <i class="fas fa-download"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-outline-danger" 
                                        onclick="eliminaAllegato(${allegato.id})"
                                        title="Elimina">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('');
            } else {
                showError('Errore nel caricamento degli allegati');
            }
        })
        .catch(error => showError('Errore di connessione'));
}

function caricaAllegato() {
    const form = document.getElementById('formCaricaAllegato');
    const formData = new FormData(form);

    fetch('<?php echo BASE_URL; ?>allegati/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('File caricato con successo');
            form.reset();
            $('#modalCaricaAllegato').modal('hide');
            caricaListaAllegati();
        } else {
            showError(data.error || 'Errore nel caricamento del file');
        }
    })
    .catch(error => showError('Errore di connessione'));
}

function eliminaAllegato(id) {
    if (!confirm('Sei sicuro di voler eliminare questo allegato?')) {
        return;
    }

    const formData = new FormData();
    formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);

    fetch(`<?php echo BASE_URL; ?>allegati/delete/${id}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Allegato eliminato con successo');
            caricaListaAllegati();
        } else {
            showError(data.error || 'Errore nell\'eliminazione dell\'allegato');
        }
    })
    .catch(error => showError('Errore di connessione'));
}

function getCategoriaLabel(value) {
    const categorie = <?php echo json_encode($categorie); ?>;
    return categorie[value] || value;
}

function showSuccess(message) {
    Swal.fire({
        icon: 'success',
        title: 'Successo',
        text: message,
        timer: 2000,
        showConfirmButton: false
    });
}

function showError(message) {
    Swal.fire({
        icon: 'error',
        title: 'Errore',
        text: message
    });
}
</script>
