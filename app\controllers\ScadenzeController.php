<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Config\Database;
use PDO;

class ScadenzeController extends Controller {
    private $db;

    public function __construct() {
        parent::__construct();
        $this->db = Database::getInstance();
    }

    public function index() {
        try {
            $stmt = $this->db->query("
                SELECT * FROM scadenze 
                ORDER BY data_scadenza ASC
            ");
            $scadenze = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $this->view('scadenze/index', ['scadenze' => $scadenze]);
        } catch (\Exception $e) {
            error_log($e->getMessage());
            $this->view('errors/500');
        }
    }

    public function nuova() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Implementare la logica per salvare una nuova scadenza
        } else {
            $stmt = $this->db->query("SELECT id, nome FROM progetti ORDER BY nome ASC");
            $progetti = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $this->view('scadenze/nuova', ['progetti' => $progetti]);
        }
    }

    public function modifica($id) {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Implementare la logica per aggiornare una scadenza
        } else {
            try {
                $stmt = $this->db->prepare("SELECT * FROM scadenze WHERE id = ?");
                $stmt->execute([$id]);
                $scadenza = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$scadenza) {
                    header('Location: ' . BASE_URL . 'scadenze');
                    exit;
                }

                $stmt = $this->db->query("SELECT id, nome FROM progetti ORDER BY nome ASC");
                $progetti = $stmt->fetchAll(PDO::FETCH_ASSOC);

                $this->view('scadenze/modifica', [
                    'scadenza' => $scadenza,
                    'progetti' => $progetti
                ]);
            } catch (\Exception $e) {
                error_log($e->getMessage());
                $this->view('errors/500');
            }
        }
    }

    public function elimina($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM scadenze WHERE id = ?");
            $stmt->execute([$id]);
            
            header('Location: ' . BASE_URL . 'scadenze');
            exit;
        } catch (\Exception $e) {
            error_log($e->getMessage());
            $this->view('errors/500');
        }
    }
}
