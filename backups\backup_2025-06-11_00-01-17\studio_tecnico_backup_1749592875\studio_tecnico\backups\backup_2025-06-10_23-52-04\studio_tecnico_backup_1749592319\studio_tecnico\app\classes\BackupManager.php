<?php
namespace App\Classes;

class BackupManager {
    private $maxExecutionTime = 300; // 5 minuti
    private $memoryLimit = '256M';
    private $excludeDirs = ['temp', 'tmp', '.git', '.svn', 'node_modules'];
    
    public function __construct() {
        ini_set('max_execution_time', $this->maxExecutionTime);
        ini_set('memory_limit', $this->memoryLimit);
    }
    
    /**
     * Genera un nome file per il backup
     */
    public function generateBackupFilename($prefix) {
        $date = new \DateTime();
        return $prefix . '_' . $date->format('Y-m-d_H-i-s');
    }
    
    /**
     * Crea un archivio ZIP
     */
    public function createZipArchive($source, $destination) {
        if (extension_loaded('zip')) {
            return $this->createZipWithNativeExtension($source, $destination);
        }
        return $this->createFallbackBackup($source, $destination);
    }
    
    /**
     * Crea ZIP usando l'estensione nativa
     */
    private function createZipWithNativeExtension($source, $destination) {
        error_log("Inizio creazione ZIP con estensione nativa");
        error_log("Source: " . $source);
        error_log("Destination: " . $destination);
        
        $zip = new \ZipArchive();
        
        if ($zip->open($destination, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === TRUE) {
            $this->addFolderToZip($zip, $source);
            
            // Aggiungi file di informazioni sul backup
            $infoContent = $this->generateBackupInfo($source, $zip->numFiles);
            $zip->addFromString('backup_info.txt', $infoContent);
            error_log("Aggiunto file di informazioni al backup");

            if ($zip->close()) {
                error_log("File ZIP creato con successo: " . $destination);
                return true;
            }
            
            error_log("Errore nella chiusura del file ZIP: " . $destination);
            return false;
        }
        
        error_log("Errore nell'apertura/creazione del file ZIP: " . $destination);
        return false;
    }
    
    /**
     * Aggiunge ricorsivamente cartelle e file allo ZIP
     */
    private function addFolderToZip($zip, $folderPath, $relativePath = '') {
        $dir = opendir($folderPath);
        
        while ($file = readdir($dir)) {
            if ($file == '.' || $file == '..') continue;
            
            $filePath = $folderPath . '/' . $file;
            $zipPath = $relativePath . $file;
            
            // Ignora file e cartelle specifici
            if (in_array($file, $this->excludeDirs)) {
                error_log("Ignorato file/cartella di sistema: " . $zipPath);
                continue;
            }
            
            if (is_file($filePath)) {
                if ($zip->addFile($filePath, $zipPath)) {
                    error_log("Aggiunto file al backup: " . $zipPath);
                } else {
                    error_log("Errore nell'aggiunta del file: " . $zipPath);
                }
            } elseif (is_dir($filePath)) {
                if ($zip->addEmptyDir($zipPath . '/')) {
                    error_log("Aggiunta directory al backup: " . $zipPath);
                    $this->addFolderToZip($zip, $filePath, $zipPath . '/');
                } else {
                    error_log("Errore nella creazione della directory: " . $zipPath);
                }
            }
        }
        
        closedir($dir);
    }
    
    /**
     * Crea un backup senza l'estensione ZIP
     */
    private function createFallbackBackup($source, $destination) {
        $backupDir = dirname($destination) . '/backup_' . date('Y-m-d_H-i-s');
        if (!file_exists($backupDir)) {
            mkdir($backupDir, 0777, true);
        }
        
        if (file_exists($source)) {
            if (is_dir($source)) {
                $this->copyDirectory($source, $backupDir . '/' . basename($source));
            } else {
                copy($source, $backupDir . '/' . basename($source));
            }
        }
        
        // Crea file di informazioni
        $infoContent = $this->generateBackupInfo($source);
        file_put_contents($backupDir . '/backup_info.txt', $infoContent);
        
        return $backupDir;
    }
    
    /**
     * Copia ricorsivamente una directory
     */
    public function copyDirectory($source, $destination) {
        if (!is_dir($destination)) {
            mkdir($destination, 0777, true);
        }
        
        $dir = dir($source);
        while (false !== ($entry = $dir->read())) {
            if ($entry == '.' || $entry == '..') continue;
            
            $sourcePath = $source . '/' . $entry;
            $destPath = $destination . '/' . $entry;
            
            if (is_dir($sourcePath)) {
                if (!in_array($entry, $this->excludeDirs)) {
                    $this->copyDirectory($sourcePath, $destPath);
                }
            } else {
                copy($sourcePath, $destPath);
            }
        }
        $dir->close();
    }
    
    /**
     * Genera informazioni sul backup
     */
    private function generateBackupInfo($source, $numFiles = null) {
        $info = "Backup creato il: " . date('Y-m-d H:i:s') . "\n";
        $info .= "Directory di origine: " . $source . "\n";
        if ($numFiles !== null) {
            $info .= "Numero totale di file: " . $numFiles . "\n";
        }
        return $info;
    }
    
    /**
     * Verifica e prepara la directory di backup
     */
    public function prepareBackupDirectory($path) {
        if (!file_exists($path)) {
            if (!mkdir($path, 0777, true)) {
                throw new \Exception("Impossibile creare la directory di backup");
            }
        }
        
        if (!is_writable($path)) {
            throw new \Exception("La directory di backup non è scrivibile");
        }
        
        return true;
    }
    
    /**
     * Backup del database
     */
    public function backupDatabase($host, $user, $pass, $name, $outputFile) {
        // Percorso di mysqldump per XAMPP
        $mysqldump = 'C:\\xampp\\mysql\\bin\\mysqldump.exe';
        
        if (!file_exists($mysqldump)) {
            throw new \Exception("mysqldump non trovato in: " . $mysqldump);
        }

        // Costruisci il comando
        $command = sprintf(
            '"%s" --host=%s --user=%s --password=%s %s > %s',
            $mysqldump,
            escapeshellarg($host),
            escapeshellarg($user),
            escapeshellarg($pass),
            escapeshellarg($name),
            escapeshellarg($outputFile)
        );

        // Esegui il comando e cattura l'output
        $output = [];
        $returnVar = 0;
        exec($command . " 2>&1", $output, $returnVar);

        if ($returnVar !== 0) {
            throw new \Exception("Errore durante il backup del database: " . implode("\n", $output));
        }

        return file_exists($outputFile) && filesize($outputFile) > 0;
    }
}
