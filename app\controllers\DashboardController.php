<?php
namespace App\Controllers;

use PDO;
use PDOException;

class DashboardController {
    private $db;
    
    public function __construct($db) {
        $this->db = $db;
    }
    
    private function logSeparator($message = '') {
        error_log("\n" . str_repeat('=', 50));
        if ($message) {
            error_log("=== " . $message . " ===");
        }
        error_log(str_repeat('=', 50) . "\n");
    }
    
    public function index() {
        if (!isset($_SESSION['user'])) {
            header('Location: ' . BASE_URL . 'login');
            exit;
        }
        
        if (isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'admin') {
            $adminProfileController = new AdminProfileController($this->db);
            if (method_exists($adminProfileController, 'checkProfile')) {
                $adminProfileController->checkProfile();
            }
        }
        
        $this->logSeparator("INIZIO SESSIONE DASHBOARD");
        
        try {
            $stats = $this->getStats();
            $hasStats = true;
        } catch (PDOException $e) {
            error_log("Errore nel recupero delle statistiche: " . $e->getMessage());
            
            $stats = [
                'total_clients' => 0,
                'total_practices' => 0,
                'active_practices' => 0,
                'total_projects' => 0,
                'pending_deadlines' => 0
            ];
            $hasStats = false;
        }
        
        $this->logSeparator("FINE SESSIONE DASHBOARD");
        
        include VIEWS_DIR . '/dashboard/index.php';
    }
    
    private function getStats() {
        $stats = [
            'total_clients' => 0,
            'total_practices' => 0,
            'active_practices' => 0,
            'total_projects' => 0,
            'pending_deadlines' => 0
        ];

        try {
            // Conteggio clienti
            $query = $this->db->query("SELECT COUNT(*) as total FROM clienti");
            $stats['total_clients'] = $query->fetchColumn();
            error_log("Clienti totali: " . $stats['total_clients']);

            // Conteggio pratiche totali
            $query = $this->db->query("SELECT COUNT(*) as total FROM pratiche");
            $stats['total_practices'] = $query->fetchColumn();
            error_log("Pratiche totali: " . $stats['total_practices']);

            // Conteggio pratiche attive
            $query = $this->db->query("SELECT COUNT(*) as total FROM pratiche WHERE stato IN ('in_revisione', 'in_attesa', 'in_corso')");
            $stats['active_practices'] = $query->fetchColumn();
            error_log("Pratiche attive: " . $stats['active_practices']);

            // Conteggio progetti totali
            $query = $this->db->query("SELECT COUNT(*) as total FROM progetti");
            $stats['total_projects'] = $query->fetchColumn();
            error_log("Progetti totali: " . $stats['total_projects']);

            // Conteggio scadenze in arrivo (prossimi 7 giorni)
            $query = $this->db->query("SELECT COUNT(*) as total FROM pratiche 
                WHERE data_scadenza IS NOT NULL 
                AND data_scadenza BETWEEN CURRENT_DATE AND DATE_ADD(CURRENT_DATE, INTERVAL 7 DAY)
                AND stato != 'completata'");
            $stats['pending_deadlines'] = $query->fetchColumn();
            error_log("Scadenze pendenti: " . $stats['pending_deadlines']);

        } catch (PDOException $e) {
            error_log("[EXCEPTION] Errore nel recupero delle statistiche: " . $e->getMessage());
        }

        return $stats;
    }

    private function getCount($query) {
        try {
            $stmt = $this->db->query($query);
            if ($stmt === false) {
                error_log("Query fallita: " . $query);
                return 0;
            }
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['total'] ?? 0;
        } catch (PDOException $e) {
            error_log("Errore in getCount per query '" . $query . "': " . $e->getMessage());
            return 0;
        }
    }
}
