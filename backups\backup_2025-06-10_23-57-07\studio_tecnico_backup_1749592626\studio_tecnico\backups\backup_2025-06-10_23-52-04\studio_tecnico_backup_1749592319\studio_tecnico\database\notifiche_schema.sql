-- database/notifiche_schema.sql - Schema per il sistema di notifiche
-- Creato il 5 Gennaio 2025 per Studio Tecnico

-- Tabella principale per le notifiche
CREATE TABLE IF NOT EXISTS `notifiche` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `tipo` enum('scadenza','pratica','progetto','sistema','fattura','cliente','documento') NOT NULL,
  `titolo` varchar(255) NOT NULL,
  `messaggio` text NOT NULL,
  `priorita` enum('bassa','media','alta') DEFAULT 'media',
  `link_azione` varchar(255) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `letta` boolean DEFAULT FALSE,
  `data_creazione` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `data_lettura` timestamp NULL DEFAULT NULL,
  <PERSON><PERSON>AR<PERSON> (`id`),
  <PERSON><PERSON>Y `idx_user_letta` (`user_id`, `letta`),
  KEY `idx_data_creazione` (`data_creazione`),
  KEY `idx_tipo` (`tipo`),
  KEY `idx_priorita` (`priorita`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabella per le preferenze di notifica degli utenti
CREATE TABLE IF NOT EXISTS `notifiche_preferenze` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `tipo_notifica` varchar(50) NOT NULL,
  `email_enabled` boolean DEFAULT TRUE,
  `push_enabled` boolean DEFAULT TRUE,
  `soglia_giorni` int(11) DEFAULT 7,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_tipo` (`user_id`, `tipo_notifica`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inserimento preferenze di default per tutti gli utenti esistenti
INSERT IGNORE INTO `notifiche_preferenze` (`user_id`, `tipo_notifica`, `email_enabled`, `push_enabled`, `soglia_giorni`)
SELECT 
    u.id,
    tipo.tipo_notifica,
    TRUE,
    TRUE,
    CASE 
        WHEN tipo.tipo_notifica IN ('scadenza', 'pratica') THEN 7
        WHEN tipo.tipo_notifica = 'sistema' THEN 1
        ELSE 3
    END
FROM `users` u
CROSS JOIN (
    SELECT 'scadenza' as tipo_notifica
    UNION SELECT 'pratica'
    UNION SELECT 'progetto' 
    UNION SELECT 'sistema'
    UNION SELECT 'fattura'
    UNION SELECT 'cliente'
    UNION SELECT 'documento'
) tipo;

-- Indici aggiuntivi per performance
CREATE INDEX IF NOT EXISTS `idx_notifiche_user_tipo_data` ON `notifiche` (`user_id`, `tipo`, `data_creazione`);
CREATE INDEX IF NOT EXISTS `idx_notifiche_letta_data` ON `notifiche` (`letta`, `data_creazione`);
CREATE INDEX IF NOT EXISTS `idx_notifiche_metadata` ON `notifiche` ((CAST(`metadata` AS CHAR(255))));

-- Vista per statistiche notifiche
CREATE OR REPLACE VIEW `v_notifiche_stats` AS
SELECT 
    u.id as user_id,
    u.username,
    COUNT(n.id) as totale_notifiche,
    SUM(CASE WHEN n.letta = FALSE THEN 1 ELSE 0 END) as non_lette,
    SUM(CASE WHEN n.letta = TRUE THEN 1 ELSE 0 END) as lette,
    SUM(CASE WHEN n.priorita = 'alta' AND n.letta = FALSE THEN 1 ELSE 0 END) as alta_priorita_non_lette,
    MAX(n.data_creazione) as ultima_notifica
FROM `users` u
LEFT JOIN `notifiche` n ON u.id = n.user_id
GROUP BY u.id, u.username;

-- Stored procedure per pulizia automatica notifiche vecchie
DELIMITER //
CREATE OR REPLACE PROCEDURE `sp_pulisci_notifiche_vecchie`(IN giorni_retention INT)
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    -- Elimina notifiche lette più vecchie del periodo specificato
    DELETE FROM `notifiche` 
    WHERE `letta` = TRUE 
    AND `data_creazione` < DATE_SUB(NOW(), INTERVAL giorni_retention DAY);
    
    SET deleted_count = ROW_COUNT();
    
    -- Log dell'operazione
    INSERT INTO `notifiche` (`user_id`, `tipo`, `titolo`, `messaggio`, `priorita`)
    SELECT 1, 'sistema', 'Pulizia automatica notifiche', 
           CONCAT('Eliminate ', deleted_count, ' notifiche vecchie di ', giorni_retention, ' giorni'), 'bassa'
    WHERE deleted_count > 0;
    
    SELECT deleted_count as notifiche_eliminate;
END //
DELIMITER ;

-- Trigger per aggiornare data_lettura quando una notifica viene marcata come letta
DELIMITER //
CREATE OR REPLACE TRIGGER `tr_notifiche_update_data_lettura`
BEFORE UPDATE ON `notifiche`
FOR EACH ROW
BEGIN
    IF NEW.letta = TRUE AND OLD.letta = FALSE THEN
        SET NEW.data_lettura = NOW();
    END IF;
END //
DELIMITER ;

-- Funzione per contare notifiche non lette per utente
DELIMITER //
CREATE OR REPLACE FUNCTION `fn_count_notifiche_non_lette`(user_id_param INT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE count_result INT DEFAULT 0;
    
    SELECT COUNT(*) INTO count_result
    FROM `notifiche`
    WHERE `user_id` = user_id_param AND `letta` = FALSE;
    
    RETURN count_result;
END //
DELIMITER ;

-- Event scheduler per pulizia automatica (esegue ogni settimana)
-- Nota: Richiede che l'event scheduler sia abilitato (SET GLOBAL event_scheduler = ON;)
CREATE EVENT IF NOT EXISTS `ev_pulizia_notifiche_settimanale`
ON SCHEDULE EVERY 1 WEEK
STARTS CURRENT_TIMESTAMP
DO
  CALL sp_pulisci_notifiche_vecchie(30);

-- Inserimento notifiche di sistema iniziali
INSERT IGNORE INTO `notifiche` (`user_id`, `tipo`, `titolo`, `messaggio`, `priorita`, `metadata`)
VALUES 
(1, 'sistema', 'Sistema notifiche attivato', 'Il sistema di notifiche è stato configurato e attivato con successo.', 'media', '{"tipo_notifica": "sistema_attivato"}'),
(1, 'sistema', 'Benvenuto nel sistema notifiche', 'Ora riceverai notifiche automatiche per scadenze, pratiche e aggiornamenti importanti.', 'bassa', '{"tipo_notifica": "benvenuto"}');

-- Commenti per documentazione
ALTER TABLE `notifiche` 
COMMENT = 'Tabella principale per la gestione delle notifiche del sistema';

ALTER TABLE `notifiche_preferenze` 
COMMENT = 'Preferenze di notifica personalizzate per ogni utente';

-- Verifica integrità e ottimizzazione
ANALYZE TABLE `notifiche`;
ANALYZE TABLE `notifiche_preferenze`;

-- Output di conferma
SELECT 'Schema notifiche creato con successo!' as messaggio,
       COUNT(*) as notifiche_esistenti
FROM `notifiche`;

SELECT 'Preferenze utente configurate!' as messaggio,
       COUNT(*) as preferenze_configurate  
FROM `notifiche_preferenze`;
