<?php
$pageTitle = "Gestione Utenti";
ob_start();
?>

<style>
.card {
    border: 1px solid #e0e0e0;
    box-shadow: none;
}

.card-body {
    background-color: #ffffff;
}

.btn-primary {
    background-color: #404040;
    border-color: #404040;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #333333;
    border-color: #333333;
}

.btn-outline-secondary {
    color: #404040;
    border-color: #d0d0d0;
}

.btn-outline-secondary:hover {
    background-color: #f0f0f0;
    color: #333333;
    border-color: #c0c0c0;
}

.btn-outline-primary {
    color: #404040;
    border-color: #d0d0d0;
}

.btn-outline-primary:hover {
    background-color: #f0f0f0;
    color: #333333;
    border-color: #c0c0c0;
}

.btn-outline-danger {
    color: #707070;
    border-color: #d0d0d0;
}

.btn-outline-danger:hover {
    background-color: #f0f0f0;
    color: #505050;
    border-color: #c0c0c0;
}

.badge.bg-success {
    background-color: #808080 !important;
}

.badge.bg-danger {
    background-color: #a0a0a0 !important;
}

.form-control, .form-select {
    border-color: #e0e0e0;
}

.form-control:focus, .form-select:focus {
    border-color: #808080;
    box-shadow: 0 0 0 0.2rem rgba(128, 128, 128, 0.25);
}

.input-group-text {
    background-color: #f8f8f8;
    border-color: #e0e0e0;
    color: #606060;
}

.pagination .page-link {
    color: #404040;
    border-color: #e0e0e0;
}

.pagination .page-item.active .page-link {
    background-color: #404040;
    border-color: #404040;
    color: #ffffff;
}

.modal-header {
    border-bottom: 1px solid #e0e0e0;
}

.modal-footer {
    border-top: 1px solid #e0e0e0;
}

.breadcrumb-item a {
    color: #606060;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #909090;
}

.table {
    color: #404040;
}

.table thead th {
    border-bottom: 2px solid #e0e0e0;
    color: #606060;
}

.table td {
    border-bottom: 1px solid #f0f0f0;
}
</style>

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-0" style="color: #404040;">Gestione Utenti</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= BASE_URL ?>admin">Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Utenti</li>
                </ol>
            </nav>
        </div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newUserModal">
            <i class="fas fa-plus"></i> Nuovo Utente
        </button>
    </div>

    <!-- Filtri e Ricerca -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="searchUser" placeholder="Cerca utente...">
                    </div>
                </div>
                <div class="col-md-4">
                    <select class="form-select" id="filterRole">
                        <option value="">Tutti i ruoli</option>
                        <option value="admin">Amministratore</option>
                        <option value="user">Utente</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-secondary w-100" id="resetFilters">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabella Utenti -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll"></th>
                            <th>Username</th>
                            <th>Ruolo</th>
                            <th>Stato</th>
                            <th>Azioni</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><input type="checkbox" class="user-select" value="<?= $user['id'] ?>"></td>
                            <td><?= htmlspecialchars($user['username']) ?></td>
                            <td><?= $user['ruolo'] === 'admin' ? 'Amministratore' : 'Utente' ?></td>
                            <td>
                                <span class="badge <?= $user['active'] ? 'bg-success' : 'bg-danger' ?>">
                                    <?= $user['active'] ? 'Attivo' : 'Inattivo' ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary edit-user" 
                                            data-id="<?= $user['id'] ?>" 
                                            data-username="<?= htmlspecialchars($user['username']) ?>"
                                            data-role="<?= $user['ruolo'] ?>">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-user" 
                                            data-id="<?= $user['id'] ?>">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Paginazione -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?= $page === $i ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal Nuovo Utente -->
<div class="modal fade" id="newUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nuovo Utente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="newUserForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Ruolo</label>
                        <select class="form-select" id="role" name="role">
                            <option value="user">Utente</option>
                            <option value="admin">Amministratore</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                <button type="button" class="btn btn-primary" id="saveNewUser">Salva</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Modifica Utente -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modifica Utente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId" name="id">
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="editUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="editPassword" class="form-label">Nuova Password (lascia vuoto per non modificare)</label>
                        <input type="password" class="form-control" id="editPassword" name="password">
                    </div>
                    <div class="mb-3">
                        <label for="editRole" class="form-label">Ruolo</label>
                        <select class="form-select" id="editRole" name="role">
                            <option value="user">Utente</option>
                            <option value="admin">Amministratore</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                <button type="button" class="btn btn-primary" id="saveEditUser">Salva</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestione selezione multipla
    const selectAll = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-select');
    
    selectAll.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Gestione nuovo utente
    document.getElementById('saveNewUser').addEventListener('click', function() {
        const formData = new FormData(document.getElementById('newUserForm'));
        fetch('<?= BASE_URL ?>admin/users/create', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Errore durante il salvataggio');
            }
        });
    });

    // Gestione modifica utente
    document.querySelectorAll('.edit-user').forEach(button => {
        button.addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
            document.getElementById('editUserId').value = this.dataset.id;
            document.getElementById('editUsername').value = this.dataset.username;
            document.getElementById('editRole').value = this.dataset.role;
            modal.show();
        });
    });

    // Salvataggio modifiche utente
    document.getElementById('saveEditUser').addEventListener('click', function() {
        const formData = new FormData(document.getElementById('editUserForm'));
        fetch('<?= BASE_URL ?>admin/users/update', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Errore durante il salvataggio');
            }
        });
    });

    // Gestione eliminazione utente
    document.querySelectorAll('.delete-user').forEach(button => {
        button.addEventListener('click', function() {
            if (confirm('Sei sicuro di voler eliminare questo utente?')) {
                const formData = new FormData();
                formData.append('id', this.dataset.id);
                fetch('<?= BASE_URL ?>admin/users/delete', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'Errore durante l\'eliminazione');
                    }
                });
            }
        });
    });
});
</script>

<?php
$content = ob_get_clean();
include VIEWS_DIR . '/layouts/admin.php';
?>
