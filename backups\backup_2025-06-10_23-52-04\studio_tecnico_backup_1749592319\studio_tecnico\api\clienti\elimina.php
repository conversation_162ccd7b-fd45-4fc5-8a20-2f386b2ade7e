<?php
require_once '../config.php';
requirePost();

try {
    if (empty($_POST['id'])) {
        sendError('ID cliente non specificato');
    }

    $stmt = $conn->prepare("DELETE FROM clienti WHERE id = ?");
    $stmt->execute([$_POST['id']]);

    if ($stmt->rowCount() > 0) {
        sendResponse([
            'success' => true,
            'message' => 'Cliente eliminato con successo'
        ]);
    } else {
        sendError('Cliente non trovato');
    }
} catch (PDOException $e) {
    sendError('Errore durante l\'eliminazione: ' . $e->getMessage());
} 