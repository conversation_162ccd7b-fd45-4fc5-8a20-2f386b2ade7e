<?php
// Mostra tutti gli errori
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Inizia il logging
error_log("Avvio dell'applicazione");

// Carica il bootstrap
require_once __DIR__ . '/bootstrap.php';
require_once ROOT_PATH . '/app/core/Autoloader.php';

error_log("Bootstrap e Autoloader caricati");

use App\Core\{Router, Autoloader};
use App\Config\Database;

// Registra l'autoloader
Autoloader::register();
error_log("Autoloader registrato");

try {
    error_log("Tentativo di connessione al database");
    // Ottieni la connessione al database
    $db = Database::getInstance();
    error_log("Connessione al database ottenuta");

    // Inizializza il router con la connessione al database
    $router = new Router($db);
    error_log("Router inizializzato");

    // Middleware di autenticazione
    $authMiddleware = function() {
        if (!isset($_SESSION['user'])) {
            error_log("Utente non autenticato, redirect al login");
            header('Location: ' . BASE_URL . 'login');
            exit;
        }
        error_log("Utente autenticato: " . $_SESSION['user']['username']);
    };

    // Middleware di autenticazione admin
    $adminMiddleware = function() {
        if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
            error_log("Accesso non autorizzato alla sezione admin");
            header('Location: ' . BASE_URL . 'login');
            exit;
        }
        error_log("Accesso admin verificato: " . $_SESSION['user']['username']);
    };

    // Auth routes (non protette)
    $router->get('login', 'AuthController', 'showLoginForm');
    $router->post('login', 'AuthController', 'login');
    $router->get('logout', 'AuthController', 'logout');
    $router->get('profilo', 'AuthController', 'profile');
    error_log("Route di autenticazione definite");

    // Admin routes (protette da adminMiddleware)
    $router->get('admin', 'AdminController', 'index', $adminMiddleware);
    $router->get('admin/logs', 'AdminController', 'viewLogs', $adminMiddleware);
    $router->get('admin/refresh-logs', 'AdminController', 'refreshLogs', $adminMiddleware);
    
    // User management routes
    $router->get('admin/users', 'AdminController', 'users', $adminMiddleware);
    $router->post('admin/users/create', 'AdminController', 'createUser', $adminMiddleware);
    $router->post('admin/users/update', 'AdminController', 'updateUser', $adminMiddleware);
    $router->post('admin/users/delete', 'AdminController', 'deleteUser', $adminMiddleware);
    $router->post('admin/users/toggle-status', 'AdminController', 'toggleUserStatus', $adminMiddleware);
    $router->post('admin/users/reset-password', 'AdminController', 'resetPassword', $adminMiddleware);
    $router->post('admin/users/bulk-action', 'AdminController', 'bulkAction', $adminMiddleware);

    // Configuration routes
    $router->get('admin/config', 'ConfigController', 'index', $adminMiddleware);
    $router->post('admin/config/save', 'ConfigController', 'saveConfig', $adminMiddleware);
    
    $router->get('admin/users/create', 'AdminController', 'createUser', $adminMiddleware);
    $router->get('admin/users', 'AdminController', 'users', $adminMiddleware);
    $router->get('admin/backup', 'AdminController', 'backup', $adminMiddleware);

    // Admin profile routes (protette)
    $router->get('admin/profile/setup', 'AdminProfileController', 'setup', $authMiddleware);
    $router->post('admin/profile/setup', 'AdminProfileController', 'setup', $authMiddleware);
    error_log("Route profilo admin definite");

    // Home route (protetta)
    $router->get('', 'HomeController', 'index', $authMiddleware);
    error_log("Route home definita");

    // Dashboard route (protetta)
    $router->get('dashboard', 'DashboardController', 'index', $authMiddleware);
    error_log("Route dashboard definita");

    // Pratiche routes (protette)
    $router->get('pratiche', 'PraticheController', 'index', $authMiddleware);
    $router->get('pratiche/nuovo', 'PraticheController', 'nuovo', $authMiddleware);
    $router->post('pratiche/nuovo', 'PraticheController', 'nuovo', $authMiddleware);
    $router->get('pratiche/modifica/{id}', 'PraticheController', 'modifica', $authMiddleware);
    $router->post('pratiche/modifica/{id}', 'PraticheController', 'modifica', $authMiddleware);
    $router->get('pratiche/dettagli/{id}', 'PraticheController', 'dettagli', $authMiddleware);
    $router->get('pratiche/elimina/{id}', 'PraticheController', 'elimina', $authMiddleware);
    error_log("Route pratiche definite");

    // Progetti routes (protette)
    $router->get('progetti', 'ProgettiController', 'index', $authMiddleware);
    $router->get('progetti/nuovo', 'ProgettiController', 'nuovo', $authMiddleware);
    $router->post('progetti/nuovo', 'ProgettiController', 'nuovo', $authMiddleware);
    $router->get('progetti/modifica/{id}', 'ProgettiController', 'modifica', $authMiddleware);
    $router->post('progetti/modifica/{id}', 'ProgettiController', 'modifica', $authMiddleware);
    $router->get('progetti/dettagli/{id}', 'ProgettiController', 'dettagli', $authMiddleware);
    $router->get('progetti/elimina/{id}', 'ProgettiController', 'elimina', $authMiddleware);
    error_log("Route progetti definite");

    // Clienti routes (protette)
    $router->get('clienti', 'ClientiController', 'index', $authMiddleware);
    $router->get('clienti/nuovo', 'ClientiController', 'nuovo', $authMiddleware);
    $router->post('clienti/nuovo', 'ClientiController', 'nuovo', $authMiddleware);
    $router->get('clienti/modifica/{id}', 'ClientiController', 'modifica', $authMiddleware);
    $router->post('clienti/modifica/{id}', 'ClientiController', 'modifica', $authMiddleware);
    $router->get('clienti/dettagli/{id}', 'ClientiController', 'dettagli', $authMiddleware);
    $router->get('clienti/elimina/{id}', 'ClientiController', 'elimina', $authMiddleware);
    error_log("Route clienti definite");

    // Scadenze routes (protette)
    $router->get('scadenze', 'ScadenzeController', 'index', $authMiddleware);
    $router->get('scadenze/nuovo', 'ScadenzeController', 'nuovo', $authMiddleware);
    $router->post('scadenze/nuovo', 'ScadenzeController', 'nuovo', $authMiddleware);
    $router->get('scadenze/modifica/{id}', 'ScadenzeController', 'modifica', $authMiddleware);
    $router->post('scadenze/modifica/{id}', 'ScadenzeController', 'modifica', $authMiddleware);
    $router->get('scadenze/elimina/{id}', 'ScadenzeController', 'elimina', $authMiddleware);
    error_log("Route scadenze definite");

    // Notifiche routes (protette)
    $router->get('notifiche', 'NotificheController', 'index', $authMiddleware);
    $router->get('notifiche/getUltime', 'NotificheController', 'getUltime', $authMiddleware);
    $router->get('notifiche/getNotificheAjax', 'NotificheController', 'getNotificheAjax', $authMiddleware);
    $router->post('notifiche/markAsRead/{id}', 'NotificheController', 'markAsRead', $authMiddleware);
    $router->post('notifiche/markAllAsRead', 'NotificheController', 'markAllAsRead', $authMiddleware);
    $router->get('notifiche/preferenze', 'NotificheController', 'preferenze', $authMiddleware);
    $router->post('notifiche/preferenze', 'NotificheController', 'savePreferenze', $authMiddleware);
    error_log("Route notifiche definite");

    // Gestione 404
    $router->setNotFound(function() {
        header("HTTP/1.0 404 Not Found");
        echo '404 - Pagina non trovata';
    });

    // Ottieni l'URL corrente
    $url = trim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');
    // Rimuovi il prefisso della directory se presente
    $prefix = trim(parse_url(BASE_URL, PHP_URL_PATH), '/');
    if (!empty($prefix) && strpos($url, $prefix) === 0) {
        $url = substr($url, strlen($prefix));
    }
    $url = trim($url, '/');
    
    error_log("URL richiesto: " . $url);

    // Dispatch della route
    $result = $router->dispatch($url);
    error_log("Route dispatched");

    // Se il risultato è un array con una chiave 'view', carica la vista
    if (is_array($result) && isset($result['view'])) {
        extract($result);
        $viewPath = VIEWS_DIR . '/' . $result['view'] . '.php';
        if (file_exists($viewPath)) {
            require $viewPath;
        } else {
            throw new Exception("Vista non trovata: " . $viewPath);
        }
    }

} catch (Exception $e) {
    error_log($e->getMessage());
    error_log($e->getTraceAsString());
    echo "<h1>Si è verificato un errore</h1>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    if (defined('DEBUG') && DEBUG) {
        echo "<pre>";
        echo htmlspecialchars($e->getTraceAsString());
        echo "</pre>";
    }
}