<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/app/classes/BackupManager.php';

use App\Classes\BackupManager;

// CONTROLLO DI SICUREZZA - Solo admin possono accedere
if (!isset($_SESSION['user']) || !isset($_SESSION['user']['role']) || $_SESSION['user']['role'] !== 'admin') {
    http_response_code(403);
    header('Location: ' . BASE_URL . 'login');
    exit('Accesso negato. Solo gli amministratori possono accedere al sistema di backup.');
}

// Gestione del download
if (isset($_GET['download'])) {
    $file = $_GET['download'];
    $backupDir = __DIR__ . '/backups';
    $filePath = $backupDir . '/' . basename($file);
    
    if (file_exists($filePath) && strpos($filePath, '..') === false) {
        header('Content-Type: application/zip');
        header('Content-Disposition: attachment; filename="' . basename($filePath) . '"');
        header('Content-Length: ' . filesize($filePath));
        header('Cache-Control: no-cache');
        
        readfile($filePath);
        exit;
    }
}

// Gestione dell'eliminazione
if (isset($_POST['delete'])) {
    $file = $_POST['delete'];
    $backupDir = __DIR__ . '/backups';
    $filePath = $backupDir . '/' . basename($file);
    
    if (file_exists($filePath) && strpos($filePath, '..') === false) {
        if (unlink($filePath)) {
            header('Location: ' . $_SERVER['PHP_SELF'] . '?deleted=' . urlencode($file));
            exit;
        }
    }
}

// Eliminazione multipla
if (isset($_POST['delete_selected']) && isset($_POST['backups'])) {
    $backupDir = __DIR__ . '/backups';
    $deleted = 0;
    
    foreach ($_POST['backups'] as $file) {
        $filePath = $backupDir . '/' . basename($file);
        if (file_exists($filePath) && strpos($filePath, '..') === false) {
            if (unlink($filePath)) {
                $deleted++;
            }
        }
    }
    
    header('Location: ' . $_SERVER['PHP_SELF'] . '?deleted_count=' . $deleted);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_backup'])) {
    try {
        $backupManager = new BackupManager();
        $backupDir = __DIR__ . '/backups';
        $tempDir = sys_get_temp_dir() . '/studio_tecnico_backup_' . time();
        
        // Crea directory temporanea
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0777, true);
        }
        
        // Crea la cartella principale del progetto
        $projectDir = $tempDir . '/studio_tecnico';
        mkdir($projectDir, 0777, true);
        
        // Copia i file del progetto
        $backupManager->copyDirectory(__DIR__, $projectDir);
        
        // Backup del database fuori dalla cartella del progetto
        $dbConfig = require __DIR__ . '/config/database.php';
        $dbBackupFile = $tempDir . '/database_backup.sql';
        
        try {
            $dbSuccess = $backupManager->backupDatabase(
                $dbConfig['db']['host'],
                $dbConfig['db']['user'],
                $dbConfig['db']['pass'],
                $dbConfig['db']['dbname'],
                $dbBackupFile
            );
        } catch (\Exception $e) {
            error_log("Errore backup database: " . $e->getMessage());
            throw new \Exception("Errore durante il backup del database: " . $e->getMessage());
        }

        // Crea il file info.html con le specifiche del progetto
        $currentDate = date('d/m/Y H:i:s');
        $infoContent = "<!DOCTYPE html>
<html lang='it'>
<head>
    <meta charset='UTF-8'>
    <title>Informazioni Backup Studio Tecnico</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        h1 { color: #2c3e50; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        .info-box { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .warning { color: #e74c3c; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Backup Studio Tecnico</h1>
        <div class='info-box'>
            <h2>Informazioni Backup</h2>
            <p><strong>Data backup:</strong> {$currentDate}</p>
            <p><strong>Nome progetto:</strong> Studio Tecnico</p>
        </div>
        
        <div class='info-box'>
            <h2>Configurazione Database</h2>
            <p><strong>Host:</strong> {$dbConfig['db']['host']}</p>
            <p><strong>Database:</strong> {$dbConfig['db']['dbname']}</p>
            <p><strong>Utente:</strong> {$dbConfig['db']['user']}</p>
            <p class='warning'>Nota: Per motivi di sicurezza, la password non viene mostrata.</p>
        </div>

        <div class='info-box'>
            <h2>Struttura Backup</h2>
            <ul>
                <li><strong>studio_tecnico/</strong> - Cartella principale del progetto</li>
                <li><strong>database_backup.sql</strong> - Backup completo del database</li>
                <li><strong>info.html</strong> - Questo file di informazioni</li>
            </ul>
        </div>

        <div class='info-box'>
            <h2>Istruzioni di Ripristino</h2>
            <ol>
                <li>Copiare il contenuto della cartella 'studio_tecnico' nella directory di destinazione</li>
                <li>Importare il file 'database_backup.sql' nel database MySQL</li>
                <li>Verificare le configurazioni nel file config/database.php</li>
                <li>Assicurarsi che il server web abbia i permessi corretti sulle cartelle</li>
            </ol>
        </div>
    </div>
</body>
</html>";
        
        file_put_contents($tempDir . '/info.html', $infoContent);
        
        // Crea il file ZIP finale
        $zipName = $backupManager->generateBackupFilename('backup_completo');
        $destination = $backupDir . '/' . $zipName . '.zip';
        
        $backupManager->prepareBackupDirectory($backupDir);
        $success = $backupManager->createZipArchive($tempDir, $destination);
        
        // Pulisci la directory temporanea
        if (file_exists($tempDir)) {
            removeDirectory($tempDir);
        }
        
        if ($success) {
            echo '<div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> Backup completato con successo!<br>
                    File: ' . basename($destination) . '<br>
                    Dimensione: ' . number_format(filesize($destination) / (1024 * 1024), 2) . ' MB<br>
                    <a href="?download=' . basename($destination) . '" class="btn btn-primary btn-sm mt-2">
                        <i class="fas fa-download"></i> Scarica Backup
                    </a>
                  </div>';
        } else {
            throw new Exception("Errore durante la creazione del backup");
        }
    } catch (Exception $e) {
        // Assicurati di pulire la directory temporanea in caso di errore
        if (isset($tempDir) && file_exists($tempDir)) {
            removeDirectory($tempDir);
        }
        echo '<div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> ' . htmlspecialchars($e->getMessage()) . '
              </div>';
        error_log("Errore backup completo: " . $e->getMessage());
    }
}

// Funzione per rimuovere directory ricorsivamente
function removeDirectory($dir) {
    if (!file_exists($dir)) {
        return true;
    }
    if (!is_dir($dir)) {
        return unlink($dir);
    }
    foreach (scandir($dir) as $item) {
        if ($item == '.' || $item == '..') {
            continue;
        }
        if (!removeDirectory($dir . DIRECTORY_SEPARATOR . $item)) {
            return false;
        }
    }
    return rmdir($dir);
}

// Interfaccia Web
?>
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backup Completo - Studio Tecnico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        .backup-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .backup-actions {
            display: flex;
            gap: 10px;
        }
        .backup-info {
            flex-grow: 1;
        }
        .floating-delete {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="backup-container bg-light">
            <h2 class="text-center mb-4">Backup Completo Sistema</h2>
            
            <?php
            // Mostra messaggi di successo
            if (isset($_GET['deleted'])) {
                echo '<div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Backup ' . htmlspecialchars($_GET['deleted']) . ' eliminato con successo!
                      </div>';
            }
            if (isset($_GET['deleted_count'])) {
                $count = (int)$_GET['deleted_count'];
                echo '<div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> ' . $count . ' backup eliminati con successo!
                      </div>';
            }
            
            ?>
            
            <form method="POST" class="needs-validation" novalidate>
                <div class="mb-3">
                    <button type="submit" name="create_backup" class="btn btn-primary w-100">
                        <i class="fas fa-save me-2"></i>Crea Nuovo Backup
                    </button>
                </div>
            </form>
            
            <?php
            // Mostra lista dei backup esistenti
            $backupDir = __DIR__ . '/backups';
            if (is_dir($backupDir)) {
                $backups = glob($backupDir . '/*.zip');
                if (!empty($backups)) {
                    echo '<form method="POST" id="backupsForm">';
                    echo '<div class="d-flex justify-content-between align-items-center mb-3">';
                    echo '<h4 class="mb-0">Backup Disponibili</h4>';
                    echo '<div class="form-check">';
                    echo '<input type="checkbox" class="form-check-input" id="selectAll">';
                    echo '<label class="form-check-label" for="selectAll">Seleziona Tutti</label>';
                    echo '</div>';
                    echo '</div>';
                    
                    echo '<div class="list-group">';
                    foreach ($backups as $backup) {
                        $size = filesize($backup);
                        $date = date('d/m/Y H:i', filemtime($backup));
                        echo '<div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="form-check me-3">
                                            <input type="checkbox" class="form-check-input backup-checkbox" 
                                                   name="backups[]" value="' . basename($backup) . '">
                                        </div>
                                        <div class="backup-info">
                                            <h6 class="mb-1">
                                                <i class="fas fa-file-archive text-primary me-2"></i>
                                                ' . basename($backup) . '
                                            </h6>
                                            <small class="text-muted">
                                                Creato il: ' . $date . ' - 
                                                Dimensione: ' . number_format($size / (1024 * 1024), 2) . ' MB
                                            </small>
                                        </div>
                                    </div>
                                    <div class="backup-actions">
                                        <a href="?download=' . basename($backup) . '" 
                                           class="btn btn-primary btn-sm" 
                                           title="Scarica questo backup">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <button type="submit" 
                                                name="delete" 
                                                value="' . basename($backup) . '" 
                                                class="btn btn-danger btn-sm"
                                                onclick="return confirm(\'Sei sicuro di voler eliminare questo backup?\')"
                                                title="Elimina questo backup">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                              </div>';
                    }
                    echo '</div>';
                    
                    // Pulsante elimina multiplo flottante
                    echo '<div class="floating-delete" id="floatingDelete">
                            <button type="submit" 
                                    name="delete_selected" 
                                    class="btn btn-danger"
                                    onclick="return confirm(\'Sei sicuro di voler eliminare i backup selezionati?\')">
                                <i class="fas fa-trash me-2"></i>
                                Elimina Selezionati (<span id="selectedCount">0</span>)
                            </button>
                          </div>';
                    
                    echo '</form>';
                }
            }
            ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.backup-checkbox');
        const floatingDelete = document.getElementById('floatingDelete');
        const selectedCount = document.getElementById('selectedCount');
        
        function updateSelectedCount() {
            const count = document.querySelectorAll('.backup-checkbox:checked').length;
            selectedCount.textContent = count;
            floatingDelete.style.display = count > 0 ? 'block' : 'none';
        }
        
        if (selectAll) {
            selectAll.addEventListener('change', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateSelectedCount();
            });
        }
        
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedCount);
        });
    });
    </script>
</body>
</html>
