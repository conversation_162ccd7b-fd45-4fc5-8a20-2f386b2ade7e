    <?php
$pageTitle = 'Modifica Progetto';
include VIEWS_DIR . '/layouts/header.php';
?>
<link rel="stylesheet" href="<?= BASE_URL ?>assets/css/management.css">

<?php
require_once dirname(dirname(__DIR__)) . '/config/config.php';
use App\Core\Security;
?>

<div class="container-fluid py-4">
    <div class="page-header">
        <h2 class="page-title">
            <i class="fas fa-edit me-2"></i>
            Modifica Progetto
        </h2>
        <div class="page-actions">
            <a href="<?= BASE_URL ?>progetti" class="btn btn-outline-neutral">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <?php if (!empty($errori)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i>
            <ul class="mb-0">
                <?php foreach ($errori as $errore): ?>
                    <li><?php echo htmlspecialchars($errore); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="content-card">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-project-diagram me-2"></i> Dettagli Progetto
                </h5>
            </div>
            <div class="card-body">
                <form action="<?php echo BASE_URL; ?>progetti/modifica/<?php echo htmlspecialchars($progetto['id']); ?>" method="POST" id="progettoForm">
                    <?php echo Security::csrfField(); ?>
                    <input type="hidden" name="id" value="<?php echo htmlspecialchars($progetto['id']); ?>">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nome_progetto" class="form-label">Nome Progetto *</label>
                            <input type="text" class="form-control" id="nome_progetto" name="nome_progetto" 
                                   value="<?php echo isset($progetto['nome_progetto']) ? htmlspecialchars($progetto['nome_progetto']) : ''; ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="cliente_id" class="form-label">Cliente *</label>
                            <select class="form-select" id="cliente_id" name="cliente_id" required>
                                <option value="">Seleziona Cliente</option>
                                <?php foreach ($clienti as $cliente): ?>
                                    <option value="<?php echo htmlspecialchars($cliente['id']); ?>" 
                                        <?php echo isset($progetto['cliente_id']) && $progetto['cliente_id'] == $cliente['id'] ? 'selected' : ''; ?>>
                                        <?php 
                                        if ($cliente['tipo_cliente'] === 'privato') {
                                            echo htmlspecialchars($cliente['nome'] . ' ' . $cliente['cognome']);
                                        } else {
                                            echo htmlspecialchars($cliente['ragione_sociale']);
                                        }
                                        ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tipo_progetto" class="form-label">Tipo Progetto *</label>
                            <select class="form-select" id="tipo_progetto" name="tipo_progetto" required>
                                <option value="">Seleziona Tipo</option>
                                <option value="architettonico" <?php echo isset($progetto['tipo_progetto']) && $progetto['tipo_progetto'] === 'architettonico' ? 'selected' : ''; ?>>Architettonico</option>
                                <option value="strutturale" <?php echo isset($progetto['tipo_progetto']) && $progetto['tipo_progetto'] === 'strutturale' ? 'selected' : ''; ?>>Strutturale</option>
                                <option value="impiantistico" <?php echo isset($progetto['tipo_progetto']) && $progetto['tipo_progetto'] === 'impiantistico' ? 'selected' : ''; ?>>Impiantistico</option>
                                <option value="urbanistico" <?php echo isset($progetto['tipo_progetto']) && $progetto['tipo_progetto'] === 'urbanistico' ? 'selected' : ''; ?>>Urbanistico</option>
                                <option value="altro" <?php echo isset($progetto['tipo_progetto']) && $progetto['tipo_progetto'] === 'altro' ? 'selected' : ''; ?>>Altro</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="stato_progetto" class="form-label">Stato Progetto *</label>
                            <select class="form-select" id="stato_progetto" name="stato_progetto" required>
                                <option value="">Seleziona Stato</option>
                                <option value="in_corso" <?php echo isset($progetto['stato_progetto']) && $progetto['stato_progetto'] === 'in_corso' ? 'selected' : ''; ?>>In Corso</option>
                                <option value="completato" <?php echo isset($progetto['stato_progetto']) && $progetto['stato_progetto'] === 'completato' ? 'selected' : ''; ?>>Completato</option>
                                <option value="sospeso" <?php echo isset($progetto['stato_progetto']) && $progetto['stato_progetto'] === 'sospeso' ? 'selected' : ''; ?>>Sospeso</option>
                                <option value="annullato" <?php echo isset($progetto['stato_progetto']) && $progetto['stato_progetto'] === 'annullato' ? 'selected' : ''; ?>>Annullato</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="data_inizio" class="form-label">Data Inizio *</label>
                            <input type="date" class="form-control" id="data_inizio" name="data_inizio" 
                                   value="<?php echo isset($progetto['data_inizio']) ? htmlspecialchars($progetto['data_inizio']) : ''; ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="data_fine_prevista" class="form-label">Data Fine Prevista</label>
                            <input type="date" class="form-control" id="data_fine_prevista" name="data_fine_prevista" 
                                   value="<?php echo isset($progetto['data_fine_prevista']) ? htmlspecialchars($progetto['data_fine_prevista']) : ''; ?>">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="indirizzo" class="form-label">Indirizzo *</label>
                        <input type="text" class="form-control" id="indirizzo" name="indirizzo" 
                               value="<?php echo isset($progetto['indirizzo']) ? htmlspecialchars($progetto['indirizzo']) : ''; ?>" required>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="cap" class="form-label">CAP *</label>
                            <input type="text" class="form-control" id="cap" name="cap" 
                                   value="<?php echo isset($progetto['cap']) ? htmlspecialchars($progetto['cap']) : ''; ?>" 
                                   pattern="^[0-9]{5}$" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="citta" class="form-label">Città *</label>
                            <input type="text" class="form-control" id="citta" name="citta" 
                                   value="<?php echo isset($progetto['citta']) ? htmlspecialchars($progetto['citta']) : ''; ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="provincia" class="form-label">Provincia *</label>
                            <input type="text" class="form-control" id="provincia" name="provincia" 
                                   value="<?php echo isset($progetto['provincia']) ? htmlspecialchars($progetto['provincia']) : ''; ?>" 
                                   maxlength="2" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="descrizione" class="form-label">Descrizione</label>
                        <textarea class="form-control" id="descrizione" name="descrizione" rows="3"><?php echo isset($progetto['descrizione']) ? htmlspecialchars($progetto['descrizione']) : ''; ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="note" class="form-label">Note</label>
                        <textarea class="form-control" id="note" name="note" rows="3"><?php echo isset($progetto['note']) ? htmlspecialchars($progetto['note']) : ''; ?></textarea>
                    </div>

                    <div class="text-end mt-4">
                        <button type="submit" class="btn btn-neutral">
                            <i class="fas fa-save"></i> Salva Modifiche
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('progettoForm');
    const dataInizio = document.getElementById('data_inizio');
    const dataFinePrevista = document.getElementById('data_fine_prevista');
    const dataFine = document.getElementById('data_fine');
    
    form.addEventListener('submit', function(e) {
        if (dataFinePrevista.value && dataFinePrevista.value < dataInizio.value) {
            e.preventDefault();
            alert('La data di fine prevista non può essere precedente alla data di inizio');
        }
        if (dataFine.value && dataFine.value < dataInizio.value) {
            e.preventDefault();
            alert('La data di fine effettiva non può essere precedente alla data di inizio');
        }
    });
});
</script>

<?php require_once ROOT_PATH . '/includes/footer.php'; ?>