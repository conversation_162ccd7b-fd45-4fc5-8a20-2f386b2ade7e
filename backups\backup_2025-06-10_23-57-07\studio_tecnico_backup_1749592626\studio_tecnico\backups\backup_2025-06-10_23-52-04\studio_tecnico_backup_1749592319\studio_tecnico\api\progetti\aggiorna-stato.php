<?php
require_once '../config.php';
requirePost();

try {
    if (empty($_POST['id']) || empty($_POST['stato'])) {
        sendError('ID e stato sono obbligatori');
    }

    $stati_validi = ['in_corso', 'completato', 'sospeso'];
    if (!in_array($_POST['stato'], $stati_validi)) {
        sendError('Stato non valido');
    }

    $stmt = $conn->prepare("UPDATE progetti SET stato = ? WHERE id = ?");
    $stmt->execute([$_POST['stato'], $_POST['id']]);

    // Recupera i dati aggiornati del progetto
    $stmt = $conn->prepare("SELECT * FROM progetti WHERE id = ?");
    $stmt->execute([$_POST['id']]);
    $progetto = $stmt->fetch(PDO::FETCH_ASSOC);

    sendResponse([
        'success' => true,
        'message' => 'Stato aggiornato con successo',
        'data' => $progetto
    ]);
} catch (PDOException $e) {
    sendError('Errore nell\'aggiornamento dello stato: ' . $e->getMessage());
} 