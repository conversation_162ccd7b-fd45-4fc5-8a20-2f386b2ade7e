<?php 
use App\Core\Security;
include VIEWS_DIR . '/layouts/header.php'; ?>
<link rel="stylesheet" href="<?= BASE_URL ?>assets/css/management.css">

<div class="container-fluid py-4">
    <div class="page-header">
        <h2 class="page-title">
            <i class="fas fa-user-edit"></i>
            Modifica Cliente
        </h2>
        <div class="page-actions">
            <a href="<?= BASE_URL ?>clienti" class="btn btn-outline-neutral">
                <i class="fas fa-arrow-left"></i>
                Torna alla lista
            </a>
        </div>
    </div>

    <?php if (!empty($errori)): ?>
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">Attenzione!</h4>
            <p>Si sono verificati i seguenti errori:</p>
            <hr>
            <ul class="mb-0">
                <?php foreach ($errori as $campo => $messaggio): ?>
                    <?php if (is_array($messaggio)): // Se ci sono più errori per un campo ?>
                        <?php foreach ($messaggio as $msg): ?>
                            <li><?php echo htmlspecialchars($msg); ?></li>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <li><?php echo htmlspecialchars($messaggio); ?></li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="form-card">
                <form action="<?php echo BASE_URL; ?>clienti/modifica/<?php echo htmlspecialchars($cliente['id']); ?>" method="POST" id="clienteForm">
                    <?php echo Security::csrfField(); ?>
                    <input type="hidden" name="id" value="<?php echo htmlspecialchars($cliente['id']); ?>">

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-id-card"></i>
                            Tipo Cliente
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-12">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="tipo_cliente" id="tipo_privato" 
                                           value="privato" <?php echo isset($cliente['tipo_cliente']) && $cliente['tipo_cliente'] === 'privato' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="tipo_privato">Privato</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="tipo_cliente" id="tipo_azienda" 
                                           value="azienda" <?php echo isset($cliente['tipo_cliente']) && $cliente['tipo_cliente'] === 'azienda' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="tipo_azienda">Azienda</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="dati_privato" class="form-section" <?php echo isset($cliente['tipo_cliente']) && $cliente['tipo_cliente'] === 'azienda' ? 'style="display: none;"' : ''; ?>>
                        <h3 class="form-section-title">
                            <i class="fas fa-user"></i>
                            Dati Personali
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="nome" class="form-label">Nome *</label>
                                <input type="text" class="form-control" id="nome" name="nome" 
                                       value="<?php echo isset($cliente['nome']) ? htmlspecialchars($cliente['nome']) : ''; ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="cognome" class="form-label">Cognome *</label>
                                <input type="text" class="form-control" id="cognome" name="cognome" 
                                       value="<?php echo isset($cliente['cognome']) ? htmlspecialchars($cliente['cognome']) : ''; ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="codice_fiscale" class="form-label">Codice Fiscale *</label>
                                <input type="text" class="form-control" id="codice_fiscale" name="codice_fiscale" 
                                       value="<?php echo isset($cliente['codice_fiscale']) ? htmlspecialchars($cliente['codice_fiscale']) : ''; ?>" required>
                            </div>
                        </div>
                    </div>

                    <div id="dati_azienda" class="form-section" <?php echo isset($cliente['tipo_cliente']) && $cliente['tipo_cliente'] === 'privato' ? 'style="display: none;"' : ''; ?>>
                        <h3 class="form-section-title">
                            <i class="fas fa-building"></i>
                            Dati Aziendali
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="ragione_sociale" class="form-label">Ragione Sociale *</label>
                                <input type="text" class="form-control" id="ragione_sociale" name="ragione_sociale" 
                                       value="<?php echo isset($cliente['ragione_sociale']) ? htmlspecialchars($cliente['ragione_sociale']) : ''; ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="partita_iva" class="form-label">Partita IVA *</label>
                                <input type="text" class="form-control" id="partita_iva" name="partita_iva" 
                                       value="<?php echo isset($cliente['partita_iva']) ? htmlspecialchars($cliente['partita_iva']) : ''; ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-address-book"></i>
                            Contatti
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo isset($cliente['email']) ? htmlspecialchars($cliente['email']) : ''; ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="telefono" class="form-label">Telefono *</label>
                                <input type="tel" class="form-control" id="telefono" name="telefono" 
                                       value="<?php echo isset($cliente['telefono']) ? htmlspecialchars($cliente['telefono']) : ''; ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-map-marker-alt"></i>
                            Indirizzo
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="indirizzo" class="form-label">Indirizzo *</label>
                                <input type="text" class="form-control" id="indirizzo" name="indirizzo" 
                                       value="<?php echo isset($cliente['indirizzo']) ? htmlspecialchars($cliente['indirizzo']) : ''; ?>" required>
                            </div>
                            <div class="col-md-4">
                                <label for="citta" class="form-label">Città *</label>
                                <input type="text" class="form-control" id="citta" name="citta" 
                                       value="<?php echo isset($cliente['citta']) ? htmlspecialchars($cliente['citta']) : ''; ?>" required>
                            </div>
                            <div class="col-md-4">
                                <label for="provincia" class="form-label">Provincia *</label>
                                <input type="text" class="form-control" id="provincia" name="provincia" 
                                       value="<?php echo isset($cliente['provincia']) ? htmlspecialchars($cliente['provincia']) : ''; ?>" required maxlength="2">
                            </div>
                            <div class="col-md-4">
                                <label for="cap" class="form-label">CAP *</label>
                                <input type="text" class="form-control" id="cap" name="cap" 
                                       value="<?php echo isset($cliente['cap']) ? htmlspecialchars($cliente['cap']) : ''; ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-sticky-note"></i>
                            Note Aggiuntive
                        </h3>
                        <div class="row g-3">
                            <div class="col-12">
                                <label for="note" class="form-label">Note</label>
                                <textarea class="form-control" id="note" name="note" rows="3"><?php echo isset($cliente['note']) ? htmlspecialchars($cliente['note']) : ''; ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="text-end mt-4">
                        <button type="submit" class="btn btn-neutral">
                            <i class="fas fa-save"></i>
                            Salva Modifiche
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tipoPrivato = document.getElementById('tipo_privato');
    const tipoAzienda = document.getElementById('tipo_azienda');
    const datiPrivato = document.getElementById('dati_privato');
    const datiAzienda = document.getElementById('dati_azienda');
    const nomeInput = document.getElementById('nome');
    const cognomeInput = document.getElementById('cognome');
    const codiceFiscaleInput = document.getElementById('codice_fiscale');
    const ragioneSocialeInput = document.getElementById('ragione_sociale');
    const partitaIvaInput = document.getElementById('partita_iva');

    function toggleFields() {
        if (tipoPrivato.checked) {
            datiPrivato.style.display = 'block';
            datiAzienda.style.display = 'none';
            nomeInput.required = true;
            cognomeInput.required = true;
            codiceFiscaleInput.required = true;
            ragioneSocialeInput.required = false;
            partitaIvaInput.required = false;
        } else {
            datiPrivato.style.display = 'none';
            datiAzienda.style.display = 'block';
            nomeInput.required = false;
            cognomeInput.required = false;
            codiceFiscaleInput.required = false;
            ragioneSocialeInput.required = true;
            partitaIvaInput.required = true;
        }
    }

    tipoPrivato.addEventListener('change', toggleFields);
    tipoAzienda.addEventListener('change', toggleFields);
});
</script>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>
