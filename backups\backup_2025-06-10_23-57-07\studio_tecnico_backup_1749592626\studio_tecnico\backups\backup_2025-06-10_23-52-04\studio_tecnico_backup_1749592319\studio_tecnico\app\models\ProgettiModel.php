<?php
// app/models/ProgettiModel.php - Model per la gestione dei progetti
namespace App\Models;

use App\Config\Database;
use PDO;
use PDOException;

class ProgettiModel {
    private PDO $db;

    public function __construct() {
        try {
            $this->db = Database::getInstance();
            if (!$this->db instanceof PDO) {
                throw new PDOException("Connessione al database non valida nel ProgettiModel.");
            }
        } catch (PDOException $e) {
            error_log("Errore connessione DB in ProgettiModel: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Recupera tutti i progetti con informazioni cliente e statistiche
     */
    public function getAllProgetti(array $filters = []): array {
        try {
            $query = "
                SELECT p.*, 
                       c.tipo_cliente,
                       c.nome as cliente_nome,
                       c.cognome as cliente_cognome,
                       c.ragione_sociale as cliente_ragione_sociale,
                       COUNT(DISTINCT pr.id) as num_pratiche,
                       COUNT(DISTINCT s.id) as num_scadenze
                FROM progetti p 
                LEFT JOIN clienti c ON p.cliente_id = c.id 
                LEFT JOIN pratiche pr ON p.id = pr.progetto_id
                LEFT JOIN scadenze s ON p.id = s.progetto_id
            ";

            $whereConditions = [];
            $params = [];

            // Filtri opzionali
            if (!empty($filters['stato'])) {
                $whereConditions[] = "p.stato = :stato";
                $params[':stato'] = $filters['stato'];
            }

            if (!empty($filters['cliente_id'])) {
                $whereConditions[] = "p.cliente_id = :cliente_id";
                $params[':cliente_id'] = $filters['cliente_id'];
            }

            if (!empty($filters['tipo_progetto'])) {
                $whereConditions[] = "p.tipo_progetto = :tipo_progetto";
                $params[':tipo_progetto'] = $filters['tipo_progetto'];
            }

            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(" AND ", $whereConditions);
            }

            $query .= " GROUP BY p.id ORDER BY p.data_inizio DESC";

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            
            if ($stmt === false) {
                throw new PDOException("Errore nell'esecuzione della query per getAllProgetti");
            }
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in ProgettiModel::getAllProgetti(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Recupera un progetto specifico per ID con informazioni cliente
     */
    public function getProgettoById(int $id): ?array {
        try {
            $query = "
                SELECT p.*, 
                       c.nome as cliente_nome,
                       c.cognome as cliente_cognome,
                       c.ragione_sociale as cliente_ragione_sociale,
                       c.tipo_cliente,
                       c.email as cliente_email,
                       c.telefono as cliente_telefono
                FROM progetti p
                LEFT JOIN clienti c ON p.cliente_id = c.id
                WHERE p.id = :id
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([':id' => $id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ?: null;

        } catch (PDOException $e) {
            error_log("Errore in ProgettiModel::getProgettoById(): " . $e->getMessage());
            return null;
        }
    }

    /**
     * Inserisce un nuovo progetto
     */
    public function insertProgetto(array $data): bool {
        try {
            error_log("ProgettiModel::insertProgetto() - Dati ricevuti: " . print_r($data, true));
            
            $query = "INSERT INTO progetti (
                cliente_id, nome_progetto, descrizione, data_inizio, 
                data_fine_prevista, stato, importo, comune, 
                indirizzo_progetto, tipo_progetto, priorita
            ) VALUES (
                :cliente_id, :nome_progetto, :descrizione, :data_inizio,
                :data_fine_prevista, :stato, :importo, :comune,
                :indirizzo_progetto, :tipo_progetto, :priorita
            )";

            $stmt = $this->db->prepare($query);
            $allowed_keys = [
                'cliente_id', 'nome_progetto', 'descrizione', 'data_inizio',
                'data_fine_prevista', 'stato', 'importo', 'comune',
                'indirizzo_progetto', 'tipo_progetto', 'priorita'
            ];
            
            $filtered_data = array_intersect_key($data, array_flip($allowed_keys));
            
            // Gestione valori null per importo
            if (isset($filtered_data['importo']) && empty($filtered_data['importo'])) {
                $filtered_data['importo'] = null;
            }
            
            error_log("ProgettiModel::insertProgetto() - Dati filtrati per execute: " . print_r($filtered_data, true));
            
            $success = $stmt->execute($filtered_data);
            if (!$success) {
                error_log("ProgettiModel::insertProgetto() - Errore SQL: " . print_r($stmt->errorInfo(), true));
            }
            
            error_log("ProgettiModel::insertProgetto() - Esito: " . ($success ? 'Successo' : 'Fallimento'));
            return $success;

        } catch (PDOException $e) {
            error_log("Errore in ProgettiModel::insertProgetto(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Aggiorna un progetto esistente
     */
    public function updateProgetto(array $data): bool {
        try {
            error_log("ProgettiModel::updateProgetto() - Dati ricevuti: " . print_r($data, true));
            
            if (!isset($data['id'])) {
                error_log("ID progetto mancante per l'aggiornamento in ProgettiModel");
                return false;
            }

            $query = "UPDATE progetti SET 
                cliente_id = :cliente_id,
                nome_progetto = :nome_progetto,
                descrizione = :descrizione,
                data_inizio = :data_inizio,
                data_fine_prevista = :data_fine_prevista,
                stato = :stato,
                importo = :importo,
                comune = :comune,
                indirizzo_progetto = :indirizzo_progetto,
                tipo_progetto = :tipo_progetto,
                priorita = :priorita
                WHERE id = :id";

            $stmt = $this->db->prepare($query);
            $allowed_keys = [
                'cliente_id', 'nome_progetto', 'descrizione', 'data_inizio',
                'data_fine_prevista', 'stato', 'importo', 'comune',
                'indirizzo_progetto', 'tipo_progetto', 'priorita', 'id'
            ];
            
            $filtered_data = array_intersect_key($data, array_flip($allowed_keys));
            
            // Gestione valori null per importo
            if (isset($filtered_data['importo']) && empty($filtered_data['importo'])) {
                $filtered_data['importo'] = null;
            }
            
            error_log("ProgettiModel::updateProgetto() - Dati filtrati per execute: " . print_r($filtered_data, true));
            
            $success = $stmt->execute($filtered_data);
            if (!$success) {
                error_log("ProgettiModel::updateProgetto() - Errore SQL: " . print_r($stmt->errorInfo(), true));
            }
            
            error_log("ProgettiModel::updateProgetto() - Esito: " . ($success ? 'Successo' : 'Fallimento'));
            return $success;

        } catch (PDOException $e) {
            error_log("Errore in ProgettiModel::updateProgetto(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Elimina un progetto (solo se non ha pratiche collegate)
     */
    public function deleteProgetto(int $id): bool {
        try {
            // Controlla se esistono pratiche collegate
            $stmt = $this->db->prepare("SELECT COUNT(*) as num_pratiche FROM pratiche WHERE progetto_id = :id");
            $stmt->execute([':id' => $id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result['num_pratiche'] > 0) {
                error_log("ProgettiModel::deleteProgetto() - Impossibile eliminare progetto con pratiche collegate");
                return false;
            }

            // Elimina il progetto
            $stmt = $this->db->prepare("DELETE FROM progetti WHERE id = :id");
            $success = $stmt->execute([':id' => $id]);
            
            error_log("ProgettiModel::deleteProgetto() - Esito: " . ($success ? 'Successo' : 'Fallimento'));
            return $success;

        } catch (PDOException $e) {
            error_log("Errore in ProgettiModel::deleteProgetto(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Recupera progetti per cliente specifico
     */
    public function getProgettiByClienteId(int $cliente_id): array {
        try {
            $query = "
                SELECT 
                    p.*,
                    COUNT(DISTINCT pr.id) as num_pratiche,
                    COUNT(DISTINCT s.id) as num_scadenze
                FROM progetti p 
                LEFT JOIN pratiche pr ON p.id = pr.progetto_id
                LEFT JOIN scadenze s ON p.id = s.progetto_id
                WHERE p.cliente_id = :cliente_id 
                GROUP BY p.id
                ORDER BY p.data_inizio DESC";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([':cliente_id' => $cliente_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in ProgettiModel::getProgettiByClienteId(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Recupera progetti per stato
     */
    public function getProgettiByStato(string $stato): array {
        try {
            return $this->getAllProgetti(['stato' => $stato]);
        } catch (Exception $e) {
            error_log("Errore in ProgettiModel::getProgettiByStato(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Recupera statistiche sui progetti
     */
    public function getStatisticheProgetti(): array {
        try {
            $stats = [];
            
            // Progetti per stato
            $stmt = $this->db->query("
                SELECT stato, COUNT(*) as count 
                FROM progetti 
                GROUP BY stato
            ");
            $stats['per_stato'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            // Progetti per tipo
            $stmt = $this->db->query("
                SELECT tipo_progetto, COUNT(*) as count 
                FROM progetti 
                GROUP BY tipo_progetto
            ");
            $stats['per_tipo'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            // Valore totale progetti
            $stmt = $this->db->query("
                SELECT 
                    COUNT(*) as totale_progetti,
                    SUM(importo) as totale_valore,
                    AVG(importo) as valore_medio
                FROM progetti 
                WHERE importo IS NOT NULL
            ");
            $stats['valori'] = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Progetti per priorità
            $stmt = $this->db->query("
                SELECT priorita, COUNT(*) as count 
                FROM progetti 
                GROUP BY priorita
            ");
            $stats['per_priorita'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            return $stats;

        } catch (PDOException $e) {
            error_log("Errore in ProgettiModel::getStatisticheProgetti(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Conta il numero di pratiche per un progetto
     */
    public function countPraticheByProgettoId(int $progetto_id): int {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) as num_pratiche FROM pratiche WHERE progetto_id = :progetto_id");
            $stmt->execute([':progetto_id' => $progetto_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? (int)$result['num_pratiche'] : 0;
        } catch (PDOException $e) {
            error_log("Errore in ProgettiModel::countPraticheByProgettoId(): " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Verifica se un progetto può essere eliminato (non ha pratiche collegate)
     */
    public function canDeleteProgetto(int $id): bool {
        return $this->countPraticheByProgettoId($id) === 0;
    }
}
?>
