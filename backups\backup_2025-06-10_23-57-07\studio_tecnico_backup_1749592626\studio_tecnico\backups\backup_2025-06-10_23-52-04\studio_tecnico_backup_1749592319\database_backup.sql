-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.4.32-MariaD<PERSON>, for Win64 (AMD64)
--
-- Host: localhost    Database: studio_tecnico
-- ------------------------------------------------------
-- Server version	10.4.32-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admin_profile`
--

DROP TABLE IF EXISTS `admin_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_profile` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `nome` varchar(100) NOT NULL,
  `cognome` varchar(100) NOT NULL,
  `codice_fiscale` varchar(16) NOT NULL,
  `partita_iva` varchar(11) NOT NULL,
  `indirizzo` varchar(255) NOT NULL,
  `citta` varchar(100) NOT NULL,
  `cap` varchar(5) NOT NULL,
  `provincia` varchar(2) NOT NULL,
  `telefono` varchar(20) NOT NULL,
  `email` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  CONSTRAINT `admin_profile_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_profile`
--

LOCK TABLES `admin_profile` WRITE;
/*!40000 ALTER TABLE `admin_profile` DISABLE KEYS */;
INSERT INTO `admin_profile` VALUES (2,5,'Mauro','Mazzarelli','MZZMRA72E11HF','08817481008','via della Stazione di Ciampino 151','Roma','00118','RM','3392720049','<EMAIL>','2024-12-12 22:00:35','2024-12-12 22:00:35');
/*!40000 ALTER TABLE `admin_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `clienti`
--

DROP TABLE IF EXISTS `clienti`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clienti` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) DEFAULT NULL,
  `cognome` varchar(255) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `telefono` varchar(20) DEFAULT NULL,
  `indirizzo` text DEFAULT NULL,
  `cap_cliente` varchar(10) DEFAULT NULL,
  `data_registrazione` timestamp NOT NULL DEFAULT current_timestamp(),
  `tipo_cliente` enum('privato','societa') NOT NULL DEFAULT 'privato',
  `ragione_sociale` varchar(200) DEFAULT NULL,
  `partita_iva` varchar(20) DEFAULT NULL,
  `codice_fiscale` varchar(16) DEFAULT NULL,
  `pec` varchar(100) DEFAULT NULL,
  `citta` varchar(100) DEFAULT NULL,
  `provincia` varchar(2) DEFAULT NULL,
  `note` text DEFAULT NULL,
  `attivo` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `clienti`
--

LOCK TABLES `clienti` WRITE;
/*!40000 ALTER TABLE `clienti` DISABLE KEYS */;
INSERT INTO `clienti` VALUES (1,'Mauro','Mazzarelli','<EMAIL>','3392720049','via della Stazione di Ciampino 151','00118','2024-12-08 07:04:27','privato','','','****************','<EMAIL>','Roma','RM','sono io, ciao',1),(2,'Roberto','Di Nunzio','<EMAIL>','3357421632','via Ajani 33','00044','2024-12-08 15:45:59','privato','','','****************','<EMAIL>','Roma','RM','',1),(4,'Ciccio','Baciccio','<EMAIL>','0600000000','via roma 17',NULL,'2025-05-24 17:19:38','privato',NULL,NULL,'XXXXXX00X00X000X',NULL,'frascati','RM','',1),(6,'Ciccio',NULL,'<EMAIL>','0600000001','via roma 17',NULL,'2025-05-24 20:24:56','','sers srl','12345678901',NULL,NULL,'frascati','RM','',1);
/*!40000 ALTER TABLE `clienti` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifiche`
--

DROP TABLE IF EXISTS `notifiche`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifiche` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `tipo` enum('scadenza','pratica','progetto','sistema','fattura','cliente','documento') NOT NULL,
  `titolo` varchar(255) NOT NULL,
  `messaggio` text NOT NULL,
  `priorita` enum('bassa','media','alta') DEFAULT 'media',
  `link_azione` varchar(255) DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `letta` tinyint(1) DEFAULT 0,
  `data_creazione` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_lettura` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_letta` (`user_id`,`letta`),
  KEY `idx_data_creazione` (`data_creazione`),
  KEY `idx_tipo` (`tipo`),
  KEY `idx_priorita` (`priorita`),
  KEY `idx_notifiche_user_tipo_data` (`user_id`,`tipo`,`data_creazione`),
  KEY `idx_notifiche_letta_data` (`letta`,`data_creazione`),
  CONSTRAINT `notifiche_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifiche`
--

LOCK TABLES `notifiche` WRITE;
/*!40000 ALTER TABLE `notifiche` DISABLE KEYS */;
/*!40000 ALTER TABLE `notifiche` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifiche_preferenze`
--

DROP TABLE IF EXISTS `notifiche_preferenze`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifiche_preferenze` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `tipo_notifica` varchar(50) NOT NULL,
  `email_enabled` tinyint(1) DEFAULT 1,
  `push_enabled` tinyint(1) DEFAULT 1,
  `soglia_giorni` int(11) DEFAULT 7,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_tipo` (`user_id`,`tipo_notifica`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `notifiche_preferenze_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifiche_preferenze`
--

LOCK TABLES `notifiche_preferenze` WRITE;
/*!40000 ALTER TABLE `notifiche_preferenze` DISABLE KEYS */;
INSERT INTO `notifiche_preferenze` VALUES (1,5,'scadenza',1,1,7,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(2,6,'scadenza',1,1,7,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(3,5,'pratica',1,1,7,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(4,6,'pratica',1,1,7,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(5,5,'progetto',1,1,3,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(6,6,'progetto',1,1,3,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(7,5,'sistema',1,1,1,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(8,6,'sistema',1,1,1,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(9,5,'fattura',1,1,3,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(10,6,'fattura',1,1,3,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(11,5,'cliente',1,1,3,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(12,6,'cliente',1,1,3,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(13,5,'documento',1,1,3,'2025-06-10 20:36:54','2025-06-10 20:36:54'),(14,6,'documento',1,1,3,'2025-06-10 20:36:54','2025-06-10 20:36:54');
/*!40000 ALTER TABLE `notifiche_preferenze` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pratiche`
--

DROP TABLE IF EXISTS `pratiche`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pratiche` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `progetto_id` int(11) DEFAULT NULL,
  `tipo_pratica` varchar(100) DEFAULT NULL,
  `stato` enum('in_attesa','in_revisione','approvata','completata','sospesa','respinta') DEFAULT 'in_attesa',
  `data_apertura` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_scadenza` date DEFAULT NULL,
  `note` text DEFAULT NULL,
  `numero_pratica` varchar(50) DEFAULT NULL,
  `tipo_documento` enum('cila','scia','permesso_costruire','altro') DEFAULT 'altro',
  `ente_riferimento` varchar(100) DEFAULT NULL,
  `protocollo` varchar(50) DEFAULT NULL,
  `data_protocollo` date DEFAULT NULL,
  `data_scadenza_integrazione` date DEFAULT NULL,
  `importo_diritti` decimal(10,2) DEFAULT NULL,
  `note_interne` text DEFAULT NULL,
  `documenti_richiesti` text DEFAULT NULL,
  `responsabile` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `progetto_id` (`progetto_id`),
  KEY `idx_pratiche_stato` (`stato`),
  KEY `idx_pratiche_data_scadenza` (`data_scadenza`),
  KEY `idx_pratiche_data_scadenza_integrazione` (`data_scadenza_integrazione`),
  KEY `idx_pratiche_stato_data` (`stato`,`data_apertura`),
  KEY `idx_pratiche_ente` (`ente_riferimento`),
  KEY `idx_pratiche_responsabile` (`responsabile`),
  CONSTRAINT `pratiche_ibfk_1` FOREIGN KEY (`progetto_id`) REFERENCES `progetti` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pratiche`
--

LOCK TABLES `pratiche` WRITE;
/*!40000 ALTER TABLE `pratiche` DISABLE KEYS */;
INSERT INTO `pratiche` VALUES (1,1,NULL,'in_attesa','2024-12-07 23:00:00','2025-02-28','','1/2025','cila','','34566','2025-01-17',NULL,NULL,'','doc id','Roberto');
/*!40000 ALTER TABLE `pratiche` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pratiche_backup_20250610_225826`
--

DROP TABLE IF EXISTS `pratiche_backup_20250610_225826`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pratiche_backup_20250610_225826` (
  `id` int(11) NOT NULL DEFAULT 0,
  `progetto_id` int(11) DEFAULT NULL,
  `tipo_pratica` varchar(100) DEFAULT NULL,
  `stato` enum('in_attesa','in_revisione','approvata') DEFAULT 'in_attesa',
  `data_apertura` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_scadenza` date DEFAULT NULL,
  `note` text DEFAULT NULL,
  `numero_pratica` varchar(50) DEFAULT NULL,
  `tipo_documento` enum('cila','scia','permesso_costruire','altro') DEFAULT 'altro',
  `ente_riferimento` varchar(100) DEFAULT NULL,
  `protocollo` varchar(50) DEFAULT NULL,
  `data_protocollo` date DEFAULT NULL,
  `data_scadenza_integrazione` date DEFAULT NULL,
  `importo_diritti` decimal(10,2) DEFAULT NULL,
  `note_interne` text DEFAULT NULL,
  `documenti_richiesti` text DEFAULT NULL,
  `responsabile` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pratiche_backup_20250610_225826`
--

LOCK TABLES `pratiche_backup_20250610_225826` WRITE;
/*!40000 ALTER TABLE `pratiche_backup_20250610_225826` DISABLE KEYS */;
INSERT INTO `pratiche_backup_20250610_225826` VALUES (1,1,NULL,'in_attesa','2024-12-07 23:00:00','2025-02-28','','1/2025','cila','','34566','2025-01-17',NULL,NULL,'','doc id','Roberto');
/*!40000 ALTER TABLE `pratiche_backup_20250610_225826` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `progetti`
--

DROP TABLE IF EXISTS `progetti`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `progetti` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cliente_id` int(11) DEFAULT NULL,
  `nome_progetto` varchar(200) NOT NULL,
  `descrizione` text DEFAULT NULL,
  `data_inizio` date DEFAULT NULL,
  `data_fine_prevista` date DEFAULT NULL,
  `stato` enum('in_corso','completato','sospeso') DEFAULT 'in_corso',
  `importo` decimal(10,2) DEFAULT NULL,
  `comune` varchar(100) DEFAULT NULL,
  `indirizzo_progetto` text DEFAULT NULL,
  `tipo_progetto` enum('ristrutturazione','nuova_costruzione','consulenza','altro') DEFAULT 'altro',
  `priorita` enum('bassa','media','alta') DEFAULT 'media',
  PRIMARY KEY (`id`),
  KEY `cliente_id` (`cliente_id`),
  CONSTRAINT `progetti_ibfk_1` FOREIGN KEY (`cliente_id`) REFERENCES `clienti` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `progetti`
--

LOCK TABLES `progetti` WRITE;
/*!40000 ALTER TABLE `progetti` DISABLE KEYS */;
INSERT INTO `progetti` VALUES (1,1,'Casa','','2024-12-08','2025-04-08','in_corso',50000.00,'Roma','via della Stazione di Ciampino 151','ristrutturazione','media'),(3,2,'tettoia','','2025-05-24','2025-05-31','in_corso',NULL,NULL,NULL,NULL,NULL),(4,1,'tettoia','','2025-05-24','2025-05-31','in_corso',NULL,NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `progetti` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `scadenze`
--

DROP TABLE IF EXISTS `scadenze`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `scadenze` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `progetto_id` int(11) DEFAULT NULL,
  `titolo` varchar(255) NOT NULL,
  `descrizione` text DEFAULT NULL,
  `data_scadenza` date NOT NULL,
  `stato` enum('in_corso','completata','scaduta') NOT NULL DEFAULT 'in_corso',
  `priorita` enum('bassa','media','alta') NOT NULL DEFAULT 'media',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `progetto_id` (`progetto_id`),
  CONSTRAINT `scadenze_ibfk_1` FOREIGN KEY (`progetto_id`) REFERENCES `progetti` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `scadenze`
--

LOCK TABLES `scadenze` WRITE;
/*!40000 ALTER TABLE `scadenze` DISABLE KEYS */;
/*!40000 ALTER TABLE `scadenze` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `ruolo` enum('admin','user') NOT NULL DEFAULT 'user',
  `active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (5,'admin','<EMAIL>','$2y$10$r.WU6ccReM4T9KhArvZDhe1xcwGOMtLyK5iaFwDfbC4ExBXXJp1.W','admin',1),(6,'Mauro',NULL,'$2y$10$RQ704WI9LMMrkTkX4A8OjOMDm12m6ovhOVpkT1C2RRSwPwx3W/a7e','user',1);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-10 23:52:04
