# Piano di Aggiornamento Webapp Studio Tecnico

## Indice

- [1. Elenco Funzionalità e Stato di Avanzamento](#1-elenco-funzionalità-e-stato-di-avanzamento)
  - [1.1. Gestione Anagrafiche](#11-gestione-anagrafiche)
  - [1.2. Gestione Operativa](#12-gestione-operativa)
  - [1.3. Comunicazione e Notifiche](#13-comunicazione-e-notifiche)
  - [1.4. Interfaccia Utente (UI) e User Experience (UX)](#14-interfaccia-utente-ui-e-user-experience-ux)
  - [1.5. <PERSON><PERSON><PERSON>](#15-sicurezza)
  - [1.6. Amministrazione e Configurazione di Sistema](#16-amministrazione-e-configurazione-di-sistema)
  - [1.7. Architettura e Qualità del Codice](#17-architettura-e-qualità-del-codice)
  - [1.8. Test e Validazione](#18-test-e-validazione)
  - [1.9. Database](#19-database)
  - [1.10. Gestione Dipendenze e Deployment](#110-gestione-dipendenze-e-deployment)
  - [1.11. Documentazione](#111-documentazione)
  - [1.12. Miglioramenti Funzionali Specifici (Nuove Funzionalità Maggiori)](#112-miglioramenti-funzionali-specifici-nuove-funzionalità-maggiori)
- [2. Introduzione](#2-introduzione)
- [3. Aree Chiave di Aggiornamento](#3-aree-chiave-di-aggiornamento)
  - [3.1. Architettura e Design Patterns](#31-architettura-e-design-patterns)
  - [3.2. Qualità del Codice e Manutenibilità](#32-qualità-del-codice-e-manutenibilità)
  - [3.3. Sicurezza](#33-sicurezza)
  - [3.4. Test](#34-test)
  - [3.5. Interfaccia Utente (UI) e User Experience (UX)](#35-interfaccia-utente-ui-e-user-experience-ux)
  - [3.6. Gestione Errori e Logging](#36-gestione-errori-e-logging)
  - [3.7. Database](#37-database)
  - [3.8. Gestione Dipendenze](#38-gestione-dipendenze)
  - [3.9. Documentazione](#39-documentazione)
  - [3.10. Miglioramenti Funzionali Specifici](#310-miglioramenti-funzionali-specifici)
- [4. Pianificazione Operativa (Note di Sessione)](#4-pianificazione-operativa-note-di-sessione)
  - [4.1. Sessione del 23 Maggio 2025](#41-sessione-del-23-maggio-2025)
  - [4.2. Sessione del 24 Maggio 2025 - Analisi Struttura e Documentazione](#42-sessione-del-24-maggio-2025---analisi-struttura-e-documentazione)

---

## 1. Elenco Funzionalità e Stato di Avanzamento

### 1.1. Gestione Anagrafiche
- [x] Gestione Clienti (CRUD base, ricerca)
- [x] Gestione Utenti (registrazione, login, gestione account base)

### 1.2. Gestione Operativa
- [x] Gestione Progetti (CRUD base, associazione clienti, stato)
- [x] Gestione Pratiche (CRUD base, associazione progetti, stato, date)
- [x] Gestione Allegati (upload, download, organizzazione base)
- [x] Gestione Scadenze (monitoraggio e gestione base)

### 1.3. Comunicazione e Notifiche
- [x] Sistema di Notifiche interne (avvisi base)

### 1.4. Interfaccia Utente (UI) e User Experience (UX)
- [x] Dashboard Amministrativa (panoramica base)
- [x] Dashboard Utente (panoramica base)
- [ ] Ricerca Avanzata (su clienti, progetti, pratiche)
- [x] Interfaccia Responsive (garantita da Bootstrap)
- [x] Temi Personalizzabili (Light/Dark)
- [ ] Revisione e modernizzazione completa UI
- [ ] Miglioramento navigazione e flussi utente
- [ ] Ottimizzazione performance frontend
- [ ] Valutazione e implementazione standard di Accessibilità (WCAG)

### 1.5. Sicurezza
- [x] Gestione password sicura (hashing - da verificare implementazione `password_hash()`)
- [x] Protezione CSRF (meccanismo base presente in `Security.php`)
- [ ] Revisione e potenziamento protezione XSS (sanificazione output)
- [ ] Revisione e potenziamento protezione SQL Injection (verifica uso estensivo prepared statements)
- [ ] Protezione avanzata upload file (validazione MIME type server-side, scansione)
- [ ] Implementazione Content Security Policy (CSP)
- [ ] Utilizzo HTTPS per tutte le comunicazioni (configurazione server)
- [ ] Autenticazione a Due Fattori (2FA)

### 1.6. Amministrazione e Configurazione di Sistema
- [x] Gestione Utenti da pannello Admin (CRUD utenti, ruoli base)
- [ ] Interfaccia UI per configurazioni di sistema
- [ ] Funzionalità di Backup (procedure e/o interfaccia UI)

### 1.7. Architettura e Qualità del Codice
- [ ] Adozione completa dei Modelli (Models) per tutta la logica dati
- [ ] Introduzione di un Service Layer (opzionale)
- [ ] Standardizzazione dell'ereditarietà e delle funzionalità base dei Controller
- [ ] Refactoring esteso per leggibilità e manutenibilità
- [x] Adozione parziale PSR-12 (da estendere e verificare)
- [ ] Utilizzo estensivo di PHPDoc per classi e metodi
- [ ] Rimozione sistematica codice duplicato/morto

### 1.8. Test e Validazione
- [ ] Introduzione Test Unitari (PHPUnit) per Modelli e Servizi
- [ ] Introduzione Test di Integrazione per interazioni tra componenti
- [ ] Introduzione Test Funzionali/E2E (es. Codeception, Selenium)

### 1.9. Database
- [ ] Ottimizzazione sistematica query e indici DB
- [ ] Revisione completa schema DB per coerenza e normalizzazione
- [ ] Implementazione di un meccanismo di migrazioni DB (es. Phinx)

### 1.10. Gestione Dipendenze e Deployment
- [x] Utilizzo di Composer per dipendenze PHP (da verificare completezza)
- [ ] Definizione di un processo di deployment standardizzato (es. script, CI/CD)

### 1.11. Documentazione
- [x] Mantenimento documentazione tecnica (`webapp_structure.md`, `app_map.md`)
- [x] Mantenimento documentazione di progetto (`aggiornamento.md`)
- [ ] Creazione/Aggiornamento manuale utente dettagliato

### 1.12. Miglioramenti Funzionali Specifici (Nuove Funzionalità Maggiori)
- **Gestione Clienti Avanzata**
  - [ ] Campi personalizzati per clienti
  - [ ] Segmentazione clienti (tag, categorie)
  - [ ] Storico interazioni completo (log chiamate, email, appuntamenti)
  - [ ] Import/Export dati clienti (CSV, Excel)
- **Gestione Progetti Migliorata**
  - [ ] Fasi di progetto personalizzabili e tracciabili
  - [ ] Assegnazione task specifici a utenti all'interno dei progetti
  - [ ] Diagrammi di Gantt o timeline visuali per i progetti
  - [ ] Calcolo automatico avanzamento progetto basato su task/fasi
- **Gestione Pratiche Potenziata**
  - [ ] Template per tipi di pratiche comuni (precompilazione campi, allegati standard)
  - [ ] Workflow di approvazione personalizzabili per le pratiche
  - [ ] Collegamento diretto a normative o riferimenti esterni per tipo pratica
- **Modulo Scadenze e Calendario Avanzato**
  - [ ] Calendario interattivo (visualizzazione scadenze clienti, progetti, pratiche)
  - [ ] Promemoria automatici via email e/o notifiche interne per scadenze
  - [ ] (Opzionale) Sincronizzazione con calendari esterni (Google Calendar, Outlook)
- **Modulo Reportistica Avanzata**
  - [ ] Interfaccia per la creazione di report personalizzati (dati clienti, progetti, performance)
  - [ ] Esportazione report in formati standard (PDF, Excel, CSV)
  - [ ] Dashboard con grafici e KPI configurabili dall'utente
- **Funzionalità di Backup Avanzate**
  - [ ] Backup automatici e pianificati (database e file allegati)
  - [ ] Interfaccia per la gestione dei backup (visualizzazione, ripristino)
- **Modulo API per Integrazioni Esterne**
  - [ ] Sviluppo API RESTful per permettere integrazioni con altri software

---

## 2. Introduzione

Questo documento delinea un piano strategico per l'aggiornamento della webapp "Studio Tecnico". L'obiettivo è evolvere l'applicazione attuale migliorandone la struttura, la sicurezza, l'efficienza e la manutenibilità, preparandola per future espansioni e garantendo una maggiore longevità tecnologica. L'analisi dei controller e della struttura generale ha rivelato diverse aree di potenziale miglioramento.

## 3. Aree Chiave di Aggiornamento

### 3.1. Architettura e Design Patterns

*   **Adozione Completa dei Modelli (Models)**:
    *   **Obiettivo**: Separare nettamente la logica di business e l'accesso ai dati dalla logica di presentazione (Controller).
    *   **Azioni**: 
        *   Refactoring dei controller `ClientiController`, `ProgettiController`, `PraticheController`, e `ScadenzeController` per spostare tutte le query SQL e la logica di manipolazione dei dati in classi Model dedicate (es. `ClienteModel`, `ProgettoModel`, `PraticaModel`, `ScadenzaModel`).
        *   Anche `AdminProfileController` dovrebbe utilizzare un `AdminProfileModel`.
    *   **Benefici**: Migliore organizzazione del codice, maggiore riusabilità, testabilità facilitata.

*   **Introduzione di un Service Layer (Opzionale ma Consigliato)**:
    *   **Obiettivo**: Gestire logiche di business complesse che orchestrano più modelli o eseguono operazioni non direttamente legate alla persistenza.
    *   **Azioni**: Creare classi Service (es. `ClienteService`, `ProgettoService`) che si interpongono tra Controller e Model. I Controller chiameranno i Service, che a loro volta utilizzeranno i Model.
    *   **Benefici**: Controller più snelli, logica di business centralizzata e riutilizzabile.

*   **Standardizzazione dell'Ereditarietà dei Controller**:
    *   **Obiettivo**: Garantire che tutti i controller beneficino di funzionalità comuni e seguano un pattern consistente.
    *   **Azioni**: Modificare `AdminProfileController` e `ConfigController` affinché estendano la classe base `App\Core\Controller`. Questo permetterà di utilizzare metodi helper comuni per il rendering delle viste, redirect, gestione CSRF, ecc.
    *   **Benefici**: Coerenza, riduzione di codice duplicato, più facile manutenzione.

*   **Dependency Injection (DI)**:
    *   **Obiettivo**: Migliorare la gestione delle dipendenze e la testabilità.
    *   **Azioni**: Invece di utilizzare `App\Core\Database::getInstance()` globalmente, iniettare l'istanza del database (o altre dipendenze) nei costruttori dei Model, Service e Controller dove necessario. Considerare l'uso di un container DI semplice o di autowiring se il progetto cresce.
    *   **Benefici**: Codice più disaccoppiato, maggiore flessibilità, testabilità migliorata.

### 3.2. Qualità del Codice e Manutenibilità

*   **Aderenza Rigorosa a PSR-12**:
    *   **Obiettivo**: Mantenere uno stile di codice consistente e leggibile.
    *   **Azioni**: Utilizzare strumenti come PHP-CS-Fixer o PHPCodeSniffer per analizzare e formattare il codice secondo lo standard PSR-12.
    *   **Benefici**: Migliore leggibilità e collaborazione.

*   **Utilizzo Estensivo di Type Hinting e Return Types (PHP 7.0+)**:
    *   **Obiettivo**: Migliorare la robustezza del codice e facilitare il debugging.
    *   **Azioni**: Aggiungere type hints per i parametri dei metodi/funzioni e specificare i tipi di ritorno ovunque possibile.
    *   **Benefici**: Rilevamento precoce degli errori, codice più auto-documentante.

*   **Gestione Centralizzata degli Errori e Logging Migliorato**:
    *   **Obiettivo**: Avere un sistema robusto e informativo per la gestione degli errori.
    *   **Azioni**: Standardizzare la gestione delle eccezioni. Assicurarsi che tutti gli errori significativi siano loggati con dettagli sufficienti (utilizzando il sistema di logging già presente in `logs/error_log.php` ma integrandolo meglio).
    *   **Benefici**: Debugging più efficiente, monitoraggio proattivo.

### 3.3. Sicurezza

*   **Protezione CSRF Consistente**:
    *   **Obiettivo**: Prevenire attacchi Cross-Site Request Forgery.
    *   **Azioni**: Verificare e implementare la validazione del token CSRF su *tutti* i form che eseguono azioni di modifica (POST, PUT, DELETE), inclusi quelli in `AdminProfileController`, `ConfigController` e `ScadenzeController`.
    *   **Benefici**: Maggiore sicurezza contro attacchi comuni.

*   **Sanificazione e Validazione degli Input**:
    *   **Obiettivo**: Prevenire XSS, SQL Injection e altri attacchi basati su input malevoli.
    *   **Azioni**: Rafforzare la sanificazione e la validazione di tutti gli input provenienti dall'utente (GET, POST, COOKIE). Considerare l'uso di una libreria di validazione dedicata per centralizzare e standardizzare le regole.
    *   **Benefici**: Applicazione più robusta e sicura.

*   **Revisione Gestione Sessioni**:
    *   **Obiettivo**: Assicurare che le sessioni siano gestite in modo sicuro.
    *   **Azioni**: Controllare le configurazioni di sessione (es. `session.cookie_httponly`, `session.use_strict_mode`). Implementare meccanismi di rigenerazione dell'ID di sessione al login/logout.
    *   **Benefici**: Riduzione del rischio di session hijacking.

*   **Hashing delle Password**:
    *   **Obiettivo**: Proteggere le credenziali utente.
    *   **Azioni**: Verificare che tutte le password siano gestite utilizzando `password_hash()` e `password_verify()`.
    *   **Benefici**: Sicurezza delle password conforme agli standard attuali.

### 3.4. Test

*   **Introduzione di Unit Test**:
    *   **Obiettivo**: Verificare la correttezza delle singole unità di codice (classi, metodi).
    *   **Azioni**: Scrivere unit test con PHPUnit per i Model, i Service e le classi helper.
    *   **Benefici**: Rilevamento precoce dei bug, facilitazione del refactoring, documentazione vivente del codice.

*   **Introduzione di Integration Test**:
    *   **Obiettivo**: Verificare che diverse parti dell'applicazione interagiscano correttamente.
    *   **Azioni**: Scrivere test di integrazione per i flussi principali dei Controller.
    *   **Benefici**: Maggiore confidenza nella stabilità dell'applicazione.

### 3.5. Interfaccia Utente (UI) e User Experience (UX)

*   **Miglioramento dell'Interfaccia Utente**:
    *   **Obiettivo**: Migliorare l'esperienza utente e la navigazione all'interno dell'applicazione.
    *   **Azioni**: Implementare un design responsivo, migliorare la struttura delle pagine, utilizzare elementi di feedback visivo per le azioni dell'utente.
    *   **Benefici**: Migliore esperienza utente, maggiore accessibilità.

### 3.6. Gestione Errori e Logging

*   **Gestione Centralizzata degli Errori**:
    *   **Obiettivo**: Avere un sistema robusto e informativo per la gestione degli errori.
    *   **Azioni**: Standardizzare la gestione delle eccezioni. Assicurarsi che tutti gli errori significativi siano loggati con dettagli sufficienti.
    *   **Benefici**: Debugging più efficiente, monitoraggio proattivo.

### 3.7. Database

*   **Sistema di Migrazioni Database**:
    *   **Obiettivo**: Gestire le modifiche allo schema del database in modo controllato, versionato e riproducibile.
    *   **Azioni**: Introdurre uno strumento di migrazione come Phinx o Doctrine Migrations. Creare migrazioni iniziali basate sullo schema attuale e utilizzare migrazioni per tutte le future modifiche.
    *   **Benefici**: Semplifica lo sviluppo collaborativo, il deployment e il rollback delle modifiche al DB.

### 3.8. Gestione Dipendenze

*   **Utilizzo di Composer**:
    *   **Obiettivo**: Gestire le dipendenze PHP in modo efficiente e scalabile.
    *   **Azioni**: Utilizzare Composer per gestire tutte le dipendenze PHP. Assicurarsi che tutte le dipendenze siano aggiornate e che siano gestite le versioni in modo appropriato.
    *   **Benefici**: Gestione delle dipendenze più efficiente, riduzione del rischio di conflitti.

### 3.9. Documentazione

*   **Mantenimento della Documentazione**:
    *   **Obiettivo**: Mantenere la documentazione aggiornata e completa.
    *   **Azioni**: Aggiornare regolarmente la documentazione tecnica, il manuale utente e la documentazione di progetto.
    *   **Benefici**: Migliore comprensione dell'applicazione, facilitazione della manutenzione e dello sviluppo.

### 3.10. Miglioramenti Funzionali Specifici

*   **Gestione Clienti Avanzata**:
    *   **Obiettivo**: Migliorare la gestione dei clienti.
    *   **Azioni**: Implementare la gestione dei campi personalizzati, la segmentazione dei clienti, lo storico delle interazioni e l'import/export dei dati clienti.
    *   **Benefici**: Migliore gestione dei clienti, maggiore personalizzazione.

## 4. Pianificazione Operativa (Note di Sessione)

### 4.1. Sessione del 23 Maggio 2025

**OBIETTIVI RAGGIUNTI:**
1. Gestione Tipi Documento nelle Pratiche
   - Corretto il campo tipo_documento nel database
   - Implementata visualizzazione corretta nei dettagli
   - Aggiunta formattazione per migliore leggibilità
   - Gestita retrocompatibilità con tipo_pratica

2. Miglioramenti UI
   - Implementato SweetAlert2 per conferme
   - Migliorata visualizzazione dettagli pratiche
   - Aggiornata interfaccia gestione pratiche
   - Ottimizzata visualizzazione stati

3. File Modificati
   - /app/controllers/PraticheController.php: gestione tipo_documento
   - /app/controllers/ProgettiController.php: visualizzazione pratiche
   - /views/pratiche/dettagli.php: aggiornata visualizzazione
   - /views/progetti/dettagli.php: corretta visualizzazione pratiche
   - /docs/*: aggiornata documentazione

4. Documentazione
   - Aggiornato manuale utente
   - Aggiunta sezione gestione pratiche
   - Documentate nuove funzionalità
   - Aggiornate procedure operative

**NOTE TECNICHE:**
- Utilizzato COALESCE per gestire campo tipo_documento/tipo_pratica
- Implementata formattazione testo per tipi documento
- Aggiunta gestione valori null
- Ottimizzate query database

**PROSSIMI PASSI (da questa sessione):**
- Implementare gestione documenti allegati
- Aggiungere sistema notifiche scadenze
- Migliorare filtri ricerca pratiche
- Implementare log operazioni

### 4.2. Sessione del 24 Maggio 2025 - Analisi Struttura e Documentazione

*Ultimo aggiornamento: 24 Maggio 2025*

#### Completato
- Implementata la gestione completa dei tipi di documento nelle pratiche
- Corretta la visualizzazione dei dettagli delle pratiche nei progetti
- Migliorata l'interfaccia utente con SweetAlert2 per le conferme
- Aggiornata la documentazione del sistema

#### Prossimi Task
1. **Miglioramenti Pratiche**
   - Implementare la gestione dei documenti allegati
   - Aggiungere un sistema di notifiche per le scadenze
   - Migliorare la visualizzazione dello stato pratica
   - Aggiungere filtri avanzati per la ricerca pratiche

2. **Sicurezza**
   - Implementare log delle operazioni sulle pratiche
   - Aggiungere controlli di autorizzazione per tipo utente
   - Migliorare la validazione dei dati in input

3. **UI/UX**
   - Aggiungere animazioni di caricamento
   - Migliorare il feedback visivo delle azioni
   - Ottimizzare la visualizzazione su dispositivi mobili

#### Note Tecniche
- Mantenere la coerenza nella gestione dei tipi di documento
- Assicurarsi che tutti i messaggi di errore siano chiari e informativi
- Documentare eventuali modifiche al sistema di gestione pratiche

## 5. Conclusione

Questo piano di ammodernamento è una guida per trasformare la webapp "Studio Tecnico" in un'applicazione più robusta, sicura e moderna. Richiederà un impegno significativo, ma i benefici a lungo termine in termini di stabilità, manutenibilità e scalabilità saranno considerevoli. È importante procedere per fasi, testando accuratamente ogni modifica.
