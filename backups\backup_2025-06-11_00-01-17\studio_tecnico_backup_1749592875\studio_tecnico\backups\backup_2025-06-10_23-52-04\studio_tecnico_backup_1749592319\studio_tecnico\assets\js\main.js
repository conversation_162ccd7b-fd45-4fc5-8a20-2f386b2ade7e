const StudioApp = {
    debug(message) {
        console.log(`[Theme Debug] ${message}`);
        // Solo se BASE_URL è definito, invia il log al server
        if (typeof BASE_URL !== 'undefined') {
            fetch(`${BASE_URL}api/log-theme.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    timestamp: new Date().toISOString()
                })
            }).catch(error => console.error('Error logging:', error));
        }
    },

    init() {
        try {
            this.initTheme();
            this.initDataTables();
            this.initSelect2();
            this.initDatePickers();
            this.initTooltips();
        } catch (error) {
            console.error('Error initializing app:', error);
            this.debug(`Initialization error: ${error.message}`);
        }
    },

    // Inizializza DataTables per tutte le tabelle
    initDataTables() {
        try {
            if (typeof BASE_URL === 'undefined') {
                console.error('BASE_URL is not defined');
                return;
            }
            $('.datatable').DataTable({
                language: {
                    url: `${BASE_URL}assets/libs/datatables/it-IT.json`
                },
                responsive: true,
                pageLength: 25
            });
        } catch (error) {
            console.error('Error initializing DataTables:', error);
        }
    },

    // Inizializza Select2 per i select avanzati
    initSelect2() {
        try {
            $('.select2').select2({
                language: 'it',
                width: '100%'
            });
        } catch (error) {
            console.error('Error initializing Select2:', error);
        }
    },

    // Inizializza i date picker
    initDatePickers() {
        try {
            $('.datepicker').flatpickr({
                locale: 'it',
                dateFormat: 'Y-m-d',
                allowInput: true
            });
        } catch (error) {
            console.error('Error initializing DatePickers:', error);
        }
    },

    // Inizializza i tooltip
    initTooltips() {
        try {
            $('[data-bs-toggle="tooltip"]').tooltip();
        } catch (error) {
            console.error('Error initializing Tooltips:', error);
        }
    },

    // Inizializza e gestisce il tema
    initTheme() {
        try {
            const initThemeElements = () => {
                const themeToggle = document.getElementById('themeToggle');
                const themeIcon = document.getElementById('themeIcon');
                
                if (!themeToggle || !themeIcon) {
                    return;
                }

                if (themeToggle.dataset.initialized === 'true') {
                    return;
                }

                // Ottieni il tema corrente dall'attributo HTML
                const currentTheme = document.documentElement.getAttribute('data-bs-theme');
                this.updateThemeIcon(themeIcon, currentTheme);
                themeToggle.dataset.initialized = 'true';

                // Gestisce il click sul pulsante
                themeToggle.addEventListener('click', () => {
                    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                    
                    // Imposta il cookie con path=/ per essere disponibile su tutte le pagine
                    document.cookie = `theme=${newTheme};path=/;max-age=31536000`;
                    document.documentElement.setAttribute('data-bs-theme', newTheme);
                    this.updateThemeIcon(themeIcon, newTheme);
                    
                    // Aggiorna DataTables se presenti
                    const dataTables = $('.datatable');
                    if (dataTables.length) {
                        dataTables.DataTable().draw();
                    }
                });
            };

            // Inizializza quando il DOM è pronto
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initThemeElements);
            } else {
                initThemeElements();
            }

        } catch (error) {
            console.error('Error in theme initialization:', error);
        }
    },

    // Aggiorna l'icona del tema
    updateThemeIcon(icon, theme) {
        try {
            if (icon) {
                const oldClass = icon.className;
                icon.className = theme === 'dark' ? 'fas fa-moon' : 'fas fa-sun';
                this.debug(`Icon updated from ${oldClass} to ${icon.className}`);
            }
        } catch (error) {
            console.error('Error updating theme icon:', error);
            this.debug(`Error updating theme icon: ${error.message}`);
        }
    }
};

// Inizializza l'app quando il documento è pronto
$(document).ready(() => {
    StudioApp.init();
});