<?php
namespace App\Core;

class Router {
    private $routes = [];
    private $params = [];
    private $notFoundCallback;
    private $db;

    public function __construct($db) {
        $this->db = $db;
    }

    public function add($route, $controller, $action, $middleware = null, $method = 'GET') {
        // Converti il pattern della route in una regex
        $route = preg_replace('/\//', '\\/', $route);
        $route = preg_replace('/\{([a-z]+)\}/', '(?P<\1>[0-9]+)', $route);
        $route = '/^' . $route . '$/i';
        
        $this->routes[$method][$route] = [
            'controller' => $controller,
            'action' => $action,
            'middleware' => $middleware
        ];
    }

    public function get($route, $controller, $action, $middleware = null) {
        $this->add($route, $controller, $action, $middleware, 'GET');
    }

    public function post($route, $controller, $action, $middleware = null) {
        $this->add($route, $controller, $action, $middleware, 'POST');
    }

    public function setNotFound($callback) {
        $this->notFoundCallback = $callback;
    }

    public function match($url, $method = 'GET') {
        if (isset($this->routes[$method])) {
            foreach ($this->routes[$method] as $route => $params) {
                if (preg_match($route, $url, $matches)) {
                    // Estrai i parametri nominati dalla regex
                    $routeParams = array_filter($matches, function($key) {
                        return is_string($key) && !is_numeric($key);
                    }, ARRAY_FILTER_USE_KEY);
                    
                    // Converti i parametri in array numerico mantenendo l'ordine
                    $orderedParams = array_values($routeParams);
                    
                    $this->params = array_merge($params, ['params' => $orderedParams]);
                    return true;
                }
            }
        }
        return false;
    }

    public function dispatch($url) {
        $url = $this->removeQueryStringVariables($url);
        $method = $_SERVER['REQUEST_METHOD'];

        if ($this->match($url, $method)) {
            $controller = $this->params['controller'];
            $action = $this->params['action'];
            $middleware = $this->params['middleware'] ?? null;
            
            // Esegui il middleware se presente
            if ($middleware !== null) {
                $middleware();
            }

            $controller = "App\\Controllers\\" . $controller;
            if (class_exists($controller)) {
                $controller = new $controller($this->db);
                if (method_exists($controller, $action)) {
                    return call_user_func_array([$controller, $action], $this->params['params'] ?? []);
                }
            }
        }

        if ($this->notFoundCallback) {
            call_user_func($this->notFoundCallback);
            return;
        }

        throw new \Exception('Route not found: ' . $url);
    }

    private function removeQueryStringVariables($url) {
        if ($url != '') {
            $parts = explode('?', $url, 2);
            $url = $parts[0];
        }
        return rtrim($url, '/');
    }
}
