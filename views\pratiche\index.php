<?php
$pageTitle = 'Pratiche';
include VIEWS_DIR . '/layouts/header.php';
?>

<div class="container-fluid py-4">
    <div class="page-header">
        <h2 class="page-title">
            <i class="fas fa-file-alt"></i>
            Gestione Pratiche
        </h2>
        <div class="page-actions">
            <?php 
            $guideTitle = "Guida Rapida - Gestione Pratiche";
            $guideContent = '
                <div class="guide-section mb-4">
                    <h6 class="fw-bold"><i class="fas fa-file-alt me-2"></i>Gestione Pratiche</h6>
                    <p>Questa sezione ti permette di gestire tutte le pratiche amministrative:</p>
                    <ul class="list-unstyled ps-3">
                        <li><i class="fas fa-plus text-success me-2"></i><strong>Nuova Pratica:</strong> Crea una nuova pratica e associala a un progetto</li>
                        <li><i class="fas fa-edit text-primary me-2"></i><strong>Modifica:</strong> Aggiorna lo stato e i dettagli delle pratiche</li>
                        <li><i class="fas fa-calendar text-info me-2"></i><strong>Scadenze:</strong> Monitora le date importanti</li>
                        <li><i class="fas fa-file-upload text-warning me-2"></i><strong>Documenti:</strong> Gestisci gli allegati</li>
                    </ul>
                </div>
                <div class="guide-section">
                    <h6 class="fw-bold"><i class="fas fa-lightbulb me-2"></i>Suggerimenti</h6>
                    <ul class="list-unstyled ps-3">
                        <li><i class="fas fa-check text-success me-2"></i>Controlla regolarmente le scadenze</li>
                        <li><i class="fas fa-check text-success me-2"></i>Mantieni ordinati i documenti allegati</li>
                        <li><i class="fas fa-check text-success me-2"></i>Aggiorna lo stato delle pratiche tempestivamente</li>
                        <li><i class="fas fa-check text-success me-2"></i>Verifica la completezza della documentazione</li>
                    </ul>
                </div>
            ';
            include VIEWS_DIR . '/components/quick_guide.php'; 
            ?>
            <a href="<?= BASE_URL ?>pratiche/nuovo" class="btn btn-neutral">
                <i class="fas fa-plus"></i>
                Nuova Pratica
            </a>
        </div>
    </div>

    <?php if (isset($_GET['msg'])): ?>
        <?php if ($_GET['msg'] === 'success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Pratica inserita con successo!
            </div>
        <?php elseif ($_GET['msg'] === 'update_success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Pratica aggiornata con successo!
            </div>
        <?php elseif ($_GET['msg'] === 'delete_success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Pratica eliminata con successo!
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <?php if (isset($_GET['error'])): ?>
        <?php if ($_GET['error'] === 'delete_error'): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                Errore durante l'eliminazione della pratica.
            </div>
        <?php elseif ($_GET['error'] === 'pratica_non_trovata'): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                Pratica non trovata.
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <div class="table-card">
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>N° Pratica</th>
                        <th>Progetto</th>
                        <th>Cliente</th>
                        <th>Tipo</th>
                        <th>Stato</th>
                        <th>Data Apertura</th>
                        <th>Data Scadenza</th>
                        <th class="text-end">Azioni</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    error_log("Debug vista - Contenuto di \$pratiche: " . print_r($pratiche, true));
                    if (empty($pratiche)): 
                        error_log("Debug vista - \$pratiche è vuoto");
                    ?>
                        <tr>
                            <td colspan="8" class="text-center">
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle"></i>
                                    Nessuna pratica trovata
                                </div>
                            </td>
                        </tr>
                    <?php else: 
                        error_log("Debug vista - Numero di pratiche: " . count($pratiche));
                        foreach ($pratiche as $pratica): 
                            error_log("Debug vista - Elaborazione pratica ID: " . $pratica['id']);
                    ?>
                            <tr>
                                <td><?= htmlspecialchars($pratica['numero_pratica'] ?? '') ?></td>
                                <td><?= htmlspecialchars($pratica['nome_progetto'] ?? '') ?></td>
                                <td><?= htmlspecialchars($pratica['cliente_nome'] ?? '') ?></td>
                                <td><?= htmlspecialchars(ucfirst($pratica['tipo_pratica'] ?? '')) ?></td>
                                <td>
                                    <?php
                                    $statoClass = match($pratica['stato']) {
                                        'completata' => 'success',
                                        'in_corso' => 'primary',
                                        'in_attesa' => 'warning',
                                        'annullata' => 'danger',
                                        default => 'secondary'
                                    };
                                    ?>
                                    <div class="d-flex align-items-center">
                                        <span class="status-indicator status-<?= $statoClass ?>"></span>
                                        <span><?= ucfirst(str_replace('_', ' ', $pratica['stato'])) ?></span>
                                    </div>
                                </td>
                                <td><?= date('d/m/Y', strtotime($pratica['data_apertura'])) ?></td>
                                <td>
                                    <?php if ($pratica['data_scadenza']): ?>
                                        <?= date('d/m/Y', strtotime($pratica['data_scadenza'])) ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end">
                                    <div class="btn-group">
                                        <a href="<?= BASE_URL ?>pratiche/dettagli/<?= $pratica['id'] ?>" class="btn btn-sm btn-outline-primary" title="Visualizza">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= BASE_URL ?>pratiche/modifica/<?= $pratica['id'] ?>" class="btn btn-sm btn-outline-neutral" title="Modifica">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="javascript:void(0)" onclick="confirmDelete(<?= $pratica['id'] ?>)" class="btn btn-sm btn-outline-danger" title="Elimina">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Definisci BASE_URL per JavaScript
const BASE_URL = '<?= BASE_URL ?>';

function confirmDelete(id) {
    Swal.fire({
        title: 'Sei sicuro?',
        text: "Questa azione non può essere annullata!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Sì, elimina!',
        cancelButtonText: 'Annulla'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `${BASE_URL}pratiche/elimina/${id}`;
        }
    });
}
</script>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>