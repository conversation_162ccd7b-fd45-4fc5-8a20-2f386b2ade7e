<?php
namespace App\Models;

class User {
    private $db;
    
    public function __construct($db) {
        $this->db = $db;
    }
    
    public function login($username, $password) {
        error_log("Tentativo di login per username: " . $username);
        
        $sql = "SELECT * FROM users WHERE username = ? AND active = 1 LIMIT 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if (!$user) {
            error_log("Utente non trovato: " . $username);
            return false;
        }
        
        error_log("Utente trovato, verifica della password...");
        if (password_verify($password, $user['password'])) {
            error_log("Password verificata con successo");
            // Crea un array pulito per i dati di sessione
            $sessionUser = [
                'id' => $user['id'],
                'username' => $user['username'],
                'role' => $user['ruolo'], // Manteniamo il campo originale del database
                'active' => $user['active']
            ];
            return $sessionUser;
        }
        
        error_log("Password non valida per l'utente: " . $username);
        return false;
    }
    
    public function create($data) {
        $sql = "INSERT INTO users (username, email, password, nome, cognome, ruolo) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['username'],
            $data['email'],
            password_hash($data['password'], PASSWORD_DEFAULT),
            $data['nome'],
            $data['cognome'],
            $data['ruolo'] ?? 'user'
        ]);
    }
    
    public function getById($id) {
        $sql = "SELECT id, username, email, nome, cognome, ruolo as role, active, created_at 
                FROM users WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    public function update($id, $data) {
        $sql = "UPDATE users SET 
                username = ?, 
                email = ?, 
                nome = ?, 
                cognome = ?, 
                ruolo = ?,
                active = ?
                WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['username'],
            $data['email'],
            $data['nome'],
            $data['cognome'],
            $data['ruolo'],
            $data['active'],
            $id
        ]);
    }
    
    public function updatePassword($id, $newPassword) {
        $sql = "UPDATE users SET password = ? WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            password_hash($newPassword, PASSWORD_DEFAULT),
            $id
        ]);
    }
}
