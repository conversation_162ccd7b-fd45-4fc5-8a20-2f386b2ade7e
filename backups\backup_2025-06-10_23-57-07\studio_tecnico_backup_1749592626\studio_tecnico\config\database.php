<?php
if (!extension_loaded('pdo')) {
    die('L\'estensione PDO non è caricata');
}

if (!extension_loaded('pdo_mysql')) {
    die('L\'estensione PDO MySQL non è caricata');
}

return [
    'db' => [
        'host' => 'localhost',
        'dbname' => 'studio_tecnico',
        'user' => 'root',
        'pass' => '',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    ]
];