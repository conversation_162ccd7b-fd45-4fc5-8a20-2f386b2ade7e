<?php
require_once dirname(dirname(__DIR__)) . '/config/config.php';
require_once ROOT_PATH . '/config/database.php';

// Inizializziamo le variabili
$id = isset($_GET['id']) ? intval($_GET['id']) : null;
$cliente = null;
$error_message = '';
$success_message = '';
$redirect = false;

// Se è presente un ID, recupera i dati del cliente
if ($id) {
    try {
        $stmt = $conn->prepare("SELECT * FROM clienti WHERE id = ?");
        $stmt->execute([$id]);
        $cliente = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$cliente) {
            $error_message = "Cliente non trovato.";
        }
    } catch (PDOException $e) {
        $error_message = "Errore nel recupero dei dati del cliente: " . $e->getMessage();
    }
}

// Gestione del form POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Recupera i dati dal form
        $tipo_cliente = $_POST['tipo_cliente'] ?? 'privato';
        $nome = $_POST['nome'] ?? '';
        $cognome = $_POST['cognome'] ?? '';
        $email = $_POST['email'] ?? '';
        $telefono = $_POST['telefono'] ?? '';
        $indirizzo = $_POST['indirizzo'] ?? '';
        $cap = $_POST['cap'] ?? '';
        $citta = $_POST['citta'] ?? '';
        $provincia = $_POST['provincia'] ?? '';
        $ragione_sociale = $_POST['ragione_sociale'] ?? '';
        $partita_iva = $_POST['partita_iva'] ?? '';
        $codice_fiscale = $_POST['codice_fiscale'] ?? '';
        $pec = $_POST['pec'] ?? '';
        $note = $_POST['note'] ?? '';

        // Prepara i dati per l'inserimento/aggiornamento
        $params = [
            'tipo_cliente' => $tipo_cliente,
            'nome' => $nome,
            'cognome' => $cognome,
            'email' => $email,
            'telefono' => $telefono,
            'indirizzo' => $indirizzo,
            'cap_cliente' => $cap,
            'citta' => $citta,
            'provincia' => $provincia,
            'ragione_sociale' => $ragione_sociale,
            'partita_iva' => $partita_iva,
            'codice_fiscale' => $codice_fiscale,
            'pec' => $pec,
            'note' => $note
        ];

        if ($id) {
            // Aggiornamento
            $sql = "UPDATE clienti SET 
                    tipo_cliente = :tipo_cliente,
                    nome = :nome,
                    cognome = :cognome,
                    email = :email,
                    telefono = :telefono,
                    indirizzo = :indirizzo,
                    cap_cliente = :cap_cliente,
                    citta = :citta,
                    provincia = :provincia,
                    ragione_sociale = :ragione_sociale,
                    partita_iva = :partita_iva,
                    codice_fiscale = :codice_fiscale,
                    pec = :pec,
                    note = :note
                    WHERE id = :id";
            $params['id'] = $id;
        } else {
            // Inserimento
            $sql = "INSERT INTO clienti 
                    (tipo_cliente, nome, cognome, email, telefono, indirizzo, 
                     cap_cliente, citta, provincia, ragione_sociale, partita_iva, 
                     codice_fiscale, pec, note) 
                    VALUES 
                    (:tipo_cliente, :nome, :cognome, :email, :telefono, :indirizzo,
                     :cap_cliente, :citta, :provincia, :ragione_sociale, :partita_iva,
                     :codice_fiscale, :pec, :note)";
        }

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);

        $success_message = $id ? "Cliente aggiornato con successo." : "Cliente inserito con successo.";
        
        if (!$id) {
            $id = $conn->lastInsertId();
        }
        
        // Impostiamo il flag di redirect
        $redirect = true;
        
    } catch (PDOException $e) {
        $error_message = "Errore durante il salvataggio: " . $e->getMessage();
    }
}

// Include l'header solo se non stiamo per fare un redirect
if (!$redirect) {
    require_once ROOT_PATH . '/includes/header.php';
?>

<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1><?php echo $id ? 'Modifica Cliente' : 'Nuovo Cliente'; ?></h1>
        </div>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-danger"><?php echo htmlspecialchars($error_message); ?></div>
    <?php endif; ?>

    <?php if ($success_message): ?>
        <div class="alert alert-success"><?php echo htmlspecialchars($success_message); ?></div>
    <?php endif; ?>

    <form method="POST" class="needs-validation" novalidate>
        <?php if ($id): ?>
            <input type="hidden" name="id" value="<?php echo $id; ?>">
        <?php endif; ?>

        <div class="row mb-3">
            <div class="col-md-4">
                <label for="tipo_cliente" class="form-label">Tipo Cliente</label>
                <select class="form-select" id="tipo_cliente" name="tipo_cliente" required>
                    <option value="privato" <?php echo ($cliente['tipo_cliente'] ?? 'privato') === 'privato' ? 'selected' : ''; ?>>Privato</option>
                    <option value="societa" <?php echo ($cliente['tipo_cliente'] ?? '') === 'societa' ? 'selected' : ''; ?>>Società</option>
                </select>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="nome" class="form-label">Nome</label>
                <input type="text" class="form-control" id="nome" name="nome" 
                       value="<?php echo htmlspecialchars($cliente['nome'] ?? ''); ?>" required>
            </div>
            <div class="col-md-6 mb-3">
                <label for="cognome" class="form-label">Cognome</label>
                <input type="text" class="form-control" id="cognome" name="cognome" 
                       value="<?php echo htmlspecialchars($cliente['cognome'] ?? ''); ?>" required>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" 
                       value="<?php echo htmlspecialchars($cliente['email'] ?? ''); ?>">
            </div>
            <div class="col-md-6 mb-3">
                <label for="telefono" class="form-label">Telefono</label>
                <input type="tel" class="form-control" id="telefono" name="telefono" 
                       value="<?php echo htmlspecialchars($cliente['telefono'] ?? ''); ?>">
            </div>
        </div>

        <div class="row">
            <div class="col-12 mb-3">
                <label for="indirizzo" class="form-label">Indirizzo</label>
                <input type="text" class="form-control" id="indirizzo" name="indirizzo" 
                       value="<?php echo htmlspecialchars($cliente['indirizzo'] ?? ''); ?>">
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="cap" class="form-label">CAP</label>
                <input type="text" class="form-control" id="cap" name="cap" 
                       value="<?php echo htmlspecialchars($cliente['cap_cliente'] ?? ''); ?>">
            </div>
            <div class="col-md-4 mb-3">
                <label for="citta" class="form-label">Città</label>
                <input type="text" class="form-control" id="citta" name="citta" 
                       value="<?php echo htmlspecialchars($cliente['citta'] ?? ''); ?>">
            </div>
            <div class="col-md-4 mb-3">
                <label for="provincia" class="form-label">Provincia</label>
                <input type="text" class="form-control" id="provincia" name="provincia" maxlength="2" 
                       value="<?php echo htmlspecialchars($cliente['provincia'] ?? ''); ?>">
            </div>
        </div>

        <div id="dati-societa" class="<?php echo ($cliente['tipo_cliente'] ?? 'privato') === 'societa' ? '' : 'd-none'; ?>">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="ragione_sociale" class="form-label">Ragione Sociale</label>
                    <input type="text" class="form-control" id="ragione_sociale" name="ragione_sociale" 
                           value="<?php echo htmlspecialchars($cliente['ragione_sociale'] ?? ''); ?>">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="partita_iva" class="form-label">Partita IVA</label>
                    <input type="text" class="form-control" id="partita_iva" name="partita_iva" 
                           value="<?php echo htmlspecialchars($cliente['partita_iva'] ?? ''); ?>">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="codice_fiscale" class="form-label">Codice Fiscale</label>
                <input type="text" class="form-control" id="codice_fiscale" name="codice_fiscale" 
                       value="<?php echo htmlspecialchars($cliente['codice_fiscale'] ?? ''); ?>">
            </div>
            <div class="col-md-6 mb-3">
                <label for="pec" class="form-label">PEC</label>
                <input type="email" class="form-control" id="pec" name="pec" 
                       value="<?php echo htmlspecialchars($cliente['pec'] ?? ''); ?>">
            </div>
        </div>

        <div class="row">
            <div class="col-12 mb-3">
                <label for="note" class="form-label">Note</label>
                <textarea class="form-control" id="note" name="note" rows="3"><?php echo htmlspecialchars($cliente['note'] ?? ''); ?></textarea>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col d-flex gap-2">
                <button type="submit" class="btn-custom btn-custom-primary">
                    <i class="fas fa-save me-2"></i>
                    Salva
                </button>
                <button type="button" onclick="window.location.href='index.php'" class="btn-custom btn-custom-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Annulla
                </button>
            </div>
        </div>
    </form>
</div>

<style>
.btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.3rem;
    min-width: 120px;
}
</style>

<script>
document.getElementById('tipo_cliente').addEventListener('change', function() {
    const datiSocieta = document.getElementById('dati-societa');
    if (this.value === 'societa') {
        datiSocieta.classList.remove('d-none');
    } else {
        datiSocieta.classList.add('d-none');
    }
});

// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms)
        .forEach(function (form) {
            form.addEventListener('submit', function (event) {
                if (!form.checkValidity()) {
                    event.preventDefault()
                    event.stopPropagation()
                }
                form.classList.add('was-validated')
            }, false)
        })
})()
</script>

<?php 
    require_once ROOT_PATH . '/includes/footer.php';
} else {
    // Redirect dopo il salvataggio
    header("Location: index.php?success=" . urlencode($success_message));
    exit;
}
?>