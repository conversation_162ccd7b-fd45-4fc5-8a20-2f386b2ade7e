<?php
require_once '../config.php';

try {
    $sql = "SELECT * FROM clienti ORDER BY 
            CASE 
                WHEN tipo_cliente = 'privato' THEN CONCAT(cognome, ' ', nome)
                ELSE ragione_sociale 
            END";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $clienti = $stmt->fetchAll(PDO::FETCH_ASSOC);

    sendResponse([
        'success' => true,
        'clienti' => $clienti
    ]);
} catch (PDOException $e) {
    sendError('Errore nel recupero dei clienti: ' . $e->getMessage());
} 