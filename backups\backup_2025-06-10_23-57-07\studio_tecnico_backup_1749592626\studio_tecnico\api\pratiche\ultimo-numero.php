<?php
require_once '../config.php';

try {
    if (empty($_GET['tipo']) || empty($_GET['anno'])) {
        sendError('Tipo e anno sono obbligatori');
    }

    $sql = "SELECT MAX(CAST(SUBSTRING_INDEX(numero_pratica, '/', -1) AS UNSIGNED)) as ultimo_numero 
            FROM pratiche 
            WHERE tipo_documento = ? 
            AND numero_pratica LIKE ?";

    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $_GET['tipo'],
        $_GET['tipo'] . $_GET['anno'] . '/%'
    ]);
    
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    sendResponse([
        'success' => true,
        'ultimo_numero' => $result['ultimo_numero'] ?? 0
    ]);
} catch (PDOException $e) {
    sendError('Errore nel recupero dell\'ultimo numero: ' . $e->getMessage());
} 