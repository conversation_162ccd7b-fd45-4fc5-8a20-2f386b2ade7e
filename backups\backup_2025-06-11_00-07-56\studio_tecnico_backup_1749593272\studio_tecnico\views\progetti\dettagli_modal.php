<?php
// Vista semplificata per il modal dei dettagli progetto
?>
<div class="container-fluid p-3">
    <div class="row mb-3">
        <div class="col-md-6">
            <h5>Informazioni Progetto</h5>
            <p><strong>Nome:</strong> <?= htmlspecialchars($progetto['nome_progetto']) ?></p>
            <p><strong>Cliente:</strong> 
                <?php if ($progetto['tipo_cliente'] === 'privato'): ?>
                    <?= htmlspecialchars($progetto['cliente_nome'] . ' ' . $progetto['cliente_cognome']) ?>
                <?php else: ?>
                    <?= htmlspecialchars($progetto['cliente_ragione_sociale']) ?>
                <?php endif; ?>
            </p>
            <p><strong>Data Inizio:</strong> <?= date('d/m/Y', strtotime($progetto['data_inizio'])) ?></p>
            <p><strong>Data Fine Prevista:</strong> <?= date('d/m/Y', strtotime($progetto['data_fine_prevista'])) ?></p>
        </div>
        <div class="col-md-6">
            <h5>Dettagli Tecnici</h5>
            <p><strong>Stato:</strong> 
                <span class="badge bg-<?= $progetto['stato'] === 'completato' ? 'success' : 
                                    ($progetto['stato'] === 'in_corso' ? 'warning' : 'secondary') ?>">
                    <?= ucfirst(str_replace('_', ' ', $progetto['stato'])) ?>
                </span>
            </p>
            <p><strong>Tipo Progetto:</strong> <?= htmlspecialchars($progetto['tipo_progetto'] ?? 'Non specificato') ?></p>
            <p><strong>Comune:</strong> <?= htmlspecialchars($progetto['comune'] ?? 'Non specificato') ?></p>
            <p><strong>Indirizzo:</strong> <?= htmlspecialchars($progetto['indirizzo_progetto'] ?? 'Non specificato') ?></p>
        </div>
    </div>
    <?php if (!empty($progetto['descrizione'])): ?>
    <div class="row mb-3">
        <div class="col-12">
            <h5>Descrizione</h5>
            <p><?= nl2br(htmlspecialchars($progetto['descrizione'])) ?></p>
        </div>
    </div>
    <?php endif; ?>
    <div class="row">
        <div class="col-12 text-end">
            <a href="<?= BASE_URL ?>progetti/dettagli/<?= $progetto['id'] ?>" class="btn btn-primary" target="_blank">
                <i class="fas fa-external-link-alt"></i> Apri in Nuova Finestra
            </a>
        </div>
    </div>
</div>
