<?php
$pageTitle = 'Progetti';
include VIEWS_DIR . '/layouts/header.php';
?>
<link rel="stylesheet" href="<?= BASE_URL ?>assets/css/management.css">

<div class="container-fluid py-4">
    <div class="page-header">
        <h2 class="page-title">
            <i class="fas fa-project-diagram me-2"></i>
            Gestione Progetti
        </h2>
        <div class="page-actions">
            <?php 
            $guideTitle = "Guida Rapida - Gestione Progetti";
            $guideContent = '
                <div class="guide-section mb-4">
                    <h6 class="fw-bold"><i class="fas fa-project-diagram me-2"></i>Gestione Progetti</h6>
                    <p>Questa sezione ti permette di gestire tutti i progetti dello studio:</p>
                    <ul class="list-unstyled ps-3">
                        <li><i class="fas fa-plus text-success me-2"></i><strong>Nuovo Progetto:</strong> Crea un nuovo progetto e associalo a un cliente</li>
                        <li><i class="fas fa-edit text-primary me-2"></i><strong>Modifica:</strong> Aggiorna i dettagli e lo stato dei progetti</li>
                        <li><i class="fas fa-tasks text-info me-2"></i><strong>Stato:</strong> Monitora l\'avanzamento dei progetti</li>
                        <li><i class="fas fa-file-alt text-warning me-2"></i><strong>Pratiche:</strong> Gestisci le pratiche associate</li>
                    </ul>
                </div>
                <div class="guide-section">
                    <h6 class="fw-bold"><i class="fas fa-lightbulb me-2"></i>Suggerimenti</h6>
                    <ul class="list-unstyled ps-3">
                        <li><i class="fas fa-check text-success me-2"></i>Aggiorna regolarmente lo stato dei progetti</li>
                        <li><i class="fas fa-check text-success me-2"></i>Verifica le scadenze delle pratiche associate</li>
                        <li><i class="fas fa-check text-success me-2"></i>Mantieni aggiornata la documentazione</li>
                        <li><i class="fas fa-check text-success me-2"></i>Controlla periodicamente il budget e le tempistiche</li>
                    </ul>
                </div>
            ';
            include VIEWS_DIR . '/components/quick_guide.php'; 
            ?>
            <a href="<?= BASE_URL ?>progetti/nuovo" class="btn btn-neutral">
                <i class="fas fa-plus"></i>
                Nuovo Progetto
            </a>
        </div>
    </div>

    <?php if (isset($_GET['msg'])): ?>
        <?php if ($_GET['msg'] === 'success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Progetto inserito con successo!
            </div>
        <?php elseif ($_GET['msg'] === 'update_success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Progetto aggiornato con successo!
            </div>
        <?php elseif ($_GET['msg'] === 'delete_success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Progetto eliminato con successo!
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <div class="container mt-4">
        <?php if (isset($_GET['error'])): ?>
            <?php 
            $errorMessage = '';
            switch ($_GET['error']) {
                case 'progetto_non_trovato':
                    $errorMessage = 'Progetto non trovato.';
                    break;
                case 'impossibile_eliminare_progetto_con_pratiche':
                    $errorMessage = 'Impossibile eliminare il progetto perché ha delle pratiche collegate.';
                    break;
                case 'errore_eliminazione':
                    $errorMessage = 'Si è verificato un errore durante l\'eliminazione del progetto.';
                    break;
                default:
                    $errorMessage = 'Si è verificato un errore.';
            }
            ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $errorMessage ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['success'])): ?>
            <?php 
            $successMessage = '';
            switch ($_GET['success']) {
                case 'progetto_eliminato':
                    $successMessage = 'Progetto eliminato con successo.';
                    break;
                default:
                    $successMessage = 'Operazione completata con successo.';
            }
            ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $successMessage ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
    </div>

    <?php if (empty($progetti)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            Nessun progetto trovato.
        </div>
    <?php else: ?>
        <div class="table-card">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Nome Progetto</th>
                            <th>Cliente</th>
                            <th>Stato</th>
                            <th>Data Inizio</th>
                            <th>Data Fine</th>
                            <th>Budget</th>
                            <th>Pratiche</th>
                            <th class="text-end">Azioni</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($progetti as $progetto): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($progetto['nome_progetto']); ?></td>
                                <td>
                                    <?php 
                                    if (isset($progetto['tipo_cliente']) && $progetto['tipo_cliente'] === 'privato') {
                                        echo htmlspecialchars($progetto['cliente_nome'] . ' ' . $progetto['cliente_cognome']);
                                    } else {
                                        echo htmlspecialchars($progetto['cliente_ragione_sociale'] ?? 'N/D');
                                    }
                                    ?>
                                </td>
                                <td>
                                    <span class="badge <?php 
                                        echo isset($progetto['stato']) ? 
                                            ($progetto['stato'] === 'completato' ? 'bg-success' : 
                                            ($progetto['stato'] === 'in_corso' ? 'bg-primary' : 
                                            ($progetto['stato'] === 'sospeso' ? 'bg-warning' : 'bg-secondary'))) 
                                            : 'bg-secondary'; ?>">
                                        <?php echo isset($progetto['stato']) ? ucfirst(str_replace('_', ' ', $progetto['stato'])) : 'N/D'; ?>
                                    </span>
                                </td>
                                <td><?php echo isset($progetto['data_inizio']) ? date('d/m/Y', strtotime($progetto['data_inizio'])) : 'N/D'; ?></td>
                                <td>
                                    <?php 
                                    if (isset($progetto['data_fine']) && !empty($progetto['data_fine'])) {
                                        echo date('d/m/Y', strtotime($progetto['data_fine']));
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </td>
                                <td>€ <?php echo isset($progetto['budget']) ? number_format((float)$progetto['budget'], 2, ',', '.') : '0,00'; ?></td>
                                <td>
                                    <span class="badge bg-info">
                                        <?php echo isset($progetto['num_pratiche']) ? $progetto['num_pratiche'] : '0'; ?> pratiche
                                    </span>
                                </td>
                                <td class="text-end">
                                    <div class="btn-group">
                                        <a href="<?= BASE_URL ?>progetti/dettagli/<?= $progetto['id'] ?>" class="btn btn-sm btn-outline-primary" title="Visualizza">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= BASE_URL ?>progetti/modifica/<?= $progetto['id'] ?>" class="btn btn-sm btn-outline-neutral" title="Modifica">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="javascript:void(0)" onclick="confirmDelete(<?= $progetto['id'] ?>)" class="btn btn-sm btn-outline-danger" title="Elimina">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// Definisci BASE_URL per JavaScript
const BASE_URL = '<?= BASE_URL ?>';

function confirmDelete(id) {
    Swal.fire({
        title: 'Sei sicuro?',
        text: "Questa azione non può essere annullata!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Sì, elimina!',
        cancelButtonText: 'Annulla'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `${BASE_URL}progetti/elimina/${id}`;
        }
    });
}
</script>

<?php
include VIEWS_DIR . '/layouts/footer.php';
?>