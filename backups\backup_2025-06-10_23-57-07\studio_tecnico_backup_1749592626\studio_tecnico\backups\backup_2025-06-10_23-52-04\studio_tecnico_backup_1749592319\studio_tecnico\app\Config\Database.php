<?php
namespace App\Config;

use PDO;
use PDOException;

class Database {
    private static $instance = null;
    private $connection = null;

    private function __construct() {
        try {
            if (!defined('ROOT_PATH')) {
                throw new \Exception("ROOT_PATH non è definito");
            }

            $configFile = ROOT_PATH . '/config/database.php';
            if (!file_exists($configFile)) {
                throw new \Exception("File di configurazione database non trovato: {$configFile}");
            }

            $config = require $configFile;
            if (!isset($config['db'])) {
                throw new \Exception("Configurazione del database non trovata nel file {$configFile}");
            }
            $dbConfig = $config['db'];
            
            // Verifica che tutte le chiavi necessarie siano presenti
            $requiredKeys = ['host', 'dbname', 'user', 'pass', 'charset', 'options'];
            foreach ($requiredKeys as $key) {
                if (!isset($dbConfig[$key])) {
                    throw new \Exception("Chiave di configurazione mancante: {$key}");
                }
            }
            
            $dsn = sprintf(
                "mysql:host=%s;dbname=%s;charset=%s",
                $dbConfig['host'],
                $dbConfig['dbname'],
                $dbConfig['charset']
            );

            try {
                $this->connection = new PDO(
                    $dsn,
                    $dbConfig['user'],
                    $dbConfig['pass'],
                    $dbConfig['options']
                );
                error_log("Connessione al database stabilita con successo");
            } catch (PDOException $e) {
                error_log(sprintf(
                    "Errore di connessione al database: %s. DSN: %s, User: %s",
                    $e->getMessage(),
                    $dsn,
                    $dbConfig['user']
                ));
                throw new PDOException("Errore di connessione al database. Controlla il log per i dettagli.");
            }
        } catch (\Exception $e) {
            error_log("Errore nell'inizializzazione del database: " . $e->getMessage());
            throw $e;
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance->connection;
    }

    public function getConnection() {
        return $this->connection;
    }

    // Previene la clonazione dell'istanza
    private function __clone() {}

    // Previene la deserializzazione dell'istanza
    public function __wakeup() {
        throw new \Exception("Cannot unserialize singleton");
    }
}
