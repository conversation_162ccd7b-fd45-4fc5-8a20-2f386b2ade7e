<?php 
use App\Core\Security;
include VIEWS_DIR . '/layouts/header.php'; 

// Estrai i dati dall'array data
$pratica = $data['pratica'] ?? null;
$error = $data['error'] ?? null;

if ($error) {
    echo '<div class="alert alert-danger">' . htmlspecialchars($error) . '</div>';
}

if (!$pratica) {
    echo '<div class="alert alert-danger">Pratica non trovata</div>';
    exit;
}
?>
<link rel="stylesheet" href="<?= BASE_URL ?>assets/css/management.css">

<div class="container-fluid py-4">
    <div class="page-header">
        <h2 class="page-title">
            <i class="fas fa-file-edit"></i>
            Modifica Pratica
        </h2>
        <div class="page-actions">
            <a href="<?= BASE_URL ?>pratiche" class="btn btn-outline-neutral">
                <i class="fas fa-arrow-left"></i>
                Torna alla lista
            </a>
        </div>
    </div>

    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="form-card">
                <form action="<?php echo BASE_URL; ?>pratiche/modifica/<?php echo htmlspecialchars($pratica['id']); ?>" method="POST" id="praticaForm">
                    <?php echo Security::csrfField(); ?>
                    <input type="hidden" name="id" value="<?php echo htmlspecialchars($pratica['id']); ?>">

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-info-circle"></i>
                            Informazioni Base
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="numero_pratica" class="form-label">Numero Pratica *</label>
                                <input type="text" class="form-control" id="numero_pratica" name="numero_pratica" 
                                       value="<?php echo isset($pratica['numero_pratica']) ? htmlspecialchars($pratica['numero_pratica']) : ''; ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Cliente</label>
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($pratica['cliente_nome']); ?>" readonly>
                                <input type="hidden" name="progetto_id" value="<?php echo htmlspecialchars($pratica['progetto_id']); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-project-diagram"></i>
                            Dettagli Pratica
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="tipo_pratica" class="form-label">Tipo Pratica *</label>
                                <select class="form-select" id="tipo_pratica" name="tipo_pratica" required>
                                    <option value="">Seleziona Tipo</option>
                                    <option value="cila" <?php echo isset($pratica['tipo_pratica']) && $pratica['tipo_pratica'] === 'cila' ? 'selected' : ''; ?>>CILA</option>
                                    <option value="scia" <?php echo isset($pratica['tipo_pratica']) && $pratica['tipo_pratica'] === 'scia' ? 'selected' : ''; ?>>SCIA</option>
                                    <option value="permesso_costruire" <?php echo isset($pratica['tipo_pratica']) && $pratica['tipo_pratica'] === 'permesso_costruire' ? 'selected' : ''; ?>>Permesso di Costruire</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="stato" class="form-label">Stato *</label>
                                <select class="form-select" id="stato" name="stato" required>
                                    <option value="">Seleziona Stato</option>
                                    <option value="in_attesa" <?php echo isset($pratica['stato']) && $pratica['stato'] === 'in_attesa' ? 'selected' : ''; ?>>In Attesa</option>
                                    <option value="in_corso" <?php echo isset($pratica['stato']) && $pratica['stato'] === 'in_corso' ? 'selected' : ''; ?>>In Corso</option>
                                    <option value="completata" <?php echo isset($pratica['stato']) && $pratica['stato'] === 'completata' ? 'selected' : ''; ?>>Completata</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="descrizione" class="form-label">Descrizione *</label>
                                <textarea class="form-control" id="descrizione" name="descrizione" rows="3" required><?php echo isset($pratica['descrizione']) ? htmlspecialchars($pratica['descrizione']) : ''; ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Salva Modifiche
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>