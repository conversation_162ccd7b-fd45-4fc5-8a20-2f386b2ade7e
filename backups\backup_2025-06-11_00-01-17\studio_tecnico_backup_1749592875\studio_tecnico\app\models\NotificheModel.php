<?php
// app/models/NotificheModel.php - Model per la gestione delle notifiche
namespace App\Models;

use App\Config\Database;
use PDO;
use PDOException;
use DateTime;

class NotificheModel {
    private PDO $db;

    public function __construct() {
        try {
            $this->db = Database::getInstance();
            if (!$this->db instanceof PDO) {
                throw new PDOException("Connessione al database non valida nel NotificheModel.");
            }
        } catch (PDOException $e) {
            error_log("Errore connessione DB in NotificheModel: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Crea una nuova notifica
     */
    public function insertNotifica(array $data): int|false {
        try {
            error_log("NotificheModel::insertNotifica() - <PERSON><PERSON> ricevuti: " . print_r($data, true));

            $query = "INSERT INTO notifiche (
                user_id, tipo, titolo, messaggio, priorita,
                link_azione, metadata, data_creazione
            ) VALUES (
                :user_id, :tipo, :titolo, :messaggio, :priorita,
                :link_azione, :metadata, NOW()
            )";

            $stmt = $this->db->prepare($query);
            $allowed_keys = [
                'user_id', 'tipo', 'titolo', 'messaggio', 'priorita',
                'link_azione', 'metadata'
            ];

            $filtered_data = array_intersect_key($data, array_flip($allowed_keys));

            // Converte metadata in JSON se è un array
            if (isset($filtered_data['metadata']) && is_array($filtered_data['metadata'])) {
                $filtered_data['metadata'] = json_encode($filtered_data['metadata']);
            }

            error_log("NotificheModel::insertNotifica() - Dati filtrati: " . print_r($filtered_data, true));

            $success = $stmt->execute($filtered_data);
            if ($success) {
                $id = $this->db->lastInsertId();
                error_log("NotificheModel::insertNotifica() - Notifica creata con ID: " . $id);
                return $id;
            } else {
                error_log("NotificheModel::insertNotifica() - Errore SQL: " . print_r($stmt->errorInfo(), true));
                return false;
            }

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::insertNotifica(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Crea notifica automatica per scadenza
     */
    public function createNotificaScadenza(int $scadenza_id, int $user_id = 1): bool {
        try {
            // Recupera informazioni sulla scadenza
            $stmt = $this->db->prepare("
                SELECT s.*, p.nome_progetto, c.nome as cliente_nome, c.cognome as cliente_cognome
                FROM scadenze s
                LEFT JOIN progetti p ON s.progetto_id = p.id
                LEFT JOIN clienti c ON p.cliente_id = c.id
                WHERE s.id = :scadenza_id
            ");
            $stmt->execute([':scadenza_id' => $scadenza_id]);
            $scadenza = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$scadenza) {
                error_log("NotificheModel::createNotificaScadenza() - Scadenza non trovata: " . $scadenza_id);
                return false;
            }

            $dataScadenza = new DateTime($scadenza['data_scadenza']);
            $oggi = new DateTime();
            $giorni = $oggi->diff($dataScadenza)->days;

            // Determina priorità in base ai giorni rimanenti
            $priorita = 'bassa';
            if ($giorni <= 1) $priorita = 'alta';
            elseif ($giorni <= 7) $priorita = 'media';

            $data = [
                'user_id' => $user_id,
                'tipo' => 'scadenza',
                'titolo' => 'Scadenza in arrivo: ' . $scadenza['titolo'],
                'messaggio' => sprintf(
                    'La scadenza "%s" del progetto "%s" è prevista per il %s (%d giorni)',
                    $scadenza['titolo'],
                    $scadenza['nome_progetto'] ?? 'Progetto sconosciuto',
                    $dataScadenza->format('d/m/Y'),
                    $giorni
                ),
                'priorita' => $priorita,
                'link_azione' => '/scadenze/dettagli/' . $scadenza_id,
                'metadata' => [
                    'scadenza_id' => $scadenza_id,
                    'progetto_id' => $scadenza['progetto_id'],
                    'giorni_rimanenti' => $giorni
                ]
            ];

            return $this->insertNotifica($data) !== false;

        } catch (\Exception $e) {
            error_log("Errore in NotificheModel::createNotificaScadenza(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Crea notifica automatica per pratica
     */
    public function createNotificaPratica(int $pratica_id, string $tipo, int $user_id = 1): bool {
        try {
            // Recupera informazioni sulla pratica
            $stmt = $this->db->prepare("
                SELECT pr.*, p.nome_progetto, c.nome as cliente_nome, c.cognome as cliente_cognome
                FROM pratiche pr
                LEFT JOIN progetti p ON pr.progetto_id = p.id
                LEFT JOIN clienti c ON p.cliente_id = c.id
                WHERE pr.id = :pratica_id
            ");
            $stmt->execute([':pratica_id' => $pratica_id]);
            $pratica = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$pratica) {
                error_log("NotificheModel::createNotificaPratica() - Pratica non trovata: " . $pratica_id);
                return false;
            }

            $titoli = [
                'stato_cambiato' => 'Cambio stato pratica',
                'scadenza_vicina' => 'Scadenza pratica in arrivo',
                'documento_richiesto' => 'Documento richiesto per pratica',
                'pratica_completata' => 'Pratica completata'
            ];

            $messaggi = [
                'stato_cambiato' => sprintf(
                    'La pratica "%s" del progetto "%s" ha cambiato stato in: %s',
                    $pratica['numero_pratica'] ?? 'N/A',
                    $pratica['nome_progetto'] ?? 'Progetto sconosciuto',
                    $pratica['stato']
                ),
                'scadenza_vicina' => sprintf(
                    'La pratica "%s" del progetto "%s" scade il %s',
                    $pratica['numero_pratica'] ?? 'N/A',
                    $pratica['nome_progetto'] ?? 'Progetto sconosciuto',
                    $pratica['data_scadenza'] ? date('d/m/Y', strtotime($pratica['data_scadenza'])) : 'Data non definita'
                ),
                'documento_richiesto' => sprintf(
                    'È richiesto un documento per la pratica "%s" del progetto "%s"',
                    $pratica['numero_pratica'] ?? 'N/A',
                    $pratica['nome_progetto'] ?? 'Progetto sconosciuto'
                ),
                'pratica_completata' => sprintf(
                    'La pratica "%s" del progetto "%s" è stata completata',
                    $pratica['numero_pratica'] ?? 'N/A',
                    $pratica['nome_progetto'] ?? 'Progetto sconosciuto'
                )
            ];

            $data = [
                'user_id' => $user_id,
                'tipo' => 'pratica',
                'titolo' => $titoli[$tipo] ?? 'Aggiornamento pratica',
                'messaggio' => $messaggi[$tipo] ?? 'Aggiornamento sulla pratica',
                'priorita' => ($tipo === 'scadenza_vicina') ? 'alta' : 'media',
                'link_azione' => '/pratiche/dettagli/' . $pratica_id,
                'metadata' => [
                    'pratica_id' => $pratica_id,
                    'progetto_id' => $pratica['progetto_id'],
                    'tipo_notifica' => $tipo
                ]
            ];

            return $this->insertNotifica($data) !== false;

        } catch (\Exception $e) {
            error_log("Errore in NotificheModel::createNotificaPratica(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Recupera notifiche non lette per un utente
     */
    public function getNotificheNonLette(int $user_id, int $limit = 50): array {
        try {
            $query = "
                SELECT id, tipo, titolo, messaggio, priorita, link_azione,
                       data_creazione, metadata
                FROM notifiche
                WHERE user_id = :user_id AND letta = FALSE
                ORDER BY
                    CASE priorita
                        WHEN 'alta' THEN 1
                        WHEN 'media' THEN 2
                        ELSE 3
                    END,
                    data_creazione DESC
                LIMIT :limit
            ";

            $stmt = $this->db->prepare($query);
            $stmt->bindValue(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::getNotificheNonLette(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Recupera tutte le notifiche per un utente con filtri
     */
    public function getAllNotificheUtente(int $user_id, array $filters = []): array {
        try {
            $query = "
                SELECT id, tipo, titolo, messaggio, priorita, link_azione,
                       data_creazione, data_lettura, letta, metadata
                FROM notifiche
                WHERE user_id = :user_id
            ";

            $params = [':user_id' => $user_id];

            // Filtro per stato lettura
            if (isset($filters['letta'])) {
                $query .= " AND letta = :letta";
                $params[':letta'] = $filters['letta'] ? 1 : 0;
            }

            // Filtro per tipo
            if (!empty($filters['tipo'])) {
                $query .= " AND tipo = :tipo";
                $params[':tipo'] = $filters['tipo'];
            }

            // Filtro per priorità
            if (!empty($filters['priorita'])) {
                $query .= " AND priorita = :priorita";
                $params[':priorita'] = $filters['priorita'];
            }

            $query .= " ORDER BY data_creazione DESC";

            // Limite risultati
            if (!empty($filters['limit'])) {
                $query .= " LIMIT :limit";
                $params[':limit'] = (int)$filters['limit'];
            }

            $stmt = $this->db->prepare($query);

            // Bind dei parametri con tipo corretto
            foreach ($params as $key => $value) {
                if ($key === ':limit') {
                    $stmt->bindValue($key, $value, PDO::PARAM_INT);
                } else {
                    $stmt->bindValue($key, $value);
                }
            }

            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::getAllNotificheUtente(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Recupera preferenza email per un utente e tipo notifica
     */
    public function getPreferenzaEmail(int $user_id, string $tipo_notifica): bool {
        try {
            $stmt = $this->db->prepare("
                SELECT email_enabled
                FROM notifiche_preferenze
                WHERE user_id = :user_id AND tipo_notifica = :tipo_notifica
            ");
            $stmt->execute([
                ':user_id' => $user_id,
                ':tipo_notifica' => $tipo_notifica
            ]);

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? (bool)$result['email_enabled'] : true; // Default: email abilitata

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::getPreferenzaEmail(): " . $e->getMessage());
            return true; // Default: email abilitata
        }
    }

    /**
     * Imposta preferenza utente per tipo notifica
     */
    public function setPreferenzaUtente(int $user_id, string $tipo_notifica, bool $email_enabled, bool $push_enabled = true, int $soglia_giorni = 7): bool {
        try {
            $query = "
                INSERT INTO notifiche_preferenze
                (user_id, tipo_notifica, email_enabled, push_enabled, soglia_giorni)
                VALUES (:user_id, :tipo_notifica, :email_enabled, :push_enabled, :soglia_giorni)
                ON DUPLICATE KEY UPDATE
                email_enabled = VALUES(email_enabled),
                push_enabled = VALUES(push_enabled),
                soglia_giorni = VALUES(soglia_giorni)
            ";

            $stmt = $this->db->prepare($query);
            return $stmt->execute([
                ':user_id' => $user_id,
                ':tipo_notifica' => $tipo_notifica,
                ':email_enabled' => $email_enabled ? 1 : 0,
                ':push_enabled' => $push_enabled ? 1 : 0,
                ':soglia_giorni' => $soglia_giorni
            ]);

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::setPreferenzaUtente(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Marca una notifica come letta
     */
    public function markAsRead(int $notifica_id, int $user_id): bool {
        try {
            $stmt = $this->db->prepare("
                UPDATE notifiche
                SET letta = TRUE, data_lettura = NOW()
                WHERE id = :notifica_id AND user_id = :user_id
            ");

            $success = $stmt->execute([
                ':notifica_id' => $notifica_id,
                ':user_id' => $user_id
            ]);

            if ($success) {
                error_log("NotificheModel::markAsRead() - Notifica {$notifica_id} marcata come letta per utente {$user_id}");
            }

            return $success;

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::markAsRead(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Marca tutte le notifiche come lette per un utente
     */
    public function markAllAsRead(int $user_id): bool {
        try {
            $stmt = $this->db->prepare("
                UPDATE notifiche
                SET letta = TRUE, data_lettura = NOW()
                WHERE user_id = :user_id AND letta = FALSE
            ");

            $success = $stmt->execute([':user_id' => $user_id]);

            if ($success) {
                $count = $stmt->rowCount();
                error_log("NotificheModel::markAllAsRead() - {$count} notifiche marcate come lette per utente {$user_id}");
            }

            return $success;

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::markAllAsRead(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Elimina una notifica specifica
     */
    public function deleteNotifica(int $notifica_id, int $user_id): bool {
        try {
            $stmt = $this->db->prepare("
                DELETE FROM notifiche
                WHERE id = :notifica_id AND user_id = :user_id
            ");

            $success = $stmt->execute([
                ':notifica_id' => $notifica_id,
                ':user_id' => $user_id
            ]);

            if ($success && $stmt->rowCount() > 0) {
                error_log("NotificheModel::deleteNotifica() - Notifica {$notifica_id} eliminata per utente {$user_id}");
                return true;
            }

            return false;

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::deleteNotifica(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Elimina notifiche vecchie (pulizia automatica)
     */
    public function deleteOldNotifiche(int $giorni = 30): int {
        try {
            $stmt = $this->db->prepare("
                DELETE FROM notifiche
                WHERE data_creazione < DATE_SUB(NOW(), INTERVAL :giorni DAY)
                AND letta = TRUE
            ");

            $stmt->execute([':giorni' => $giorni]);
            $deleted = $stmt->rowCount();

            if ($deleted > 0) {
                error_log("NotificheModel::deleteOldNotifiche() - Eliminate {$deleted} notifiche vecchie di {$giorni} giorni");
            }

            return $deleted;

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::deleteOldNotifiche(): " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Conta notifiche non lette per un utente
     */
    public function countNotificheNonLette(int $user_id): int {
        try {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count
                FROM notifiche
                WHERE user_id = :user_id AND letta = FALSE
            ");
            $stmt->execute([':user_id' => $user_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result ? (int)$result['count'] : 0;

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::countNotificheNonLette(): " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Recupera statistiche notifiche per dashboard
     */
    public function getStatisticheNotifiche(int $user_id): array {
        try {
            $stats = [];

            // Conteggio per stato
            $stmt = $this->db->prepare("
                SELECT
                    SUM(CASE WHEN letta = FALSE THEN 1 ELSE 0 END) as non_lette,
                    SUM(CASE WHEN letta = TRUE THEN 1 ELSE 0 END) as lette,
                    COUNT(*) as totali
                FROM notifiche
                WHERE user_id = :user_id
            ");
            $stmt->execute([':user_id' => $user_id]);
            $stats['conteggi'] = $stmt->fetch(PDO::FETCH_ASSOC);

            // Conteggio per priorità (solo non lette)
            $stmt = $this->db->prepare("
                SELECT priorita, COUNT(*) as count
                FROM notifiche
                WHERE user_id = :user_id AND letta = FALSE
                GROUP BY priorita
            ");
            $stmt->execute([':user_id' => $user_id]);
            $stats['per_priorita'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // Conteggio per tipo (solo non lette)
            $stmt = $this->db->prepare("
                SELECT tipo, COUNT(*) as count
                FROM notifiche
                WHERE user_id = :user_id AND letta = FALSE
                GROUP BY tipo
            ");
            $stmt->execute([':user_id' => $user_id]);
            $stats['per_tipo'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            return $stats;

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::getStatisticheNotifiche(): " . $e->getMessage());
            return [
                'conteggi' => ['non_lette' => 0, 'lette' => 0, 'totali' => 0],
                'per_priorita' => [],
                'per_tipo' => []
            ];
        }
    }

    /**
     * Recupera tutte le preferenze per un utente
     */
    public function getPreferenzeUtente(int $user_id): array {
        try {
            $stmt = $this->db->prepare("
                SELECT tipo_notifica, email_enabled, push_enabled, soglia_giorni
                FROM notifiche_preferenze
                WHERE user_id = :user_id
            ");
            $stmt->execute([':user_id' => $user_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::getPreferenzeUtente(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Aggiorna multiple preferenze per un utente
     */
    public function updatePreferenze(int $user_id, array $preferenze): bool {
        try {
            $success = true;

            foreach ($preferenze as $tipo => $pref) {
                $result = $this->setPreferenzaUtente(
                    $user_id,
                    $tipo,
                    $pref['email'] ?? true,
                    $pref['push'] ?? true,
                    $pref['soglia_giorni'] ?? 7
                );

                if (!$result) {
                    $success = false;
                    error_log("NotificheModel::updatePreferenze() - Errore aggiornamento preferenza: {$tipo}");
                }
            }

            return $success;

        } catch (\Exception $e) {
            error_log("Errore in NotificheModel::updatePreferenze(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Recupera scadenze imminenti per generare notifiche automatiche
     */
    public function getScadenzeImminenti(int $giorni_anticipo = 7): array {
        try {
            $stmt = $this->db->prepare("
                SELECT s.id as scadenza_id, s.titolo, s.data_scadenza, s.progetto_id,
                       p.nome_progetto, c.nome as cliente_nome, c.cognome as cliente_cognome,
                       DATEDIFF(s.data_scadenza, CURDATE()) as giorni_rimanenti
                FROM scadenze s
                LEFT JOIN progetti p ON s.progetto_id = p.id
                LEFT JOIN clienti c ON p.cliente_id = c.id
                WHERE s.data_scadenza >= CURDATE()
                AND s.data_scadenza <= DATE_ADD(CURDATE(), INTERVAL :giorni_anticipo DAY)
                AND s.stato != 'completata'
                ORDER BY s.data_scadenza ASC
            ");

            $stmt->execute([':giorni_anticipo' => $giorni_anticipo]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::getScadenzeImminenti(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Recupera pratiche in scadenza per generare notifiche automatiche
     */
    public function getPraticheInScadenza(int $giorni_anticipo = 7): array {
        try {
            $stmt = $this->db->prepare("
                SELECT pr.id as pratica_id, pr.numero_pratica, pr.data_scadenza,
                       pr.data_scadenza_integrazione, pr.progetto_id,
                       p.nome_progetto, c.nome as cliente_nome, c.cognome as cliente_cognome,
                       LEAST(
                           IFNULL(DATEDIFF(pr.data_scadenza, CURDATE()), 999),
                           IFNULL(DATEDIFF(pr.data_scadenza_integrazione, CURDATE()), 999)
                       ) as giorni_rimanenti
                FROM pratiche pr
                LEFT JOIN progetti p ON pr.progetto_id = p.id
                LEFT JOIN clienti c ON p.cliente_id = c.id
                WHERE (
                    (pr.data_scadenza IS NOT NULL AND
                     pr.data_scadenza >= CURDATE() AND
                     pr.data_scadenza <= DATE_ADD(CURDATE(), INTERVAL :giorni_anticipo DAY))
                    OR
                    (pr.data_scadenza_integrazione IS NOT NULL AND
                     pr.data_scadenza_integrazione >= CURDATE() AND
                     pr.data_scadenza_integrazione <= DATE_ADD(CURDATE(), INTERVAL :giorni_anticipo DAY))
                )
                AND pr.stato NOT IN ('completata', 'archiviata')
                ORDER BY giorni_rimanenti ASC
            ");

            $stmt->execute([':giorni_anticipo' => $giorni_anticipo]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::getPraticheInScadenza(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Verifica se esiste già una notifica per una specifica scadenza/pratica
     */
    public function esisteNotifica(string $tipo, int $riferimento_id, int $user_id): bool {
        try {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count
                FROM notifiche
                WHERE user_id = :user_id
                AND tipo = :tipo
                AND JSON_EXTRACT(metadata, '$.\"{$tipo}_id\"') = :riferimento_id
                AND data_creazione >= DATE_SUB(NOW(), INTERVAL 1 DAY)
            ");

            $stmt->execute([
                ':user_id' => $user_id,
                ':tipo' => $tipo,
                ':riferimento_id' => $riferimento_id
            ]);

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result && $result['count'] > 0;

        } catch (PDOException $e) {
            error_log("Errore in NotificheModel::esisteNotifica(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Metodi di compatibilità per il codice esistente
     */

    /**
     * @deprecated Usa insertNotifica() invece
     */
    public function create(array $data): int|false {
        return $this->insertNotifica($data);
    }

    /**
     * @deprecated Usa markAsRead() invece
     */
    public function segnaComeLetta(int $notificaId, int $utenteId): bool {
        return $this->markAsRead($notificaId, $utenteId);
    }
}
