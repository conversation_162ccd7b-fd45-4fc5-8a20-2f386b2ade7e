# Specifiche Tecniche - Implementazione Miglioramenti

## 🏗️ Architettura Target

### Pattern MVC Completo
```
app/
├── models/
│   ├── ClientiModel.php ✅
│   ├── ProgettiModel.php ❌ (DA CREARE)
│   ├── PraticheModel.php ❌ (DA CREARE)
│   ├── ScadenzeModel.php ❌ (DA CREARE)
│   ├── FattureModel.php ❌ (NUOVO)
│   ├── NotificheModel.php 🔄 (DA MIGLIORARE)
│   └── User.php ✅
├── controllers/
│   ├── [Esistenti] ✅
│   ├── NotificheController.php ❌ (DA CREARE)
│   ├── FattureController.php ❌ (NUOVO)
│   └── CalendarioController.php ❌ (NUOVO)
├── services/
│   ├── NotificationService.php ❌ (NUOVO)
│   ├── BackupService.php ❌ (NUOVO)
│   ├── WorkflowService.php ❌ (NUOVO)
│   └── EmailService.php ❌ (NUOVO)
└── core/
    ├── Router.php ✅
    ├── Security.php 🔄 (DA MIGLIORARE)
    ├── Validator.php ❌ (NUOVO)
    └── Cache.php ❌ (NUOVO)
```

## 📊 Schema Database Esteso

### Nuove Tabelle Richieste

#### 1. Sistema Notifiche
```sql
CREATE TABLE notifiche (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    tipo ENUM('scadenza', 'pratica', 'progetto', 'sistema', 'fattura') NOT NULL,
    titolo VARCHAR(255) NOT NULL,
    messaggio TEXT,
    data_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_lettura TIMESTAMP NULL,
    letta BOOLEAN DEFAULT FALSE,
    priorita ENUM('bassa', 'media', 'alta') DEFAULT 'media',
    link_azione VARCHAR(255),
    metadata JSON,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_letta (user_id, letta),
    INDEX idx_data_creazione (data_creazione)
);

CREATE TABLE notifiche_preferenze (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    tipo_notifica VARCHAR(50) NOT NULL,
    email_enabled BOOLEAN DEFAULT TRUE,
    push_enabled BOOLEAN DEFAULT TRUE,
    soglia_giorni INT DEFAULT 7,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_tipo (user_id, tipo_notifica)
);
```

#### 2. Sistema Fatturazione
```sql
CREATE TABLE fatture (
    id INT PRIMARY KEY AUTO_INCREMENT,
    progetto_id INT,
    numero_fattura VARCHAR(50) UNIQUE NOT NULL,
    data_emissione DATE NOT NULL,
    data_scadenza DATE NOT NULL,
    importo_netto DECIMAL(10,2) NOT NULL,
    iva_percentuale DECIMAL(5,2) DEFAULT 22.00,
    importo_iva DECIMAL(10,2) GENERATED ALWAYS AS (importo_netto * iva_percentuale / 100) STORED,
    importo_totale DECIMAL(10,2) GENERATED ALWAYS AS (importo_netto + importo_iva) STORED,
    stato ENUM('bozza', 'emessa', 'pagata', 'scaduta', 'stornata') DEFAULT 'bozza',
    note TEXT,
    file_path VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (progetto_id) REFERENCES progetti(id) ON DELETE SET NULL,
    INDEX idx_numero_fattura (numero_fattura),
    INDEX idx_stato_scadenza (stato, data_scadenza)
);

CREATE TABLE preventivi (
    id INT PRIMARY KEY AUTO_INCREMENT,
    cliente_id INT NOT NULL,
    numero_preventivo VARCHAR(50) UNIQUE NOT NULL,
    data_creazione DATE NOT NULL,
    data_scadenza DATE NOT NULL,
    importo_totale DECIMAL(10,2) NOT NULL,
    stato ENUM('bozza', 'inviato', 'approvato', 'rifiutato', 'scaduto') DEFAULT 'bozza',
    descrizione TEXT,
    condizioni TEXT,
    validita_giorni INT DEFAULT 30,
    file_path VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (cliente_id) REFERENCES clienti(id) ON DELETE CASCADE,
    INDEX idx_numero_preventivo (numero_preventivo),
    INDEX idx_cliente_stato (cliente_id, stato)
);
```

#### 3. Sistema Calendario
```sql
CREATE TABLE eventi (
    id INT PRIMARY KEY AUTO_INCREMENT,
    titolo VARCHAR(255) NOT NULL,
    descrizione TEXT,
    data_inizio DATETIME NOT NULL,
    data_fine DATETIME NOT NULL,
    tipo ENUM('appuntamento', 'scadenza', 'riunione', 'sopralluogo', 'deadline') NOT NULL,
    cliente_id INT NULL,
    progetto_id INT NULL,
    pratica_id INT NULL,
    user_id INT NOT NULL,
    colore VARCHAR(7) DEFAULT '#007bff',
    ricorrente BOOLEAN DEFAULT FALSE,
    ricorrenza_tipo ENUM('giornaliera', 'settimanale', 'mensile', 'annuale') NULL,
    ricorrenza_fine DATE NULL,
    reminder_minuti INT DEFAULT 30,
    location VARCHAR(255),
    partecipanti JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (cliente_id) REFERENCES clienti(id) ON DELETE SET NULL,
    FOREIGN KEY (progetto_id) REFERENCES progetti(id) ON DELETE SET NULL,
    FOREIGN KEY (pratica_id) REFERENCES pratiche(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_data_inizio (data_inizio),
    INDEX idx_user_data (user_id, data_inizio)
);
```

#### 4. Sistema Backup
```sql
CREATE TABLE backup_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nome VARCHAR(100) NOT NULL,
    tipo ENUM('completo', 'incrementale', 'database', 'files') NOT NULL,
    frequenza ENUM('giornaliero', 'settimanale', 'mensile', 'manuale') NOT NULL,
    ora_esecuzione TIME DEFAULT '02:00:00',
    destinazione VARCHAR(255) NOT NULL,
    compressione BOOLEAN DEFAULT TRUE,
    crittografia BOOLEAN DEFAULT FALSE,
    retention_giorni INT DEFAULT 30,
    attivo BOOLEAN DEFAULT TRUE,
    ultima_esecuzione TIMESTAMP NULL,
    prossima_esecuzione TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE backup_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_id INT NOT NULL,
    data_inizio TIMESTAMP NOT NULL,
    data_fine TIMESTAMP NULL,
    stato ENUM('in_corso', 'successo', 'errore', 'interrotto') NOT NULL,
    dimensione_mb DECIMAL(10,2) NULL,
    file_path VARCHAR(500),
    messaggio_errore TEXT,
    dettagli JSON,
    FOREIGN KEY (config_id) REFERENCES backup_config(id) ON DELETE CASCADE,
    INDEX idx_config_data (config_id, data_inizio),
    INDEX idx_stato (stato)
);
```

## 🔧 Classi da Implementare

### 1. ProgettiModel.php
```php
<?php
namespace App\Models;

use App\Config\Database;
use PDO;
use PDOException;

class ProgettiModel {
    private PDO $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    public function getAllProgetti(array $filters = []): array {
        $query = "SELECT p.*, c.nome as cliente_nome, c.cognome as cliente_cognome,
                         COUNT(DISTINCT pr.id) as num_pratiche,
                         COUNT(DISTINCT s.id) as num_scadenze
                  FROM progetti p
                  LEFT JOIN clienti c ON p.cliente_id = c.id
                  LEFT JOIN pratiche pr ON p.id = pr.progetto_id
                  LEFT JOIN scadenze s ON p.id = s.progetto_id";
        
        $whereConditions = [];
        $params = [];
        
        if (!empty($filters['stato'])) {
            $whereConditions[] = "p.stato = :stato";
            $params[':stato'] = $filters['stato'];
        }
        
        if (!empty($filters['cliente_id'])) {
            $whereConditions[] = "p.cliente_id = :cliente_id";
            $params[':cliente_id'] = $filters['cliente_id'];
        }
        
        if (!empty($whereConditions)) {
            $query .= " WHERE " . implode(" AND ", $whereConditions);
        }
        
        $query .= " GROUP BY p.id ORDER BY p.data_inizio DESC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getProgettoById(int $id): ?array {
        $stmt = $this->db->prepare("
            SELECT p.*, c.nome as cliente_nome, c.cognome as cliente_cognome,
                   c.email as cliente_email, c.telefono as cliente_telefono
            FROM progetti p
            LEFT JOIN clienti c ON p.cliente_id = c.id
            WHERE p.id = :id
        ");
        $stmt->execute([':id' => $id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ?: null;
    }

    public function insertProgetto(array $data): bool {
        $query = "INSERT INTO progetti (
            cliente_id, nome_progetto, descrizione, data_inizio, 
            data_fine_prevista, stato, importo, comune, 
            indirizzo_progetto, tipo_progetto, priorita
        ) VALUES (
            :cliente_id, :nome_progetto, :descrizione, :data_inizio,
            :data_fine_prevista, :stato, :importo, :comune,
            :indirizzo_progetto, :tipo_progetto, :priorita
        )";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute($data);
    }

    public function updateProgetto(array $data): bool {
        $query = "UPDATE progetti SET 
            cliente_id = :cliente_id,
            nome_progetto = :nome_progetto,
            descrizione = :descrizione,
            data_inizio = :data_inizio,
            data_fine_prevista = :data_fine_prevista,
            stato = :stato,
            importo = :importo,
            comune = :comune,
            indirizzo_progetto = :indirizzo_progetto,
            tipo_progetto = :tipo_progetto,
            priorita = :priorita
            WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute($data);
    }

    public function deleteProgetto(int $id): bool {
        $stmt = $this->db->prepare("DELETE FROM progetti WHERE id = :id");
        return $stmt->execute([':id' => $id]);
    }

    public function getStatisticheProgetti(): array {
        $stats = [];
        
        // Progetti per stato
        $stmt = $this->db->query("
            SELECT stato, COUNT(*) as count 
            FROM progetti 
            GROUP BY stato
        ");
        $stats['per_stato'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        // Progetti per tipo
        $stmt = $this->db->query("
            SELECT tipo_progetto, COUNT(*) as count 
            FROM progetti 
            GROUP BY tipo_progetto
        ");
        $stats['per_tipo'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        // Valore totale progetti
        $stmt = $this->db->query("
            SELECT SUM(importo) as totale_valore,
                   AVG(importo) as valore_medio
            FROM progetti 
            WHERE importo IS NOT NULL
        ");
        $stats['valori'] = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $stats;
    }
}
```

### 2. NotificationService.php
```php
<?php
namespace App\Services;

use App\Models\NotificheModel;
use App\Models\User;

class NotificationService {
    private NotificheModel $notificheModel;
    private EmailService $emailService;

    public function __construct() {
        $this->notificheModel = new NotificheModel();
        $this->emailService = new EmailService();
    }

    public function createNotifica(array $data): bool {
        // Crea notifica nel database
        $notificaId = $this->notificheModel->insertNotifica($data);
        
        if (!$notificaId) {
            return false;
        }

        // Verifica preferenze utente per email
        if ($this->shouldSendEmail($data['user_id'], $data['tipo'])) {
            $this->emailService->sendNotificationEmail($data);
        }

        return true;
    }

    public function checkScadenzeAutomatiche(): void {
        // Controlla scadenze pratiche
        $praticheInScadenza = $this->getPraticheInScadenza();
        
        foreach ($praticheInScadenza as $pratica) {
            $this->createNotifica([
                'user_id' => $pratica['user_id'] ?? 1, // Default admin
                'tipo' => 'scadenza',
                'titolo' => 'Pratica in scadenza',
                'messaggio' => "La pratica {$pratica['numero_pratica']} scade il {$pratica['data_scadenza']}",
                'priorita' => $this->calcolaPriorita($pratica['giorni_rimanenti']),
                'link_azione' => "/pratiche/dettagli/{$pratica['id']}",
                'metadata' => json_encode(['pratica_id' => $pratica['id']])
            ]);
        }
    }

    private function shouldSendEmail(int $userId, string $tipo): bool {
        return $this->notificheModel->getPreferenzaEmail($userId, $tipo);
    }

    private function calcolaPriorita(int $giorniRimanenti): string {
        if ($giorniRimanenti <= 1) return 'alta';
        if ($giorniRimanenti <= 7) return 'media';
        return 'bassa';
    }
}
```

## 🔒 Miglioramenti Sicurezza

### CSRF Protection
```php
// app/core/Security.php - Miglioramenti
class Security {
    public static function generateCSRFToken(): string {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    public static function validateCSRFToken(string $token): bool {
        return isset($_SESSION['csrf_token']) && 
               hash_equals($_SESSION['csrf_token'], $token);
    }

    public static function sanitizeInput(array $data): array {
        return array_map(function($value) {
            if (is_string($value)) {
                return htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
            }
            return $value;
        }, $data);
    }

    public static function validateFileUpload(array $file): array {
        $errors = [];
        
        // Controllo dimensione (max 10MB)
        if ($file['size'] > 10 * 1024 * 1024) {
            $errors[] = 'File troppo grande (max 10MB)';
        }
        
        // Controllo tipo MIME
        $allowedTypes = [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        
        if (!in_array($file['type'], $allowedTypes)) {
            $errors[] = 'Tipo file non consentito';
        }
        
        // Controllo estensione
        $allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'];
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($extension, $allowedExtensions)) {
            $errors[] = 'Estensione file non consentita';
        }
        
        return $errors;
    }
}
```

## 📈 Performance Optimization

### Cache System
```php
// app/core/Cache.php
class Cache {
    private static string $cacheDir = ROOT_PATH . '/cache/';
    
    public static function get(string $key): mixed {
        $file = self::$cacheDir . md5($key) . '.cache';
        
        if (!file_exists($file)) {
            return null;
        }
        
        $data = unserialize(file_get_contents($file));
        
        if ($data['expires'] < time()) {
            unlink($file);
            return null;
        }
        
        return $data['value'];
    }
    
    public static function set(string $key, mixed $value, int $ttl = 3600): bool {
        if (!is_dir(self::$cacheDir)) {
            mkdir(self::$cacheDir, 0755, true);
        }
        
        $file = self::$cacheDir . md5($key) . '.cache';
        $data = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        
        return file_put_contents($file, serialize($data)) !== false;
    }
    
    public static function delete(string $key): bool {
        $file = self::$cacheDir . md5($key) . '.cache';
        return file_exists($file) ? unlink($file) : true;
    }
    
    public static function clear(): bool {
        $files = glob(self::$cacheDir . '*.cache');
        foreach ($files as $file) {
            unlink($file);
        }
        return true;
    }
}
```

## 🎯 Prossimi Passi

1. **Implementare ProgettiModel** - Priorità massima
2. **Creare sistema notifiche base** - Funzionalità critica
3. **Aggiungere CSRF protection** - Sicurezza essenziale
4. **Implementare cache system** - Performance
5. **Creare test suite** - Qualità codice
