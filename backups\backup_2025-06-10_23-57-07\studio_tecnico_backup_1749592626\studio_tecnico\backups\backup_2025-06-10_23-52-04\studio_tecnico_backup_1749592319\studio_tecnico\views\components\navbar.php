<?php
/**
 * Componente Navbar comune per tutte le pagine
 * @version 1.0.0
 */
?>
<style>
.navbar {
    background-color: #1a252f !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    color: #ecf0f1 !important;
}

.nav-link {
    color: #bdc3c7 !important;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem;
}

.nav-link:hover, .nav-link.active {
    color: #3498db !important;
    background-color: rgba(52, 152, 219, 0.1);
    border-radius: 6px;
}

.nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Stili specifici per il dropdown utente */
.dropdown-toggle::after {
    margin-left: 0.5rem;
}

.dropdown-menu-dark {
    background-color: #2c3e50 !important;
    border: none;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    min-width: 200px;
    padding: 0.5rem;
}

.dropdown-item {
    color: #bdc3c7 !important;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin: 2px 6px;
    padding: 0.5rem 1rem;
}

.dropdown-item:hover {
    background-color: #3498db !important;
    color: #ffffff !important;
    padding-left: 1.5rem;
}

.dropdown-divider {
    border-color: rgba(189, 195, 199, 0.2);
    margin: 0.5rem 0;
}

.navbar-toggler {
    border-color: rgba(236, 240, 241, 0.1) !important;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.nav-link.active {
    font-weight: 500;
}

/* Stili per il pulsante utente */
.nav-item.dropdown .nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
}

.nav-item.dropdown .nav-link i {
    font-size: 1.2rem;
}
</style>

<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand d-flex align-items-center gap-2" href="<?= BASE_URL ?>">
            <?php
            // Carica la configurazione in modo sicuro
            $configFile = ROOT_PATH . '/config/app.php';
            $config = [];
            if (file_exists($configFile)) {
                $config = require $configFile;
            }
            
            // Gestione del logo
            if (!empty($config['logo_path']) && file_exists(ROOT_PATH . '/public/' . $config['logo_path'])) {
                echo '<img src="' . BASE_URL . htmlspecialchars($config['logo_path']) . '" alt="Studio Tecnico" height="40">';
            } else {
                require_once VIEWS_DIR . '/components/default-logo.php';
                echo getDefaultLogo();
            }
            ?>
            <span class="text-light fw-bold"><?= htmlspecialchars($config['app_name'] ?? 'Studio Tecnico') ?></span>
        </a>
        
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <?php if (isset($_SESSION['user'])): ?>
            <ul class="navbar-nav ms-4">
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center gap-2 <?= isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], 'clienti') !== false ? 'active' : '' ?>" 
                       href="<?= BASE_URL ?>clienti">
                       <i class="fas fa-users"></i>
                       <span>Clienti</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center gap-2 <?= isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], 'progetti') !== false ? 'active' : '' ?>" 
                       href="<?= BASE_URL ?>progetti">
                       <i class="fas fa-project-diagram"></i>
                       <span>Progetti</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center gap-2 <?= isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], 'pratiche') !== false ? 'active' : '' ?>" 
                       href="<?= BASE_URL ?>pratiche">
                       <i class="fas fa-folder-open"></i>
                       <span>Pratiche</span>
                    </a>
                </li>
            </ul>
            
            <ul class="navbar-nav ms-auto">
                <?php 
                // Debug della sessione
                if (!isset($_SESSION['user'])) {
                    error_log('Sessione utente non trovata nella navbar');
                } else {
                    error_log('Dati utente nella sessione: ' . print_r($_SESSION['user'], true));
                }
                
                // Verifica se l'utente è admin in modo sicuro
                $isAdmin = isset($_SESSION['user']['ruolo']) && $_SESSION['user']['ruolo'] === 'admin';
                if ($isAdmin): 
                ?>
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center gap-2" href="<?= BASE_URL ?>admin/config">
                        <i class="fas fa-cog"></i>
                        <span>Configurazione</span>
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center gap-2" href="#" role="button" 
                       data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle"></i>
                        <?= htmlspecialchars($_SESSION['user']['username'] ?? 'Utente') ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end">
                        <li>
                            <a class="dropdown-item d-flex align-items-center gap-2" href="<?= BASE_URL ?>profilo">
                                <i class="fas fa-user"></i>
                                <span>Profilo</span>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item d-flex align-items-center gap-2" href="#" id="manualeLink">
                                <i class="fas fa-book"></i>
                                <span>Manuale d'Uso</span>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item d-flex align-items-center gap-2" href="<?= BASE_URL ?>logout">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>Logout</span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
            <?php endif; ?>
        </div>
    </div>
</nav>

<!-- Modal Manuale d'Uso -->
<div class="modal fade" id="manualeModal" tabindex="-1" aria-labelledby="manualeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="manualeModalLabel">Manuale d'Uso</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <iframe id="manualeFrame" src="" style="width: 100%; height: 80vh; border: none;"></iframe>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestione apertura manuale in modale
    document.getElementById('manualeLink')?.addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('manualeFrame').src = '<?= BASE_URL ?>docs/manuale.html';
        var manualeModal = new bootstrap.Modal(document.getElementById('manualeModal'));
        manualeModal.show();
    });
});
</script>
