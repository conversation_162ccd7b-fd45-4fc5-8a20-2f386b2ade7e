const ClientiManager = {
    init() {
        this.bindEvents();
        this.initFormValidation();
    },

    bindEvents() {
        // Gestione tipo cliente (privato/società)
        $('input[name="tipo_cliente"]').on('change', this.toggleTipoCliente.bind(this));
        
        // Gestione submit form
        $('#clienteForm').on('submit', this.handleSubmit.bind(this));
        
        // Gestione eliminazione cliente
        $(document).on('click', '.delete-cliente', this.handleDelete.bind(this));
    },

    initFormValidation() {
        const form = document.querySelector('#clienteForm');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    },

    toggleTipoCliente(e) {
        const tipo = e.target.value;
        const campiPrivato = document.getElementById('campi_privato');
        const campiSocieta = document.getElementById('campi_societa');
        
        if (tipo === 'privato') {
            campiPrivato.style.display = 'block';
            campiSocieta.style.display = 'none';
            this.toggleRequiredFields('privato');
        } else {
            campiPrivato.style.display = 'none';
            campiSocieta.style.display = 'block';
            this.toggleRequiredFields('societa');
        }
    },

    toggleRequiredFields(tipo) {
        const campiPrivato = ['nome', 'cognome', 'codice_fiscale'];
        const campiSocieta = ['ragione_sociale', 'partita_iva'];
        
        campiPrivato.forEach(campo => {
            document.getElementById(campo).required = (tipo === 'privato');
        });
        
        campiSocieta.forEach(campo => {
            document.getElementById(campo).required = (tipo === 'societa');
        });
    },

    async handleSubmit(e) {
        e.preventDefault();
        
        if (!e.target.checkValidity()) {
            return;
        }

        const formData = new FormData(e.target);
        
        try {
            const response = await $.ajax({
                url: `${BASE_URL}api/clienti/salva.php`,
                method: 'POST',
                data: Object.fromEntries(formData),
                dataType: 'json'
            });

            if (response.success) {
                await Swal.fire({
                    title: 'Successo!',
                    text: response.message,
                    icon: 'success'
                });
                window.location.href = 'index.php';
            }
        } catch (error) {
            Swal.fire({
                title: 'Errore!',
                text: error.responseJSON?.message || 'Si è verificato un errore durante il salvataggio',
                icon: 'error'
            });
        }
    },

    async handleDelete(e) {
        e.preventDefault();
        const id = $(e.currentTarget).data('id');
        
        try {
            const result = await Swal.fire({
                title: 'Sei sicuro?',
                text: "Questa operazione non può essere annullata!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Sì, elimina',
                cancelButtonText: 'Annulla',
                reverseButtons: true
            });

            if (result.isConfirmed) {
                const response = await $.ajax({
                    url: `${BASE_URL}api/clienti/elimina.php`,
                    method: 'POST',
                    data: { id }
                });

                if (response.success) {
                    Swal.fire(
                        'Eliminato!',
                        'Il cliente è stato eliminato con successo.',
                        'success'
                    );
                    window.location.reload();
                }
            }
        } catch (error) {
            Swal.fire({
                title: 'Errore!',
                text: 'Si è verificato un errore durante l\'eliminazione.',
                icon: 'error'
            });
            console.error(error);
        }
    }
};

// Inizializza il modulo quando il documento è pronto
$(document).ready(() => {
    ClientiManager.init();
}); 