<?php
// test_login.php - Script temporaneo per testare il login
session_start();

// Simula variabili HTTP per CLI
$_SERVER['HTTP_HOST'] = 'localhost';
$_SERVER['HTTPS'] = 'off';

// Carica le configurazioni
require_once __DIR__ . '/bootstrap.php';
require_once __DIR__ . '/app/core/Autoloader.php';

use App\Core\Autoloader;
use App\Config\Database;
use App\Models\User;

// Registra l'autoloader
Autoloader::register();

try {
    // Connessione al database usando il metodo statico
    $db = Database::getInstance();
    
    echo "✅ Connessione al database riuscita\n";
    
    // Test del modello User
    $userModel = new User($db);
    
    // Test login con credenziali corrette
    echo "\n🔍 Test login con admin/admin123...\n";
    $user = $userModel->login('admin', 'admin123');
    
    if ($user) {
        echo "✅ Login riuscito!\n";
        echo "Dati utente:\n";
        print_r($user);
    } else {
        echo "❌ Login fallito\n";
        
        // Verifica se l'utente esiste
        $stmt = $db->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute(['admin']);
        $dbUser = $stmt->fetch();
        
        if ($dbUser) {
            echo "✅ Utente trovato nel database\n";
            echo "Hash password nel DB: " . $dbUser['password'] . "\n";
            
            // Test verifica password
            if (password_verify('admin123', $dbUser['password'])) {
                echo "✅ Password corretta\n";
            } else {
                echo "❌ Password non corretta\n";
                echo "Provo con password vuota...\n";
                if (password_verify('', $dbUser['password'])) {
                    echo "✅ Password vuota funziona\n";
                } else {
                    echo "❌ Nemmeno password vuota\n";
                    echo "Provo con 'admin'...\n";
                    if (password_verify('admin', $dbUser['password'])) {
                        echo "✅ Password 'admin' funziona\n";
                    } else {
                        echo "❌ Password 'admin' non funziona\n";
                    }
                }
            }
        } else {
            echo "❌ Utente non trovato nel database\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Errore: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
