<?php
// app/services/NotificationService.php - Servizio per la gestione delle notifiche automatiche
namespace App\Services;

use App\Models\NotificheModel;
use App\Models\User;
use DateTime;

class NotificationService {
    private NotificheModel $notificheModel;

    public function __construct() {
        try {
            $this->notificheModel = new NotificheModel();
        } catch (\Exception $e) {
            error_log("Errore nell'inizializzazione del NotificationService: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Crea una notifica generica
     */
    public function createNotifica(array $data): bool {
        try {
            $required_fields = ['user_id', 'tipo', 'titolo', 'messaggio'];
            foreach ($required_fields as $field) {
                if (empty($data[$field])) {
                    throw new \Exception("Campo obbligatorio mancante: {$field}");
                }
            }

            // Imposta valori di default
            $data['priorita'] = $data['priorita'] ?? 'media';
            $data['metadata'] = $data['metadata'] ?? [];

            $notificaId = $this->notificheModel->insertNotifica($data);
            
            if ($notificaId) {
                error_log("NotificationService::createNotifica() - Notifica creata con ID: {$notificaId}");
                
                // Verifica se inviare email
                if ($this->shouldSendEmail($data['user_id'], $data['tipo'])) {
                    $this->sendEmailNotification($data);
                }
                
                return true;
            }
            
            return false;

        } catch (\Exception $e) {
            error_log("Errore in NotificationService::createNotifica(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Controllo automatico delle scadenze - metodo principale
     */
    public function checkScadenzeAutomatiche(int $giorni_anticipo = 7): array {
        try {
            $risultati = [
                'scadenze_controllate' => 0,
                'pratiche_controllate' => 0,
                'notifiche_create' => 0,
                'errori' => []
            ];

            // Controlla scadenze generali
            $scadenze = $this->notificheModel->getScadenzeImminenti($giorni_anticipo);
            $risultati['scadenze_controllate'] = count($scadenze);

            foreach ($scadenze as $scadenza) {
                try {
                    // Evita duplicati
                    if (!$this->notificheModel->esisteNotifica('scadenza', $scadenza['scadenza_id'], 1)) {
                        if ($this->notificheModel->createNotificaScadenza($scadenza['scadenza_id'], 1)) {
                            $risultati['notifiche_create']++;
                        }
                    }
                } catch (\Exception $e) {
                    $risultati['errori'][] = "Errore scadenza ID {$scadenza['scadenza_id']}: " . $e->getMessage();
                }
            }

            // Controlla pratiche in scadenza
            $pratiche = $this->notificheModel->getPraticheInScadenza($giorni_anticipo);
            $risultati['pratiche_controllate'] = count($pratiche);

            foreach ($pratiche as $pratica) {
                try {
                    // Evita duplicati
                    if (!$this->notificheModel->esisteNotifica('pratica', $pratica['pratica_id'], 1)) {
                        if ($this->notificheModel->createNotificaPratica($pratica['pratica_id'], 'scadenza_vicina', 1)) {
                            $risultati['notifiche_create']++;
                        }
                    }
                } catch (\Exception $e) {
                    $risultati['errori'][] = "Errore pratica ID {$pratica['pratica_id']}: " . $e->getMessage();
                }
            }

            // Pulizia automatica notifiche vecchie
            $deleted = $this->notificheModel->deleteOldNotifiche(30);
            if ($deleted > 0) {
                error_log("NotificationService::checkScadenzeAutomatiche() - Eliminate {$deleted} notifiche vecchie");
            }

            error_log("NotificationService::checkScadenzeAutomatiche() - Completato: " . json_encode($risultati));
            return $risultati;

        } catch (\Exception $e) {
            error_log("Errore in NotificationService::checkScadenzeAutomatiche(): " . $e->getMessage());
            return [
                'scadenze_controllate' => 0,
                'pratiche_controllate' => 0,
                'notifiche_create' => 0,
                'errori' => [$e->getMessage()]
            ];
        }
    }

    /**
     * Verifica se inviare email per un tipo di notifica
     */
    private function shouldSendEmail(int $user_id, string $tipo_notifica): bool {
        try {
            return $this->notificheModel->getPreferenzaEmail($user_id, $tipo_notifica);
        } catch (\Exception $e) {
            error_log("Errore in NotificationService::shouldSendEmail(): " . $e->getMessage());
            return false; // Default: non inviare email se c'è un errore
        }
    }

    /**
     * Invia notifica email (implementazione base)
     */
    private function sendEmailNotification(array $data): bool {
        try {
            // Per ora solo logging - in futuro implementare invio email reale
            $logMessage = sprintf(
                "EMAIL NOTIFICA - User: %d, Tipo: %s, Titolo: %s, Messaggio: %s",
                $data['user_id'],
                $data['tipo'],
                $data['titolo'],
                substr($data['messaggio'], 0, 100) . '...'
            );
            
            error_log($logMessage);
            
            // TODO: Implementare invio email reale con PHPMailer o simili
            // $this->emailService->send($userEmail, $data['titolo'], $data['messaggio']);
            
            return true;

        } catch (\Exception $e) {
            error_log("Errore in NotificationService::sendEmailNotification(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Calcola priorità in base ai giorni rimanenti
     */
    public function calcolaPriorita(int $giorniRimanenti): string {
        if ($giorniRimanenti <= 1) return 'alta';
        if ($giorniRimanenti <= 7) return 'media';
        return 'bassa';
    }

    /**
     * Crea notifica per cambio stato progetto
     */
    public function notificaCambioStatoProgetto(int $progetto_id, string $nuovo_stato, int $user_id = 1): bool {
        try {
            $data = [
                'user_id' => $user_id,
                'tipo' => 'progetto',
                'titolo' => 'Cambio stato progetto',
                'messaggio' => "Il progetto ha cambiato stato in: {$nuovo_stato}",
                'priorita' => 'media',
                'link_azione' => "/progetti/dettagli/{$progetto_id}",
                'metadata' => [
                    'progetto_id' => $progetto_id,
                    'nuovo_stato' => $nuovo_stato,
                    'tipo_notifica' => 'cambio_stato'
                ]
            ];

            return $this->createNotifica($data);

        } catch (\Exception $e) {
            error_log("Errore in NotificationService::notificaCambioStatoProgetto(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Crea notifica per nuovo cliente
     */
    public function notificaNuovoCliente(int $cliente_id, string $nome_cliente, int $user_id = 1): bool {
        try {
            $data = [
                'user_id' => $user_id,
                'tipo' => 'cliente',
                'titolo' => 'Nuovo cliente registrato',
                'messaggio' => "È stato registrato un nuovo cliente: {$nome_cliente}",
                'priorita' => 'bassa',
                'link_azione' => "/clienti/dettagli/{$cliente_id}",
                'metadata' => [
                    'cliente_id' => $cliente_id,
                    'tipo_notifica' => 'nuovo_cliente'
                ]
            ];

            return $this->createNotifica($data);

        } catch (\Exception $e) {
            error_log("Errore in NotificationService::notificaNuovoCliente(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Crea notifica per documento caricato
     */
    public function notificaDocumentoCaricato(int $pratica_id, string $nome_documento, int $user_id = 1): bool {
        try {
            $data = [
                'user_id' => $user_id,
                'tipo' => 'documento',
                'titolo' => 'Nuovo documento caricato',
                'messaggio' => "È stato caricato un nuovo documento: {$nome_documento}",
                'priorita' => 'media',
                'link_azione' => "/pratiche/dettagli/{$pratica_id}",
                'metadata' => [
                    'pratica_id' => $pratica_id,
                    'nome_documento' => $nome_documento,
                    'tipo_notifica' => 'documento_caricato'
                ]
            ];

            return $this->createNotifica($data);

        } catch (\Exception $e) {
            error_log("Errore in NotificationService::notificaDocumentoCaricato(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Crea notifica per pratica in scadenza critica
     */
    public function notificaPraticaScadenzaCritica(int $pratica_id, int $giorni_rimanenti, int $user_id = 1): bool {
        try {
            $priorita = $giorni_rimanenti <= 1 ? 'alta' : 'media';

            $data = [
                'user_id' => $user_id,
                'tipo' => 'pratica',
                'titolo' => 'Pratica in scadenza critica',
                'messaggio' => "La pratica scade tra {$giorni_rimanenti} giorni. Azione richiesta urgentemente.",
                'priorita' => $priorita,
                'link_azione' => "/pratiche/dettagli/{$pratica_id}",
                'metadata' => [
                    'pratica_id' => $pratica_id,
                    'giorni_rimanenti' => $giorni_rimanenti,
                    'tipo_notifica' => 'scadenza_critica'
                ]
            ];

            return $this->createNotifica($data);

        } catch (\Exception $e) {
            error_log("Errore in NotificationService::notificaPraticaScadenzaCritica(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Crea notifica per documenti mancanti
     */
    public function notificaDocumentiMancanti(int $pratica_id, array $documenti_mancanti, int $user_id = 1): bool {
        try {
            $lista_documenti = implode(', ', $documenti_mancanti);

            $data = [
                'user_id' => $user_id,
                'tipo' => 'pratica',
                'titolo' => 'Documenti mancanti per pratica',
                'messaggio' => "Documenti richiesti: {$lista_documenti}",
                'priorita' => 'media',
                'link_azione' => "/pratiche/dettagli/{$pratica_id}",
                'metadata' => [
                    'pratica_id' => $pratica_id,
                    'documenti_mancanti' => $documenti_mancanti,
                    'tipo_notifica' => 'documenti_mancanti'
                ]
            ];

            return $this->createNotifica($data);

        } catch (\Exception $e) {
            error_log("Errore in NotificationService::notificaDocumentiMancanti(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Crea notifica per approvazione pratica
     */
    public function notificaPraticaApprovata(int $pratica_id, string $numero_pratica, int $user_id = 1): bool {
        try {
            $data = [
                'user_id' => $user_id,
                'tipo' => 'pratica',
                'titolo' => 'Pratica approvata',
                'messaggio' => "La pratica {$numero_pratica} è stata approvata con successo",
                'priorita' => 'media',
                'link_azione' => "/pratiche/dettagli/{$pratica_id}",
                'metadata' => [
                    'pratica_id' => $pratica_id,
                    'numero_pratica' => $numero_pratica,
                    'tipo_notifica' => 'pratica_approvata'
                ]
            ];

            return $this->createNotifica($data);

        } catch (\Exception $e) {
            error_log("Errore in NotificationService::notificaPraticaApprovata(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Crea notifica per pratica respinta
     */
    public function notificaPraticaRespinta(int $pratica_id, string $numero_pratica, string $motivo = '', int $user_id = 1): bool {
        try {
            $messaggio = "La pratica {$numero_pratica} è stata respinta";
            if (!empty($motivo)) {
                $messaggio .= ". Motivo: {$motivo}";
            }

            $data = [
                'user_id' => $user_id,
                'tipo' => 'pratica',
                'titolo' => 'Pratica respinta',
                'messaggio' => $messaggio,
                'priorita' => 'alta',
                'link_azione' => "/pratiche/dettagli/{$pratica_id}",
                'metadata' => [
                    'pratica_id' => $pratica_id,
                    'numero_pratica' => $numero_pratica,
                    'motivo' => $motivo,
                    'tipo_notifica' => 'pratica_respinta'
                ]
            ];

            return $this->createNotifica($data);

        } catch (\Exception $e) {
            error_log("Errore in NotificationService::notificaPraticaRespinta(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Metodo per eseguire controlli schedulati (da chiamare via cron)
     */
    public function runScheduledChecks(): array {
        try {
            error_log("NotificationService::runScheduledChecks() - Inizio controlli schedulati");
            
            $risultati = $this->checkScadenzeAutomatiche();
            
            // Aggiungi timestamp al risultato
            $risultati['timestamp'] = date('Y-m-d H:i:s');
            $risultati['success'] = empty($risultati['errori']);
            
            error_log("NotificationService::runScheduledChecks() - Completato: " . json_encode($risultati));
            
            return $risultati;

        } catch (\Exception $e) {
            error_log("Errore in NotificationService::runScheduledChecks(): " . $e->getMessage());
            return [
                'success' => false,
                'timestamp' => date('Y-m-d H:i:s'),
                'errori' => [$e->getMessage()],
                'scadenze_controllate' => 0,
                'pratiche_controllate' => 0,
                'notifiche_create' => 0
            ];
        }
    }
}
