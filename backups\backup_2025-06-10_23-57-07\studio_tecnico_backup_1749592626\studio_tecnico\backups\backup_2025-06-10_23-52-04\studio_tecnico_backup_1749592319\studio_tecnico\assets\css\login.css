/* Login page specific styles */
html[data-theme="light"] body.login-page {
    background-color: #2b2b2b;
}

html[data-theme="dark"] body.login-page {
    background-color: #1a1a1a;
}

.login-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 2rem 0;
    background-color: rgba(0, 0, 0, 0.7);
}

.login-page .login-container {
    max-width: 400px;
    margin: 0 auto;
    width: 100%;
}

.login-page .app-info {
    text-align: center;
    margin-bottom: 2rem;
    color: #ffffff;
}

.login-page .app-info h1 {
    color: rgba(255, 255, 255, 0.85);
    margin-bottom: 0.5rem;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.login-page .app-info p {
    color: #ffffff;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    opacity: 1;
    text-shadow: 0 1px 2px rgba(90, 89, 89, 0.2);
}

.login-page .app-logo {
    width: 100px;
    height: 100px;
    margin-bottom: 1.5rem;
    display: block;
    margin-left: auto;
    margin-right: auto;
    filter: brightness(0) invert(1);
}

.login-page .version-badge {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    background-color: rgba(0, 0, 0, 0.2);
    color: #ffffff;
    display: inline-block;
    margin-top: 0.5rem;
    font-weight: 600;
    opacity: 0.95;
    text-shadow: none;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.login-page .card {
    background: #f8f9fa;
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.login-page .card:hover {
    box-shadow: 0 12px 20px rgba(0,0,0,0.25);
    transform: translateY(-4px);
}

.login-page .card-header {
    background-color: transparent;
    border-bottom: 1px solid #dee2e6;
    padding: 1.5rem 1.5rem 1rem;
}

.login-page .input-group {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background-color: #ffffff;
}

.login-page .input-group:focus-within {
    border-color: #6c757d;
    box-shadow: 0 0 0 2px rgba(108, 117, 125, 0.25);
}

.login-page .input-group-text {
    background-color: #ffffff;
    border: none;
    color: #495057;
}

.login-page .form-control {
    border: none;
    padding: 0.75rem;
    background-color: #ffffff;
}

.login-page .form-control:focus {
    box-shadow: none;
    background-color: #ffffff;
}

.login-page .btn-neutral {
    background-color: #343a40;
    color: #ffffff;
    border: none;
    padding: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.login-page .btn-neutral:hover {
    background-color: #212529;
    color: #ffffff;
    transform: translateY(-1px);
}

.login-page .form-label {
    color: #495057;
    font-weight: 500;
}

.login-page .alert-danger {
    background-color: #dc3545;
    border: none;
    color: #ffffff;
}
