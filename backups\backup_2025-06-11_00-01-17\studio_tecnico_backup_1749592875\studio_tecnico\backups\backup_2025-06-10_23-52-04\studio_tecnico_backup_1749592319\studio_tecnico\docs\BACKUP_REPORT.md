# Sistema di Backup - Studio Tecnico

## Panoramica
Il sistema di backup implementato fornisce una soluzione completa per il salvataggio sicuro dei dati dell'applicazione Studio Tecnico. Il sistema è progettato per essere user-friendly mentre mantiene robustezza e affidabilità nelle operazioni di backup.

## Funzionalità Principali

### 1. Backup del Database
- Utilizza `mysqldump` per creare un dump completo del database
- Genera file SQL con timestamp per facile identificazione
- Include gestione degli errori e logging dettagliato
- Protegge le credenziali sensibili nei log

### 2. Backup dei File
- **Modalità Compressa**:
  - Utilizza l'estensione PHP ZIP per la compressione
  - Crea un singolo file ZIP contenente tutti i dati
  - Riduce significativamente la dimensione del backup
- **Modalità Non Compressa**:
  - Mantiene la struttura delle directory originale
  - Facilita l'accesso diretto ai file
  - Ideale per backup locali o quando lo spazio non è un problema

### 3. Gestione della Directory Backups
- Backup automatico della cartella `backups` e dei suoi contenuti
- Preserva script e file di utility importanti
- Mantiene la cronologia dei backup precedenti

### 4. Interfaccia Utente
- **Selezione Directory**:
  - Opzioni predefinite per percorsi comuni (Desktop, Documenti)
  - Possibilità di specificare percorsi personalizzati
  - Validazione dei permessi di scrittura
- **Opzioni di Backup**:
  - Toggle per la compressione dei file
  - Indicatore di progresso durante il backup
  - Messaggi di feedback chiari e informativi

### 5. Sicurezza e Robustezza
- Verifica dei permessi di scrittura
- Validazione dei percorsi di destinazione
- Logging dettagliato per il debugging
- Gestione degli errori con messaggi user-friendly
- Protezione delle credenziali sensibili

## Struttura del Backup

### Con Compressione
```
backup_YYYY-MM-DD_HH-II-SS/
├── database_backup_YYYY-MM-DD_HH-II-SS.sql
├── files_YYYY-MM-DD_HH-II-SS.zip
├── backups/
│   └── [contenuto della cartella backups]
└── README.txt
```

### Senza Compressione
```
backup_YYYY-MM-DD_HH-II-SS/
├── database_backup_YYYY-MM-DD_HH-II-SS.sql
├── files/
│   └── [tutti i file del progetto]
└── README.txt
```

## File di Configurazione
Il sistema utilizza le seguenti costanti dal file di configurazione:
- `DB_HOST`: Host del database
- `DB_USER`: Username del database
- `DB_PASS`: Password del database
- `DB_NAME`: Nome del database

## Requisiti di Sistema
- PHP 7.0 o superiore
- Estensione ZIP PHP (opzionale, per la compressione)
- MySQL/MariaDB
- XAMPP (per mysqldump)
- Permessi di scrittura nella directory di destinazione

## Best Practices
1. **Scelta della Destinazione**:
   - Utilizzare un disco diverso da quello del sistema operativo
   - Assicurarsi di avere spazio sufficiente
   - Verificare i permessi di scrittura

2. **Frequenza dei Backup**:
   - Eseguire backup regolari
   - Mantenere copie in luoghi diversi
   - Verificare periodicamente l'integrità dei backup

3. **Gestione dello Spazio**:
   - Utilizzare la compressione per backup di grandi dimensioni
   - Rimuovere periodicamente i backup obsoleti
   - Monitorare lo spazio disponibile

## Troubleshooting
- **Errore di Permessi**: Verificare i permessi della directory di destinazione
- **Errore di Spazio**: Assicurarsi di avere spazio sufficiente sul disco
- **Errore di Database**: Verificare le credenziali e la connessione al database
- **Errore di ZIP**: Controllare che l'estensione PHP ZIP sia installata

## Changelog

### Versione 1.0.0 (11 Dicembre 2024)
- Implementazione iniziale del sistema di backup
- Supporto per backup compressi e non compressi
- Interfaccia utente intuitiva
- Sistema di logging completo
- Gestione degli errori robusta
