<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? htmlspecialchars($pageTitle) . ' - Studio Tecnico' : 'Studio Tecnico' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .navbar-custom {
            background-color: #2c3e50;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }
        
        .navbar-custom .navbar-brand,
        .navbar-custom .nav-link {
            color: #ecf0f1;
        }
        
        .navbar-custom .nav-link:hover {
            color: #3498db;
        }
        
        .help-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .card {
            transition: transform 0.2s;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .border-left-primary { border-left: 4px solid #3498db; }
        .border-left-success { border-left: 4px solid #2ecc71; }
        .border-left-info { border-left: 4px solid #3498db; }
        .border-left-warning { border-left: 4px solid #f1c40f; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?= BASE_URL ?>dashboard">
                <i class="fas fa-drafting-compass me-2"></i>
                Studio Tecnico
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="manualeLink">
                            <i class="fas fa-book me-1"></i>
                            Manuale
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= BASE_URL ?>help" title="Aiuto">
                            <i class="fas fa-question-circle me-1"></i>
                            Aiuto
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= BASE_URL ?>logout" title="Esci">
                            <i class="fas fa-sign-out-alt me-1"></i>
                            Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Modal Manuale d'Uso -->
    <div class="modal fade" id="manualeModal" tabindex="-1" aria-labelledby="manualeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="manualeModalLabel">Manuale d'Uso</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <iframe id="manualeFrame" src="" style="width: 100%; height: 80vh; border: none;"></iframe>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenuto principale -->
    <main class="py-4">
        <?= $content ?? '' ?>
    </main>

    <!-- Pulsante Help -->
    <button class="btn btn-primary rounded-circle help-button" type="button" data-bs-toggle="modal" data-bs-target="#helpModal">
        <i class="fas fa-question"></i>
    </button>

    <!-- Modal Help -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Guida Rapida</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>Benvenuto nella Dashboard</h6>
                    <p>Da qui puoi accedere a tutte le funzionalità principali del sistema:</p>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-users text-primary me-2"></i>Clienti</h6>
                        <p>Gestisci l'anagrafica dei clienti, visualizza e modifica i loro dati.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-folder-open text-success me-2"></i>Pratiche</h6>
                        <p>Gestisci le pratiche, carica e organizza i documenti associati.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-project-diagram text-info me-2"></i>Progetti</h6>
                        <p>Gestisci i progetti, monitora il loro stato di avanzamento.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-calendar-alt text-warning me-2"></i>Scadenze</h6>
                        <p>Visualizza e gestisci le scadenze imminenti.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Chiudi</button>
                    <a href="<?= BASE_URL ?>help" class="btn btn-primary">Vai alla guida completa</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Gestione apertura manuale in modale
            document.getElementById('manualeLink').addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('manualeFrame').src = '<?= BASE_URL ?>docs/manuale.html';
                var manualeModal = new bootstrap.Modal(document.getElementById('manualeModal'));
                manualeModal.show();
            });
        });
    </script>
</body>
</html>
