/* Navbar Styles */
.navbar {
    background-color: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 0;
}

.navbar-brand {
    font-weight: 600;
    color: #2563eb;
}

.navbar-brand img {
    height: 40px;
    width: auto;
    margin-right: 0.75rem;
}

.nav-link {
    color: #4b5563;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.nav-link:hover {
    color: #2563eb;
    background-color: #f3f4f6;
}

.nav-link.active {
    color: #2563eb;
    background-color: #eff6ff;
}

.nav-link i {
    width: 20px;
    text-align: center;
    margin-right: 0.5rem;
}

/* Dropdown Styles */
.dropdown-menu {
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    padding: 0.5rem;
}

.dropdown-item {
    color: #4b5563;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    color: #2563eb;
    background-color: #f3f4f6;
}

.dropdown-divider {
    margin: 0.5rem 0;
    border-color: #e5e7eb;
}

/* Theme Toggle Button */
.theme-toggle {
    background: none;
    border: none;
    color: #4b5563;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.theme-toggle:hover {
    color: #2563eb;
    background-color: #f3f4f6;
}

/* Common Page Styles */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding: 1rem 0;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-title i {
    color: #6b7280;
}

.page-actions {
    display: flex;
    gap: 0.75rem;
}

/* Container Styles */
.container-fluid {
    max-width: 1920px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem 0;
    }

    .navbar-brand img {
        height: 32px;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .page-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

/* Dark Mode Support */
[data-theme="dark"] {
    .navbar {
        background-color: #1f2937;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .nav-link {
        color: #d1d5db;
    }

    .nav-link:hover {
        color: #60a5fa;
        background-color: #374151;
    }

    .nav-link.active {
        color: #60a5fa;
        background-color: #374151;
    }

    .dropdown-menu {
        background-color: #1f2937;
        border-color: #374151;
    }

    .dropdown-item {
        color: #d1d5db;
    }

    .dropdown-item:hover {
        color: #60a5fa;
        background-color: #374151;
    }

    .dropdown-divider {
        border-color: #374151;
    }

    .page-title {
        color: #f9fafb;
    }

    .page-title i {
        color: #9ca3af;
    }
}
