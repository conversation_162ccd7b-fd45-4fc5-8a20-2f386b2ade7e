[25-May-2025 07:59:10 Europe/Rome] Bootstrap e Autoloader caricati
[25-May-2025 07:59:10 Europe/Rome] Autoloader registrato
[25-May-2025 07:59:10 Europe/Rome] Tentativo di connessione al database
[25-May-2025 07:59:10 Europe/Rome] Connessione al database stabilita con successo
[25-May-2025 07:59:10 Europe/Rome] Connessione al database ottenuta
[25-May-2025 07:59:10 Europe/Rome] Router inizializzato
[25-May-2025 07:59:10 Europe/Rome] Route di autenticazione definite
[25-May-2025 07:59:10 Europe/Rome] Route profilo admin definite
[25-May-2025 07:59:10 Europe/Rome] Route home definita
[25-May-2025 07:59:10 Europe/Rome] Route dashboard definita
[25-May-2025 07:59:10 Europe/Rome] Route pratiche definite
[25-May-2025 07:59:10 Europe/Rome] Route progetti definite
[25-May-2025 07:59:10 Europe/Rome] Route clienti definite
[25-May-2025 07:59:10 Europe/Rome] Route scadenze definite
[25-May-2025 07:59:10 Europe/Rome] URL richiesto: login
[25-May-2025 07:59:10 Europe/Rome] Route dispatched
[10-Jun-2025 22:22:14 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 22:22:14 Europe/Rome] Autoloader registrato
[10-Jun-2025 22:22:14 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 22:22:14 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 22:22:14 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 22:22:15 Europe/Rome] Router inizializzato
[10-Jun-2025 22:22:15 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 22:22:15 Europe/Rome] Route profilo admin definite
[10-Jun-2025 22:22:15 Europe/Rome] Route home definita
[10-Jun-2025 22:22:15 Europe/Rome] Route dashboard definita
[10-Jun-2025 22:22:15 Europe/Rome] Route pratiche definite
[10-Jun-2025 22:22:15 Europe/Rome] Route progetti definite
[10-Jun-2025 22:22:15 Europe/Rome] Route clienti definite
[10-Jun-2025 22:22:15 Europe/Rome] Route scadenze definite
[10-Jun-2025 22:22:15 Europe/Rome] URL richiesto: login
[10-Jun-2025 22:22:15 Europe/Rome] Route dispatched
[10-Jun-2025 22:22:25 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 22:22:25 Europe/Rome] Autoloader registrato
[10-Jun-2025 22:22:25 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 22:22:25 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 22:22:25 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 22:22:25 Europe/Rome] Router inizializzato
[10-Jun-2025 22:22:25 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 22:22:25 Europe/Rome] Route profilo admin definite
[10-Jun-2025 22:22:25 Europe/Rome] Route home definita
[10-Jun-2025 22:22:25 Europe/Rome] Route dashboard definita
[10-Jun-2025 22:22:25 Europe/Rome] Route pratiche definite
[10-Jun-2025 22:22:25 Europe/Rome] Route progetti definite
[10-Jun-2025 22:22:25 Europe/Rome] Route clienti definite
[10-Jun-2025 22:22:25 Europe/Rome] Route scadenze definite
[10-Jun-2025 22:22:25 Europe/Rome] URL richiesto: login
[2025-06-10 22:22:25][🔴 FATAL][Guest@::1][error_log.php:105] Eccezione non catturata: Call to undefined method App\Controllers\AuthController::requireCSRF()
Stack trace:
#0 [internal function]: App\Controllers\AuthController->login()
#1 C:\xampp\htdocs\progetti\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#2 C:\xampp\htdocs\progetti\studio_tecnico\index.php(152): App\Core\Router->dispatch('login')
#3 {main}
[10-Jun-2025 22:58:26 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 22:59:05 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 22:59:05 Europe/Rome] Autoloader registrato
[10-Jun-2025 22:59:05 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 22:59:05 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 22:59:05 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 22:59:05 Europe/Rome] Router inizializzato
[10-Jun-2025 22:59:05 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 22:59:05 Europe/Rome] Route profilo admin definite
[10-Jun-2025 22:59:05 Europe/Rome] Route home definita
[10-Jun-2025 22:59:05 Europe/Rome] Route dashboard definita
[10-Jun-2025 22:59:05 Europe/Rome] Route pratiche definite
[10-Jun-2025 22:59:05 Europe/Rome] Route progetti definite
[10-Jun-2025 22:59:05 Europe/Rome] Route clienti definite
[10-Jun-2025 22:59:05 Europe/Rome] Route scadenze definite
[10-Jun-2025 22:59:05 Europe/Rome] URL richiesto: index.php
[10-Jun-2025 22:59:05 Europe/Rome] Route dispatched
[10-Jun-2025 23:01:01 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:01:01 Europe/Rome] PraticheModel::insertPratica() - Dati ricevuti: Array
(
    [progetto_id] => 1
    [tipo_documento] => SCIA
    [numero_pratica] => TEST-1749589261
    [stato] => in_attesa
    [note] => Test post-aggiornamento database
)

[10-Jun-2025 23:01:01 Europe/Rome] PraticheModel::insertPratica() - Dati filtrati per execute: Array
(
    [progetto_id] => 1
    [tipo_documento] => SCIA
    [numero_pratica] => TEST-1749589261
    [stato] => in_attesa
    [note] => Test post-aggiornamento database
    [data_apertura] => 2025-06-10 23:01:01
)

[10-Jun-2025 23:01:01 Europe/Rome] Errore in PraticheModel::insertPratica(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:01:01 Europe/Rome] Errore in PraticheModel::getPraticheInScadenza(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:01:01 Europe/Rome] Errore in PraticheModel::getPraticheInScadenza(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:01:01 Europe/Rome] NotificheModel::insertNotifica() - Dati ricevuti: Array
(
    [user_id] => 1
    [tipo] => sistema
    [titolo] => Test post-aggiornamento
    [messaggio] => Test notifica dopo aggiornamento database
    [priorita] => media
    [metadata] => Array
        (
        )

)

[10-Jun-2025 23:01:01 Europe/Rome] NotificheModel::insertNotifica() - Dati filtrati: Array
(
    [user_id] => 1
    [tipo] => sistema
    [titolo] => Test post-aggiornamento
    [messaggio] => Test notifica dopo aggiornamento database
    [priorita] => media
    [metadata] => []
)

[10-Jun-2025 23:01:01 Europe/Rome] Errore in NotificheModel::insertNotifica(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:01:01 Europe/Rome] NotificheModel::insertNotifica() - Dati ricevuti: Array
(
    [user_id] => 1
    [tipo] => pratica
    [titolo] => Pratica in scadenza critica
    [messaggio] => La pratica scade tra 2 giorni. Azione richiesta urgentemente.
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => Array
        (
            [pratica_id] => 1
            [giorni_rimanenti] => 2
            [tipo_notifica] => scadenza_critica
        )

)

[10-Jun-2025 23:01:01 Europe/Rome] NotificheModel::insertNotifica() - Dati filtrati: Array
(
    [user_id] => 1
    [tipo] => pratica
    [titolo] => Pratica in scadenza critica
    [messaggio] => La pratica scade tra 2 giorni. Azione richiesta urgentemente.
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => {"pratica_id":1,"giorni_rimanenti":2,"tipo_notifica":"scadenza_critica"}
)

[10-Jun-2025 23:01:01 Europe/Rome] Errore in NotificheModel::insertNotifica(): SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`studio_tecnico`.`notifiche`, CONSTRAINT `notifiche_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE)
[10-Jun-2025 23:01:01 Europe/Rome] NotificheModel::insertNotifica() - Dati ricevuti: Array
(
    [user_id] => 1
    [tipo] => documento
    [titolo] => Nuovo documento caricato
    [messaggio] => È stato caricato un nuovo documento: test.pdf
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => Array
        (
            [pratica_id] => 1
            [nome_documento] => test.pdf
            [tipo_notifica] => documento_caricato
        )

)

[10-Jun-2025 23:01:01 Europe/Rome] NotificheModel::insertNotifica() - Dati filtrati: Array
(
    [user_id] => 1
    [tipo] => documento
    [titolo] => Nuovo documento caricato
    [messaggio] => È stato caricato un nuovo documento: test.pdf
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => {"pratica_id":1,"nome_documento":"test.pdf","tipo_notifica":"documento_caricato"}
)

[10-Jun-2025 23:01:01 Europe/Rome] Errore in NotificheModel::insertNotifica(): SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`studio_tecnico`.`notifiche`, CONSTRAINT `notifiche_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE)
[10-Jun-2025 23:01:01 Europe/Rome] Errore in NotificheModel::getPraticheInScadenza(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:01:01 Europe/Rome] NotificationService::checkScadenzeAutomatiche() - Completato: {"scadenze_controllate":0,"pratiche_controllate":0,"notifiche_create":0,"errori":[]}
[10-Jun-2025 23:04:21 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:04:21 Europe/Rome] NotificheModel::insertNotifica() - Dati ricevuti: Array
(
    [user_id] => 1
    [tipo] => sistema
    [titolo] => Test diagnosi
    [messaggio] => Test notifica durante diagnosi
    [priorita] => bassa
    [metadata] => Array
        (
        )

)

[10-Jun-2025 23:04:21 Europe/Rome] NotificheModel::insertNotifica() - Dati filtrati: Array
(
    [user_id] => 1
    [tipo] => sistema
    [titolo] => Test diagnosi
    [messaggio] => Test notifica durante diagnosi
    [priorita] => bassa
    [metadata] => []
)

[10-Jun-2025 23:04:21 Europe/Rome] Errore in NotificheModel::insertNotifica(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:05:34 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:05:40 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:05:40 Europe/Rome] PraticheModel::insertPratica() - Dati ricevuti: Array
(
    [progetto_id] => 1
    [tipo_documento] => SCIA
    [numero_pratica] => TEST-1749589540
    [stato] => in_attesa
    [note] => Test post-aggiornamento database
)

[10-Jun-2025 23:05:40 Europe/Rome] PraticheModel::insertPratica() - Dati filtrati per execute: Array
(
    [progetto_id] => 1
    [tipo_documento] => SCIA
    [numero_pratica] => TEST-1749589540
    [stato] => in_attesa
    [note] => Test post-aggiornamento database
    [data_apertura] => 2025-06-10 23:05:40
)

[10-Jun-2025 23:05:40 Europe/Rome] Errore in PraticheModel::insertPratica(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:05:40 Europe/Rome] Errore in PraticheModel::getPraticheInScadenza(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:05:40 Europe/Rome] Errore in PraticheModel::getPraticheInScadenza(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:05:40 Europe/Rome] NotificheModel::insertNotifica() - Dati ricevuti: Array
(
    [user_id] => 1
    [tipo] => sistema
    [titolo] => Test post-aggiornamento
    [messaggio] => Test notifica dopo aggiornamento database
    [priorita] => media
    [metadata] => Array
        (
        )

)

[10-Jun-2025 23:05:40 Europe/Rome] NotificheModel::insertNotifica() - Dati filtrati: Array
(
    [user_id] => 1
    [tipo] => sistema
    [titolo] => Test post-aggiornamento
    [messaggio] => Test notifica dopo aggiornamento database
    [priorita] => media
    [metadata] => []
)

[10-Jun-2025 23:05:40 Europe/Rome] Errore in NotificheModel::insertNotifica(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:05:40 Europe/Rome] NotificheModel::insertNotifica() - Dati ricevuti: Array
(
    [user_id] => 1
    [tipo] => pratica
    [titolo] => Pratica in scadenza critica
    [messaggio] => La pratica scade tra 2 giorni. Azione richiesta urgentemente.
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => Array
        (
            [pratica_id] => 1
            [giorni_rimanenti] => 2
            [tipo_notifica] => scadenza_critica
        )

)

[10-Jun-2025 23:05:40 Europe/Rome] NotificheModel::insertNotifica() - Dati filtrati: Array
(
    [user_id] => 1
    [tipo] => pratica
    [titolo] => Pratica in scadenza critica
    [messaggio] => La pratica scade tra 2 giorni. Azione richiesta urgentemente.
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => {"pratica_id":1,"giorni_rimanenti":2,"tipo_notifica":"scadenza_critica"}
)

[10-Jun-2025 23:05:40 Europe/Rome] Errore in NotificheModel::insertNotifica(): SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`studio_tecnico`.`notifiche`, CONSTRAINT `notifiche_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE)
[10-Jun-2025 23:05:40 Europe/Rome] NotificheModel::insertNotifica() - Dati ricevuti: Array
(
    [user_id] => 1
    [tipo] => documento
    [titolo] => Nuovo documento caricato
    [messaggio] => È stato caricato un nuovo documento: test.pdf
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => Array
        (
            [pratica_id] => 1
            [nome_documento] => test.pdf
            [tipo_notifica] => documento_caricato
        )

)

[10-Jun-2025 23:05:40 Europe/Rome] NotificheModel::insertNotifica() - Dati filtrati: Array
(
    [user_id] => 1
    [tipo] => documento
    [titolo] => Nuovo documento caricato
    [messaggio] => È stato caricato un nuovo documento: test.pdf
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => {"pratica_id":1,"nome_documento":"test.pdf","tipo_notifica":"documento_caricato"}
)

[10-Jun-2025 23:05:40 Europe/Rome] Errore in NotificheModel::insertNotifica(): SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`studio_tecnico`.`notifiche`, CONSTRAINT `notifiche_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE)
[10-Jun-2025 23:05:40 Europe/Rome] Errore in NotificheModel::getPraticheInScadenza(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:05:40 Europe/Rome] NotificationService::checkScadenzeAutomatiche() - Completato: {"scadenze_controllate":0,"pratiche_controllate":0,"notifiche_create":0,"errori":[]}
[10-Jun-2025 23:12:14 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:12:14 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:12:14 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:12:14 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:12:14 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:12:14 Europe/Rome] Router inizializzato
[10-Jun-2025 23:12:14 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:12:14 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:12:14 Europe/Rome] Route home definita
[10-Jun-2025 23:12:14 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:12:14 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:12:14 Europe/Rome] Route progetti definite
[10-Jun-2025 23:12:14 Europe/Rome] Route clienti definite
[10-Jun-2025 23:12:14 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:12:14 Europe/Rome] URL richiesto: login
[10-Jun-2025 23:12:14 Europe/Rome] Route dispatched
[10-Jun-2025 23:12:20 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:12:20 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:12:20 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:12:20 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:12:20 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:12:20 Europe/Rome] Router inizializzato
[10-Jun-2025 23:12:20 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:12:20 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:12:20 Europe/Rome] Route home definita
[10-Jun-2025 23:12:20 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:12:20 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:12:20 Europe/Rome] Route progetti definite
[10-Jun-2025 23:12:20 Europe/Rome] Route clienti definite
[10-Jun-2025 23:12:20 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:12:20 Europe/Rome] URL richiesto: login
[2025-06-10 23:12:20][🔴 FATAL][Guest@::1][error_log.php:105] Eccezione non catturata: Call to undefined method App\Controllers\AuthController::requireCSRF()
Stack trace:
#0 [internal function]: App\Controllers\AuthController->login()
#1 C:\xampp\htdocs\progetti\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#2 C:\xampp\htdocs\progetti\studio_tecnico\index.php(152): App\Core\Router->dispatch('login')
#3 {main}
[10-Jun-2025 23:12:29 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:12:29 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:12:29 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:12:29 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:12:29 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:12:29 Europe/Rome] Router inizializzato
[10-Jun-2025 23:12:29 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:12:29 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:12:29 Europe/Rome] Route home definita
[10-Jun-2025 23:12:29 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:12:29 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:12:29 Europe/Rome] Route progetti definite
[10-Jun-2025 23:12:29 Europe/Rome] Route clienti definite
[10-Jun-2025 23:12:29 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:12:29 Europe/Rome] URL richiesto: login
[10-Jun-2025 23:12:29 Europe/Rome] Route dispatched
[10-Jun-2025 23:13:59 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:13:59 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:13:59 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:13:59 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:13:59 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:13:59 Europe/Rome] Router inizializzato
[10-Jun-2025 23:13:59 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:13:59 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:13:59 Europe/Rome] Route home definita
[10-Jun-2025 23:13:59 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:13:59 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:13:59 Europe/Rome] Route progetti definite
[10-Jun-2025 23:13:59 Europe/Rome] Route clienti definite
[10-Jun-2025 23:13:59 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:13:59 Europe/Rome] URL richiesto: login
[2025-06-10 23:13:59][🔴 FATAL][Guest@::1][error_log.php:105] Eccezione non catturata: Call to undefined method App\Controllers\AuthController::requireCSRF()
Stack trace:
#0 [internal function]: App\Controllers\AuthController->login()
#1 C:\xampp\htdocs\progetti\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#2 C:\xampp\htdocs\progetti\studio_tecnico\index.php(152): App\Core\Router->dispatch('login')
#3 {main}
[10-Jun-2025 23:14:06 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:14:06 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:14:06 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:14:06 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:14:06 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:14:06 Europe/Rome] Router inizializzato
[10-Jun-2025 23:14:06 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:14:06 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:14:06 Europe/Rome] Route home definita
[10-Jun-2025 23:14:06 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:14:06 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:14:06 Europe/Rome] Route progetti definite
[10-Jun-2025 23:14:06 Europe/Rome] Route clienti definite
[10-Jun-2025 23:14:06 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:14:06 Europe/Rome] URL richiesto: login
[10-Jun-2025 23:14:06 Europe/Rome] Route dispatched
[10-Jun-2025 23:14:15 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:14:15 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:14:15 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:14:15 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:14:15 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:14:15 Europe/Rome] Router inizializzato
[10-Jun-2025 23:14:15 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:14:15 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:14:15 Europe/Rome] Route home definita
[10-Jun-2025 23:14:15 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:14:15 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:14:15 Europe/Rome] Route progetti definite
[10-Jun-2025 23:14:15 Europe/Rome] Route clienti definite
[10-Jun-2025 23:14:15 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:14:15 Europe/Rome] URL richiesto: login
[2025-06-10 23:14:15][🔴 FATAL][Guest@::1][error_log.php:105] Eccezione non catturata: Call to undefined method App\Controllers\AuthController::requireCSRF()
Stack trace:
#0 [internal function]: App\Controllers\AuthController->login()
#1 C:\xampp\htdocs\progetti\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#2 C:\xampp\htdocs\progetti\studio_tecnico\index.php(152): App\Core\Router->dispatch('login')
#3 {main}
[10-Jun-2025 23:14:56 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:14:56 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:14:56 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:14:56 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:14:56 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:14:56 Europe/Rome] Router inizializzato
[10-Jun-2025 23:14:56 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:14:56 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:14:56 Europe/Rome] Route home definita
[10-Jun-2025 23:14:56 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:14:56 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:14:56 Europe/Rome] Route progetti definite
[10-Jun-2025 23:14:56 Europe/Rome] Route clienti definite
[10-Jun-2025 23:14:56 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:14:56 Europe/Rome] URL richiesto: 
[10-Jun-2025 23:14:56 Europe/Rome] Utente non autenticato, redirect al login
[10-Jun-2025 23:14:56 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:14:56 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:14:56 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:14:56 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:14:56 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:14:56 Europe/Rome] Router inizializzato
[10-Jun-2025 23:14:56 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:14:56 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:14:56 Europe/Rome] Route home definita
[10-Jun-2025 23:14:56 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:14:56 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:14:56 Europe/Rome] Route progetti definite
[10-Jun-2025 23:14:56 Europe/Rome] Route clienti definite
[10-Jun-2025 23:14:56 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:14:56 Europe/Rome] URL richiesto: login
[10-Jun-2025 23:14:56 Europe/Rome] Route dispatched
[10-Jun-2025 23:15:37 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:15:37 Europe/Rome] PraticheModel::insertPratica() - Dati ricevuti: Array
(
    [progetto_id] => 1
    [tipo_documento] => SCIA
    [numero_pratica] => TEST-1749590137
    [stato] => in_attesa
    [note] => Test post-aggiornamento database
)

[10-Jun-2025 23:15:37 Europe/Rome] PraticheModel::insertPratica() - Dati filtrati per execute: Array
(
    [progetto_id] => 1
    [tipo_documento] => SCIA
    [numero_pratica] => TEST-1749590137
    [stato] => in_attesa
    [note] => Test post-aggiornamento database
    [data_apertura] => 2025-06-10 23:15:37
)

[10-Jun-2025 23:15:37 Europe/Rome] Errore in PraticheModel::insertPratica(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:15:37 Europe/Rome] Errore in PraticheModel::getPraticheInScadenza(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:15:37 Europe/Rome] Errore in PraticheModel::getPraticheInScadenza(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:15:37 Europe/Rome] NotificheModel::insertNotifica() - Dati ricevuti: Array
(
    [user_id] => 1
    [tipo] => sistema
    [titolo] => Test post-aggiornamento
    [messaggio] => Test notifica dopo aggiornamento database
    [priorita] => media
    [metadata] => Array
        (
        )

)

[10-Jun-2025 23:15:37 Europe/Rome] NotificheModel::insertNotifica() - Dati filtrati: Array
(
    [user_id] => 1
    [tipo] => sistema
    [titolo] => Test post-aggiornamento
    [messaggio] => Test notifica dopo aggiornamento database
    [priorita] => media
    [metadata] => []
)

[10-Jun-2025 23:15:37 Europe/Rome] Errore in NotificheModel::insertNotifica(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:15:37 Europe/Rome] NotificheModel::insertNotifica() - Dati ricevuti: Array
(
    [user_id] => 1
    [tipo] => pratica
    [titolo] => Pratica in scadenza critica
    [messaggio] => La pratica scade tra 2 giorni. Azione richiesta urgentemente.
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => Array
        (
            [pratica_id] => 1
            [giorni_rimanenti] => 2
            [tipo_notifica] => scadenza_critica
        )

)

[10-Jun-2025 23:15:37 Europe/Rome] NotificheModel::insertNotifica() - Dati filtrati: Array
(
    [user_id] => 1
    [tipo] => pratica
    [titolo] => Pratica in scadenza critica
    [messaggio] => La pratica scade tra 2 giorni. Azione richiesta urgentemente.
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => {"pratica_id":1,"giorni_rimanenti":2,"tipo_notifica":"scadenza_critica"}
)

[10-Jun-2025 23:15:37 Europe/Rome] Errore in NotificheModel::insertNotifica(): SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`studio_tecnico`.`notifiche`, CONSTRAINT `notifiche_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE)
[10-Jun-2025 23:15:37 Europe/Rome] NotificheModel::insertNotifica() - Dati ricevuti: Array
(
    [user_id] => 1
    [tipo] => documento
    [titolo] => Nuovo documento caricato
    [messaggio] => È stato caricato un nuovo documento: test.pdf
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => Array
        (
            [pratica_id] => 1
            [nome_documento] => test.pdf
            [tipo_notifica] => documento_caricato
        )

)

[10-Jun-2025 23:15:37 Europe/Rome] NotificheModel::insertNotifica() - Dati filtrati: Array
(
    [user_id] => 1
    [tipo] => documento
    [titolo] => Nuovo documento caricato
    [messaggio] => È stato caricato un nuovo documento: test.pdf
    [priorita] => media
    [link_azione] => /pratiche/dettagli/1
    [metadata] => {"pratica_id":1,"nome_documento":"test.pdf","tipo_notifica":"documento_caricato"}
)

[10-Jun-2025 23:15:37 Europe/Rome] Errore in NotificheModel::insertNotifica(): SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`studio_tecnico`.`notifiche`, CONSTRAINT `notifiche_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE)
[10-Jun-2025 23:15:37 Europe/Rome] Errore in NotificheModel::getPraticheInScadenza(): SQLSTATE[HY093]: Invalid parameter number
[10-Jun-2025 23:15:37 Europe/Rome] NotificationService::checkScadenzeAutomatiche() - Completato: {"scadenze_controllate":0,"pratiche_controllate":0,"notifiche_create":0,"errori":[]}
[10-Jun-2025 23:18:25 Europe/Rome] PHP Fatal error:  Access level to App\Controllers\PraticheController::isAjax() must be protected (as in class App\Core\Controller) or weaker in C:\xampp\htdocs\progetti\studio_tecnico\app\controllers\PraticheController.php on line 320
[10-Jun-2025 23:19:42 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:19:42 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:19:42 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:19:42 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:19:42 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:19:42 Europe/Rome] Router inizializzato
[10-Jun-2025 23:19:42 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:19:42 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:19:42 Europe/Rome] Route home definita
[10-Jun-2025 23:19:42 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:19:42 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:19:42 Europe/Rome] Route progetti definite
[10-Jun-2025 23:19:42 Europe/Rome] Route clienti definite
[10-Jun-2025 23:19:42 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:19:42 Europe/Rome] URL richiesto: index.php
[10-Jun-2025 23:19:42 Europe/Rome] Route dispatched
[10-Jun-2025 23:19:50 Europe/Rome] PHP Fatal error:  Access level to App\Controllers\PraticheController::isAjax() must be protected (as in class App\Core\Controller) or weaker in C:\xampp\htdocs\progetti\studio_tecnico\app\controllers\PraticheController.php on line 320
[10-Jun-2025 23:21:33 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:21:33 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:21:33 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:21:33 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:21:33 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:21:33 Europe/Rome] Router inizializzato
[10-Jun-2025 23:21:33 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:21:33 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:21:33 Europe/Rome] Route home definita
[10-Jun-2025 23:21:33 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:21:33 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:21:33 Europe/Rome] Route progetti definite
[10-Jun-2025 23:21:33 Europe/Rome] Route clienti definite
[10-Jun-2025 23:21:33 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:21:33 Europe/Rome] URL richiesto: index.php
[10-Jun-2025 23:21:33 Europe/Rome] Route dispatched
[10-Jun-2025 23:22:29 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:22:29 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:22:29 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:22:29 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:22:29 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:22:29 Europe/Rome] Router inizializzato
[10-Jun-2025 23:22:29 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:22:29 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:22:29 Europe/Rome] Route home definita
[10-Jun-2025 23:22:29 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:22:29 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:22:29 Europe/Rome] Route progetti definite
[10-Jun-2025 23:22:29 Europe/Rome] Route clienti definite
[10-Jun-2025 23:22:29 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:22:29 Europe/Rome] URL richiesto: index.php
[10-Jun-2025 23:22:29 Europe/Rome] Route dispatched
[10-Jun-2025 23:23:30 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:23:30 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:23:30 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:23:30 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:23:30 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:23:30 Europe/Rome] Router inizializzato
[10-Jun-2025 23:23:30 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:23:30 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:23:30 Europe/Rome] Route home definita
[10-Jun-2025 23:23:30 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:23:30 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:23:30 Europe/Rome] Route progetti definite
[10-Jun-2025 23:23:30 Europe/Rome] Route clienti definite
[10-Jun-2025 23:23:30 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:23:30 Europe/Rome] URL richiesto: login
[10-Jun-2025 23:23:30 Europe/Rome] Route dispatched
[10-Jun-2025 23:23:37 Europe/Rome] Bootstrap e Autoloader caricati
[10-Jun-2025 23:23:37 Europe/Rome] Autoloader registrato
[10-Jun-2025 23:23:37 Europe/Rome] Tentativo di connessione al database
[10-Jun-2025 23:23:37 Europe/Rome] Connessione al database stabilita con successo
[10-Jun-2025 23:23:37 Europe/Rome] Connessione al database ottenuta
[10-Jun-2025 23:23:37 Europe/Rome] Router inizializzato
[10-Jun-2025 23:23:37 Europe/Rome] Route di autenticazione definite
[10-Jun-2025 23:23:37 Europe/Rome] Route profilo admin definite
[10-Jun-2025 23:23:37 Europe/Rome] Route home definita
[10-Jun-2025 23:23:37 Europe/Rome] Route dashboard definita
[10-Jun-2025 23:23:37 Europe/Rome] Route pratiche definite
[10-Jun-2025 23:23:37 Europe/Rome] Route progetti definite
[10-Jun-2025 23:23:37 Europe/Rome] Route clienti definite
[10-Jun-2025 23:23:37 Europe/Rome] Route scadenze definite
[10-Jun-2025 23:23:37 Europe/Rome] URL richiesto: login
[2025-06-10 23:23:37][🔴 FATAL][Guest@::1][error_log.php:105] Eccezione non catturata: Call to undefined method App\Controllers\AuthController::requireCSRF()
Stack trace:
#0 [internal function]: App\Controllers\AuthController->login()
#1 C:\xampp\htdocs\progetti\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#2 C:\xampp\htdocs\progetti\studio_tecnico\index.php(152): App\Core\Router->dispatch('login')
#3 {main}
