<?php
namespace App\Models;

use App\Config\Database;
use PDO;
use PDOException;

class ClientiModel {
    private $db;

    public function __construct() {
        try {
            $this->db = Database::getInstance();
            if (!$this->db instanceof PDO) {
                throw new PDOException("Connessione al database non valida nel ClientiModel.");
            }
        } catch (PDOException $e) {
            error_log("Errore connessione DB in ClientiModel: " . $e->getMessage());
        }
    }

    public function getClienteById($id) {
        $stmt = $this->db->prepare("SELECT * FROM clienti WHERE id = :id");
        $stmt->execute([':id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getClientByEmail(string $email) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM clienti WHERE email = :email");
            $stmt->execute([':email' => $email]);
            return $stmt->fetch(PDO::FETCH_ASSOC); // Restituisce i dati del cliente o false se non trovato
        } catch (PDOException $e) {
            error_log("Errore in ClientiModel::getClientByEmail(): " . $e->getMessage());
            return false; // In caso di errore, considera l'email come non trovata o gestisci diversamente
        }
    }

    public function updateCliente($data) {
        error_log("ClientiModel::updateCliente() - Dati ricevuti: " . print_r($data, true));
        if (!isset($data['id'])) {
            error_log("ID cliente mancante per l'aggiornamento in ClientiModel");
            return false;
        }
        $query = "UPDATE clienti SET 
            tipo_cliente = :tipo_cliente,
            nome = :nome,
            cognome = :cognome,
            codice_fiscale = :codice_fiscale,
            ragione_sociale = :ragione_sociale,
            partita_iva = :partita_iva,
            email = :email,
            telefono = :telefono,
            indirizzo = :indirizzo,
            cap_cliente = :cap_cliente, 
            citta = :citta,
            provincia = :provincia,
            note = :note
            WHERE id = :id";

        $stmt = $this->db->prepare($query);
        $allowed_keys = [
            'tipo_cliente', 'nome', 'cognome', 'codice_fiscale', 'ragione_sociale',
            'partita_iva', 'email', 'telefono', 'indirizzo', 'cap_cliente',
            'citta', 'provincia', 'note', 'id'
        ];
        $filtered_data = array_intersect_key($data, array_flip($allowed_keys));
        error_log("ClientiModel::updateCliente() - Dati filtrati per execute: " . print_r($filtered_data, true));
        
        foreach ($allowed_keys as $key) {
            if (!array_key_exists($key, $filtered_data)) {
            }
        }

        $success = $stmt->execute($filtered_data);
        if (!$success) {
            error_log("ClientiModel::updateCliente() - Errore SQL: " . print_r($stmt->errorInfo(), true));
        }
        error_log("ClientiModel::updateCliente() - Esito: " . ($success ? 'Successo' : 'Fallimento'));
        return $success;
    }

    public function insertCliente($data) {
        error_log("ClientiModel::insertCliente() - Dati ricevuti: " . print_r($data, true));
        $query = "INSERT INTO clienti (
            tipo_cliente, nome, cognome, codice_fiscale, ragione_sociale,
            partita_iva, email, telefono, indirizzo, cap_cliente, 
            citta, provincia, note
        ) VALUES (
            :tipo_cliente, :nome, :cognome, :codice_fiscale, :ragione_sociale,
            :partita_iva, :email, :telefono, :indirizzo, :cap_cliente,
            :citta, :provincia, :note
        )";

        $stmt = $this->db->prepare($query);
        $allowed_keys = [
            'tipo_cliente', 'nome', 'cognome', 'codice_fiscale', 'ragione_sociale',
            'partita_iva', 'email', 'telefono', 'indirizzo', 'cap_cliente',
            'citta', 'provincia', 'note'
        ];
        $filtered_data = array_intersect_key($data, array_flip($allowed_keys));
        error_log("ClientiModel::insertCliente() - Dati filtrati per execute: " . print_r($filtered_data, true));
        
        $success = $stmt->execute($filtered_data);
        if (!$success) {
            error_log("ClientiModel::insertCliente() - Errore SQL: " . print_r($stmt->errorInfo(), true));
        }
        error_log("ClientiModel::insertCliente() - Esito: " . ($success ? 'Successo' : 'Fallimento'));
        return $success;
    }

    public function getAllClienti() {
        try {
            $tableInfo = $this->db->query("SHOW COLUMNS FROM clienti LIKE 'attivo'");
            $hasActiveColumn = $tableInfo->rowCount() > 0;

            $query = "
                SELECT c.*, 
                       COUNT(DISTINCT p.id) as num_progetti,
                       COUNT(DISTINCT pr.id) as num_pratiche
                FROM clienti c
                LEFT JOIN progetti p ON c.id = p.cliente_id
                LEFT JOIN pratiche pr ON p.id = pr.progetto_id
            ";

            if ($hasActiveColumn) {
                $query .= " WHERE c.attivo = 1";
            }

            $query .= " GROUP BY c.id
                       ORDER BY 
                           CASE 
                               WHEN c.tipo_cliente = 'privato' THEN c.cognome 
                               ELSE c.ragione_sociale 
                           END";

            $stmt = $this->db->query($query);
            
            if ($stmt === false) {
                throw new PDOException("Errore nell'esecuzione della query per getAllClienti");
            }
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in ClientiModel::getAllClienti(): " . $e->getMessage());
            return []; 
        }
    }

    public function getProgettiByClienteId($cliente_id) {
        try {
            $query = "
                SELECT 
                    p.*,
                    (SELECT COUNT(*) FROM pratiche pr WHERE pr.progetto_id = p.id) as num_pratiche
                FROM progetti p 
                WHERE p.cliente_id = :cliente_id 
                ORDER BY p.data_inizio DESC";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([':cliente_id' => $cliente_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Errore in ClientiModel::getProgettiByClienteId(): " . $e->getMessage());
            return [];
        }
    }

    public function getPraticheByClienteId($cliente_id) {
        try {
            $query = "SELECT 
                pr.*,
                p.nome_progetto,
                p.cliente_id
            FROM pratiche pr
            JOIN progetti p ON pr.progetto_id = p.id
            WHERE p.cliente_id = :cliente_id
            ORDER BY pr.data_apertura DESC";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([':cliente_id' => $cliente_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Errore in ClientiModel::getPraticheByClienteId(): " . $e->getMessage());
            return [];
        }
    }

    public function countProgettiByClienteId($cliente_id) {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) as num_progetti FROM progetti WHERE cliente_id = :cliente_id");
            $stmt->execute([':cliente_id' => $cliente_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? (int)$result['num_progetti'] : 0;
        } catch (PDOException $e) {
            error_log("Errore in ClientiModel::countProgettiByClienteId(): " . $e->getMessage());
            return 0; 
        }
    }

    public function deleteClienteById($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM clienti WHERE id = :id");
            return $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            error_log("Errore in ClientiModel::deleteClienteById(): " . $e->getMessage());
            return false;
        }
    }
}
?> 