<?php
// Av<PERSON> della sessione
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Definisci il percorso root del progetto
define('ROOT_PATH', dirname(dirname(__FILE__)));

// Impostazione del percorso base per XAMPP
$projectFolder = 'progetti/studio_tecnico';

// Costruisci il BASE_URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
// Imposta il percorso base
if (!defined('BASE_URL')) {
    define('BASE_URL', $protocol . $host . '/' . $projectFolder . '/');
}

// Definizione delle costanti per le viste
if (!defined('VIEWS_DIR')) {
    define('VIEWS_DIR', ROOT_PATH . '/views');
}

// Configurazione del database
define('DB_HOST', 'localhost');
define('DB_NAME', 'studio_tecnico');
define('DB_USER', 'root');
define('DB_PASS', '');

// Impostazione del reporting degli errori
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Impostazione del fuso orario
date_default_timezone_set('Europe/Rome');

// Carica la configurazione del database
require_once __DIR__ . '/database.php';