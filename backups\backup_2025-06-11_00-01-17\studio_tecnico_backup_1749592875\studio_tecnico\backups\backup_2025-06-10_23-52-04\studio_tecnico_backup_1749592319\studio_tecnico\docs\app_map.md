# Mappa dell'Applicazione: Studio Tecnico

Questo documento serve come mappa e guida di riferimento per il progetto "Studio Tecnico". È fondamentale mantenerlo aggiornato per riflettere lo stato attuale dell'applicazione.

## 1. Mappatura del Progetto

### 1.1. Struttura Generale delle Cartelle e File Principali

L'applicazione segue una struttura ispirata al pattern MVC (Model-View-Controller).

*   **`/` (Root del Progetto)**
    *   `.htaccess`: Regole di URL rewriting e configurazioni per Apache.
    *   `index.php`: Entry point principale dell'applicazione. Carica il bootstrap.
    *   `bootstrap.php`: (Presumibilmente, basato sulla struttura comune) Inizializza l'applicazione, carica configurazioni, autoloader e router.
    *   `composer.json`: (Se presente) Gestione delle dipendenze PHP con Composer.
    *   `README.md`: (Consigliato) Informazioni generali sul progetto.


*   **`api/`**: Contiene script PHP per endpoint API.
    *   `clienti/`: API relative ai clienti.
    *   `pratiche/`: API relative alle pratiche.
    *   `progetti/`: API relative ai progetti.
    *   `config.php`: Probabile file di configurazione specifico per le API.
    *   `log-theme.php`: Script per la gestione del tema via API.

*   **`app/`**: Cuore dell'applicazione, contiene la logica di business.
    *   `Config/`:
        *   `Database.php`: Gestisce la connessione al database (Singleton PDO).
    *   `classes/`:
        *   `BackupManager.php`: Classe per la gestione dei backup.
    *   `controllers/`: Contiene i Controller che gestiscono le richieste HTTP, interagiscono con i Model e selezionano le View.
        *   `AdminController.php`, `AdminProfileController.php`, `AllegatiController.php`, `AuthController.php`, `ClientiController.php`, `ConfigController.php`, `DashboardController.php`, `HomeController.php`, `NotificheController.php` ✅, `PraticheController.php` ✅, `ProgettiController.php`, `ScadenzeController.php`.
    *   `core/`: Componenti fondamentali del framework custom.
        *   `Autoloader.php`: Carica automaticamente le classi.
        *   `Controller.php`: Classe base per tutti i controller con protezione CSRF integrata ✅.
        *   `Router.php`: Gestisce il routing delle URL ai controller e metodi appropriati.
        *   `Security.php`: Fornisce utility avanzate per la sicurezza (CSRF, sanificazione, validazione file) ✅.
    *   `helpers/`: (Se presenti) Funzioni helper riutilizzabili.
    *   `models/`: Contiene i Model che rappresentano i dati e la logica per interagire con il database.
        *   `Allegato.php`, `ClientiModel.php`, `ProgettiModel.php` ✅, `NotificheModel.php` ✅, `PraticheModel.php` ✅, `User.php`.
    *   `services/`: Servizi per la logica di business avanzata.
        *   `NotificationService.php` ✅ - Gestione notifiche automatiche, controllo scadenze e workflow pratiche.

*   **`assets/`**: File statici come CSS, JavaScript, immagini.
    *   `css/`: Fogli di stile.
        *   `login.css`, `management.css`, `style.css`, `theme-dark.css`, `theme-light.css`.
    *   `js/`: Script JavaScript.
        *   `scripts.js`, `theme-switcher.js`, `notifications.js`, `validation.js`.
    *   `images/`: Immagini utilizzate nell'applicazione.
        *   `favicon.ico`, `logo.png`, `logo_placeholder.png`.
    *   `libs/`: Librerie JavaScript/CSS di terze parti (es. Bootstrap, DataTables, FullCalendar, SweetAlert2, Chart.js, Select2).

*   **`config/`**: File di configurazione dell'applicazione.
    *   `app.php`: Configurazioni specifiche dell'applicazione (nome app, logo, ecc.).
    *   `config.php`: Configurazioni principali (BASE_URL, percorsi, credenziali DB, timezone).
    *   `database.php`: (Potrebbe essere ridondante se `app/Config/Database.php` è il principale).

*   **`docs/`**: Documentazione del progetto.
    *   `aggiornamento.md`: Piano di ammodernamento della webapp, include pianificazione operativa e resoconti sessioni.
    *   `app_map.md`: Questo file, la mappa dell'applicazione.
    *   `BACKUP_REPORT.md`: Report sulla funzionalità di backup (spostato dalla root).
    *   `README.md`: Descrizione generale del progetto per GitHub.
    *   `webapp_structure.md`: Descrizione dettagliata della struttura e dei componenti della webapp.

*   **`includes/`**: Pezzi di codice PHP riutilizzabili, spesso inclusi direttamente.
    *   `footer.php`, `header.php`, `navbar.php`, `sidebar.php`.

*   **`logs/`**: File di log dell'applicazione.
    *   `error.log`: Log degli errori PHP.
    *   `error_log.php`: Script per la configurazione del logging.

*   **`public/`**: Dovrebbe essere la document root del server web. `index.php` e `assets/` potrebbero risiedere qui per maggiore sicurezza. Attualmente, la root del progetto sembra coincidere con la document root.
    *   `uploads/`: Cartella per i file caricati dagli utenti.
        *   `allegati/`: File allegati alle pratiche.

*   **`views/`**: Contiene i file di presentazione (template HTML/PHP).
    *   `admin/`: Viste per la sezione di amministrazione.
    *   `auth/`: Viste per login, registrazione.
    *   `clienti/`: Viste per la gestione dei clienti.
    *   `components/`: Componenti riutilizzabili delle viste (es. modali, form).
    *   `dashboard/`: Vista per la dashboard principale.
    *   `errors/`: Pagine di errore (403, 404, 500).
    *   `layouts/`: Layout principali (header, footer, master).
    *   `notifiche/`: Viste per le notifiche.
    *   `pratiche/`: Viste per la gestione delle pratiche.
    *   `progetti/`: Viste per la gestione dei progetti.
    *   `scadenze/`: Viste per la gestione delle scadenze.
    *   `user/`: Viste per il profilo utente.
    *   `home.php`: Vista per la homepage.

### 1.2. Descrizione Funzionale dei Componenti Chiave

*   **Autenticazione**: Gestita da `AuthController` e `User` model.
*   **Gestione Clienti, Progetti, Pratiche**: Gestita dai rispettivi controller (`ClientiController`, `ProgettiController` ✅, `PraticheController` ✅) con Models dedicati. Tutti i controller ora utilizzano i rispettivi Models per operazioni database e workflow automatizzati.
*   **Amministrazione**: `AdminController` per la gestione utenti e log, `ConfigController` per le impostazioni dell'app, `AdminProfileController` per il profilo admin.
*   **Allegati**: `AllegatiController` e `AllegatoModel` gestiscono upload/download di file.
*   **Notifiche**: `NotificheController` e `NotificaModel` per un sistema di notifiche utente e avvisi di scadenze.

## 2. Guida di Riferimento

*   **Ambiente di Sviluppo**:
    *   PHP (versione da verificare, consigliato 7.4+ o 8.x)
    *   Server Web: Apache (tramite XAMPP)
    *   Database: MySQL/MariaDB (tramite XAMPP)
*   **Regole Base da Seguire**:
    *   Seguire lo stile di codice PSR-12.
    *   Scrivere codice modulare e commentato.
    *   Utilizzare type hints.
    *   Privilegiare la leggibilità.
    *   Mantenere aggiornata questa mappa.
*   **File Critici**:
    *   `config/config.php`: Contiene credenziali e configurazioni base. Modificare con cautela.
    *   `.htaccess`: Modifiche errate possono compromettere il funzionamento dell'URL rewriting.
    *   File nel core (`app/core/`): Modifiche possono impattare l'intero framework.

## 3. Documentazione Tecnica

*   **Architettura del Sistema**: MVC-like (Model-View-Controller).
    *   **Router** (`app/core/Router.php`) smista le richieste.
    *   **Controller** (`app/controllers/`) gestiscono la logica della richiesta.
    *   **Model** (`app/models/`) gestiscono l'interazione con i dati.
    *   **View** (`views/`) presentano i dati.
*   **Database**:
    *   Connessione tramite PDO, gestita da `app/Config/Database.php` (classe Singleton).
    *   Le query sono attualmente un misto di query dirette nei controller e query incapsulate nei model. L'obiettivo è spostare tutte le query nei model.
*   **API**: Endpoint disponibili nella cartella `api/`.

## 4. Registro Modifiche

*   **[2025-01-05]**:
    *   Analisi completa del codice completata
    *   Identificate aree di miglioramento e nuove funzionalità
    *   Aggiornato app_map.md con struttura attuale
    *   **IMPLEMENTATO**: ProgettiModel.php con CRUD completo e type hints
    *   **AGGIORNATO**: ProgettiController.php per utilizzare il nuovo Model
    *   **IMPLEMENTATO**: CSRF Protection completa in Security.php
    *   **AGGIORNATO**: Controller.php con protezione CSRF integrata
    *   **AGGIORNATO**: ProgettiController, ClientiController, AuthController con nuova protezione CSRF
    *   **IMPLEMENTATO**: Sistema Notifiche Completo
        *   NotificheModel.php con gestione avanzata notifiche e preferenze
        *   NotificheController.php con dashboard e API AJAX
        *   NotificationService.php per controllo automatico scadenze
        *   Schema database notifiche con tabelle e stored procedures
        *   Test completi per tutte le funzionalità
    *   **IMPLEMENTATO**: PraticheModel.php e Workflow Automatizzato
        *   PraticheModel.php con CRUD completo e gestione workflow stati
        *   PraticheController.php aggiornato per utilizzare il nuovo Model
        *   Workflow automatizzato con transizioni di stato validate
        *   Integrazione completa con sistema notifiche
        *   Gestione scadenze automatiche e pratiche critiche
        *   Test completi per workflow e funzionalità
    *   **COMPLETATO**: Aggiornamento Database per Nuove Funzionalità
        *   Schema database aggiornato con 6 stati pratiche (era 3)
        *   Tabelle notifiche e preferenze create e configurate
        *   Indici performance aggiunti per query ottimizzate
        *   Stored procedures e triggers per automazione
        *   Backup automatico e verifica integrità
        *   Test completi post-aggiornamento superati
    *   **RISOLTO**: Problemi Database e Funzionalità Operative
        *   Diagnosi completa problemi con script automatico
        *   Correzione automatica di tutti i problemi identificati
        *   Verifica funzionalità end-to-end completata
        *   Sistema 100% operativo con tutte le funzionalità attive
        *   Workflow pratiche e notifiche completamente funzionanti
    *   **COMPLETATO**: Pulizia File di Test e Obsoleti
        *   Rimossi tutti i file di test temporanei
        *   Eliminati script di diagnosi e correzione emergency
        *   Mantenuti solo file di produzione e documentazione
        *   Progetto pulito e pronto per produzione
*   **[2025-05-24]**:
    *   Creato `docs/aggiornamento.md` con il piano di ammodernamento della webapp.
    *   Creato questo file `docs/app_map.md`.
    *   Spostato `BACKUP_REPORT.md` dalla root a `docs/`.
    *   Eliminato `app_map.md` (deprecato) dalla root.
    *   Creato `docs/README.md` con la descrizione del progetto per GitHub.

## 5. Guida alla Manutenzione

*   **Procedure di Debug**:
    *   Controllare i log in `logs/error.log`.
    *   Utilizzare `var_dump()`, `print_r()` con `die()` per ispezionare le variabili durante lo sviluppo.
    *   Abilitare `display_errors` in `config/config.php` per l'ambiente di sviluppo.
*   **Gestione Errori Comuni**:
    *   **Errori 404 (Not Found)**: Verificare le rotte in `app/core/Router.php` e i percorsi dei file.
    *   **Errori 500 (Internal Server Error)**: Controllare i log per errori PHP fatali.
    *   **Errori di Database**: Verificare le credenziali in `config/config.php`, la struttura delle tabelle e la sintassi SQL.
*   **Backup**: Utilizzare lo script `BackupCompleto.php` o la funzionalità integrata (se presente) regolarmente.
*   **Best Practices**:
    *   Testare le modifiche in un ambiente di staging prima del deploy in produzione.
    *   Versionare il codice con Git.
    *   Mantenere le dipendenze aggiornate (se si usa Composer).

---
*Questo documento è generato e mantenuto da Cascade, assistente AI.*
