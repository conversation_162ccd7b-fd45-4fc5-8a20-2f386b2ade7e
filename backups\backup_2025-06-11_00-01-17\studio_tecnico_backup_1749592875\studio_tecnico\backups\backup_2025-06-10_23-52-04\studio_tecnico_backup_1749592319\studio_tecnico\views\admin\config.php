<?php 
include VIEWS_DIR . '/layouts/header.php';

// Carica la configurazione in modo sicuro
$configFile = ROOT_PATH . '/config/app.php';
$config = [];
if (file_exists($configFile)) {
    $config = require $configFile;
}

// Assicurati che tutte le chiavi necessarie esistano
$config = array_merge([
    'app_name' => 'Studio Tecnico',
    'timezone' => 'Europe/Rome',
    'debug' => false,
    'logo_path' => null,
    'mail' => [
        'host' => '',
        'port' => '',
        'encryption' => 'tls',
        'username' => '',
        'password' => '',
        'from_address' => '',
        'from_name' => ''
    ]
], $config);
?>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Configurazione Applicazione</h4>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success">
                            <?php 
                            echo htmlspecialchars($_SESSION['success']);
                            unset($_SESSION['success']);
                            ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger">
                            <?php 
                            echo htmlspecialchars($_SESSION['error']);
                            unset($_SESSION['error']);
                            ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="<?= BASE_URL ?>admin/config/save" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="app_name" class="form-label">Nome Applicazione</label>
                            <input type="text" class="form-control" id="app_name" name="app_name" 
                                   value="<?= htmlspecialchars($config['app_name'] ?? '') ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="timezone" class="form-label">Timezone</label>
                            <select class="form-select" id="timezone" name="timezone">
                                <?php $timezones = DateTimeZone::listIdentifiers(); ?>
                                <?php foreach ($timezones as $tz): ?>
                                    <option value="<?= htmlspecialchars($tz) ?>" 
                                            <?= ($config['timezone'] ?? 'Europe/Rome') === $tz ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($tz) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="debug" name="debug" 
                                       <?= isset($config['debug']) && $config['debug'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="debug">Modalità Debug</label>
                            </div>
                        </div>

                        <h5 class="mt-4 mb-3">Configurazione Email</h5>

                        <div class="mb-3">
                            <label for="mail_host" class="form-label">SMTP Host</label>
                            <input type="text" class="form-control" id="mail_host" name="mail_host" 
                                   value="<?= htmlspecialchars($config['mail']['host'] ?? '') ?>">
                        </div>

                        <div class="mb-3">
                            <label for="mail_port" class="form-label">SMTP Porta</label>
                            <input type="number" class="form-control" id="mail_port" name="mail_port" 
                                   value="<?= htmlspecialchars($config['mail']['port'] ?? '') ?>">
                        </div>

                        <div class="mb-3">
                            <label for="mail_encryption" class="form-label">Crittografia</label>
                            <select class="form-select" id="mail_encryption" name="mail_encryption">
                                <option value="tls" <?= ($config['mail']['encryption'] ?? 'tls') === 'tls' ? 'selected' : '' ?>>TLS</option>
                                <option value="ssl" <?= ($config['mail']['encryption'] ?? '') === 'ssl' ? 'selected' : '' ?>>SSL</option>
                                <option value="none" <?= ($config['mail']['encryption'] ?? '') === 'none' ? 'selected' : '' ?>>Nessuna</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="mail_username" class="form-label">SMTP Username</label>
                            <input type="text" class="form-control" id="mail_username" name="mail_username" 
                                   value="<?= htmlspecialchars($config['mail']['username'] ?? '') ?>">
                        </div>

                        <div class="mb-3">
                            <label for="mail_password" class="form-label">SMTP Password</label>
                            <input type="password" class="form-control" id="mail_password" name="mail_password" 
                                   value="<?= htmlspecialchars($config['mail']['password'] ?? '') ?>">
                        </div>

                        <div class="mb-3">
                            <label for="mail_from_address" class="form-label">Indirizzo Mittente</label>
                            <input type="email" class="form-control" id="mail_from_address" name="mail_from_address" 
                                   value="<?= htmlspecialchars($config['mail']['from_address'] ?? '') ?>">
                        </div>

                        <div class="mb-3">
                            <label for="mail_from_name" class="form-label">Nome Mittente</label>
                            <input type="text" class="form-control" id="mail_from_name" name="mail_from_name" 
                                   value="<?= htmlspecialchars($config['mail']['from_name'] ?? '') ?>">
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Salva Configurazione
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>
