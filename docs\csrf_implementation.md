# Implementazione CSRF Protection - Studio Tecnico

## 📋 Panoramica

È stata implementata una protezione CSRF completa per migliorare la sicurezza dell'applicazione Studio Tecnico. La protezione previene attacchi Cross-Site Request Forgery e include funzionalità avanzate di sanificazione input e validazione file.

## 🔧 Componenti Implementati

### 1. Security.php - Classe Migliorata

**Percorso**: `app/core/Security.php`

#### Nuovi Metodi Principali:

- **`generateCSRFToken(): string`** - Genera token CSRF sicuri con fallback
- **`validateCSRFToken(string $token): bool`** - Valida token CSRF con logging
- **`sanitizeInput(mixed $data): mixed`** - Sanifica input ricorsivamente
- **`validateFileUpload(array $file): array`** - Validazione completa file upload
- **`generateSafeFilename(string $originalName): string`** - Genera nomi file sicuri
- **`verifyCSRFToken(): bool`** - Verifica token dalla richiesta corrente
- **`requireValidCSRF(): void`** - Lancia eccezione se token non valido

#### Caratteristiche di Sicurezza:

- **Token sicuri**: Generati con `random_bytes(32)` e fallback
- **Validazione robusta**: Usa `hash_equals()` per prevenire timing attacks
- **Sanificazione avanzata**: Rimuove caratteri di controllo e XSS
- **Validazione file**: Controllo MIME type, estensioni, dimensioni
- **Logging dettagliato**: Traccia tentativi di attacco e errori

### 2. Controller.php - Classe Base Aggiornata

**Percorso**: `app/core/Controller.php`

#### Miglioramenti:

- **Token automatici**: Ogni vista riceve automaticamente `csrf_field` e `csrf_token`
- **Metodi helper**: `requireCSRF()`, `validateFileUpload()`
- **Sanificazione automatica**: `getPost()` e `getQuery()` usano `sanitizeInput()`
- **Gestione errori**: Try-catch per validazione CSRF

### 3. Controller Aggiornati

#### ProgettiController.php
- ✅ Protezione CSRF su `nuovo()` e `modifica()`
- ✅ Gestione errori migliorata con messaggi utente
- ✅ Utilizzo `requireCSRF()` invece di validazione manuale

#### ClientiController.php  
- ✅ Protezione CSRF su `nuovo()` e `modifica()`
- ✅ Rimozione chiamate manuali a `generateCsrfToken()`
- ✅ Gestione errori consistente

#### AuthController.php
- ✅ Protezione CSRF su `login()`
- ✅ Utilizzo `sanitizeInput()` per username
- ✅ Gestione errori con redirect

## 🛡️ Funzionalità di Sicurezza

### Protezione CSRF
```php
// Generazione automatica token in ogni vista
$data['csrf_field'] = Security::csrfField();
$data['csrf_token'] = Security::generateCSRFToken();

// Validazione nei controller
$this->requireCSRF(); // Lancia eccezione se non valido
```

### Sanificazione Input
```php
// Sanificazione automatica
$cleanData = Security::sanitizeInput($_POST);

// Caratteristiche:
// - Rimuove tag HTML pericolosi
// - Converte caratteri speciali in entità HTML
// - Rimuove caratteri di controllo
// - Gestisce array ricorsivamente
```

### Validazione File Upload
```php
$errors = Security::validateFileUpload($_FILES['file']);

// Controlli implementati:
// - Dimensione massima (10MB)
// - Tipi MIME consentiti
// - Estensioni file sicure
// - Corrispondenza MIME/estensione
// - Caratteri pericolosi nel nome
// - File eseguibili mascherati
```

### Generazione Nomi File Sicuri
```php
$safeName = Security::generateSafeFilename('documento importante.pdf');
// Output: documento_importante_2025-01-05_14-30-25_a1b2c3d4.pdf
```

## 📝 Come Utilizzare

### Nei Form HTML
```html
<!-- Token automaticamente disponibile in tutte le viste -->
<form method="POST">
    <?= $csrf_field ?>
    <!-- oppure -->
    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
    <!-- altri campi del form -->
</form>
```

### Nei Controller
```php
class MioController extends Controller {
    public function salva() {
        if ($this->isPost()) {
            // Validazione CSRF automatica
            try {
                $this->requireCSRF();
            } catch (\Exception $e) {
                // Gestisci errore CSRF
                $this->view('form', ['errori' => ['csrf' => 'Token non valido']]);
                return;
            }
            
            // Processa dati sanificati
            $data = $this->getPost(); // Già sanificato
            
            // Validazione file se necessario
            if (isset($_FILES['documento'])) {
                $errors = $this->validateFileUpload($_FILES['documento']);
                if (!empty($errors)) {
                    // Gestisci errori file
                }
            }
        }
    }
}
```

## 🧪 Test e Verifica

### Script di Test
È stato creato `test_csrf.php` per verificare tutte le funzionalità:

```bash
# Accedi a: http://localhost/progetti/studio_tecnico/test_csrf.php
```

### Test Inclusi:
- ✅ Generazione token CSRF
- ✅ Validazione token corretti/errati
- ✅ Generazione campo HTML
- ✅ Sanificazione input complessa
- ✅ Validazione file upload (simulata)
- ✅ Generazione nomi file sicuri
- ✅ Metodi di utilità CSRF

## 🔒 Livello di Sicurezza Raggiunto

### Prima dell'Implementazione:
- ❌ Protezione CSRF limitata
- ❌ Sanificazione input inconsistente  
- ❌ Validazione file upload assente
- ❌ Gestione errori di sicurezza basica

### Dopo l'Implementazione:
- ✅ Protezione CSRF completa su tutti i form
- ✅ Sanificazione input automatica e robusta
- ✅ Validazione file upload avanzata
- ✅ Gestione errori di sicurezza professionale
- ✅ Logging dettagliato per audit
- ✅ Type hints PHP 8+ per type safety

## 📈 Benefici Ottenuti

1. **Sicurezza**: Protezione contro CSRF, XSS, file upload pericolosi
2. **Usabilità**: Gestione errori user-friendly
3. **Manutenibilità**: Codice centralizzato e riutilizzabile
4. **Debugging**: Logging dettagliato per troubleshooting
5. **Conformità**: Standard di sicurezza moderni

## 🎯 Prossimi Passi

La protezione CSRF è ora completamente implementata. Si può procedere con:

1. **Sistema Notifiche Completo** (Priorità 3)
2. **PraticheModel.php** (Priorità 4)
3. **Test automatizzati** per validare la sicurezza
4. **Audit di sicurezza** completo dell'applicazione

---

*Implementazione completata il 5 Gennaio 2025*  
*Documentazione aggiornata da: Augment Agent*
