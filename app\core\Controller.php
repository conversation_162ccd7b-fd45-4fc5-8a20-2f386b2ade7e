<?php
// app/core/Controller.php - Classe base per tutti i controller
namespace App\Core;

class Controller {
    public function __construct() {
        // Il costruttore base può rimanere vuoto
        // ma deve esistere per essere chiamato dai controller figli
    }

    protected function view($view, $data = []) {
        try {
            // Aggiungi il token CSRF ai dati della vista
            $data['csrf_field'] = Security::csrfField();
            $data['csrf_token'] = Security::generateCSRFToken();

            // Estrae le variabili dall'array per renderle disponibili nella vista
            extract($data);

            // Carica la vista
            $viewPath = ROOT_PATH . '/views/' . $view . '.php';
            if (file_exists($viewPath)) {
                require_once $viewPath;
            } else {
                throw new \Exception("Vista non trovata: {$viewPath}");
            }
        } catch (\Exception $e) {
            error_log("Errore nel caricamento della vista: " . $e->getMessage());
            throw $e;
        }
    }

    protected function redirect($url) {
        header('Location: ' . BASE_URL . $url);
        exit;
    }

    protected function json($data) {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    protected function isPost() {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }

    protected function isGet() {
        return $_SERVER['REQUEST_METHOD'] === 'GET';
    }

    protected function getPost($key = null) {
        if ($key === null) {
            return Security::sanitizeInput($_POST);
        }
        return isset($_POST[$key]) ? Security::sanitizeInput($_POST[$key]) : null;
    }

    protected function getQuery($key = null) {
        if ($key === null) {
            return Security::sanitizeInput($_GET);
        }
        return Security::sanitizeInput($_GET[$key] ?? null);
    }

    protected function validate($data, $rules) {
        return Security::validateInput($data, $rules);
    }

    protected function error($message, $code = 500) {
        http_response_code($code);
        if ($this->isAjax()) {
            $this->json(['error' => $message]);
        } else {
            $this->view("errors/$code", ['message' => $message]);
        }
        exit;
    }

    protected function isAjax() {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
    }

    /**
     * Verifica il token CSRF dalla richiesta corrente
     */
    protected function verificaCsrf(): bool {
        try {
            return Security::validateCSRFToken($_POST['_token'] ?? '');
        } catch (\Exception $e) {
            error_log("Errore nella verifica del token CSRF: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Richiede un token CSRF valido, lancia eccezione se non valido
     */
    protected function requireCSRF(): void {
        if (!$this->verificaCsrf()) {
            error_log("Controller::requireCSRF() - Token CSRF non valido per " . get_class($this));
            throw new \Exception('Token CSRF non valido o mancante');
        }
    }

    /**
     * Valida file upload utilizzando Security::validateFileUpload()
     */
    protected function validateFileUpload(array $file): array {
        return Security::validateFileUpload($file);
    }
}
