<?php
require_once __DIR__ . '/../config/database.php';

try {
    $config = require __DIR__ . '/../config/database.php';
    $dbConfig = $config['db'];
    
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], $dbConfig['options']);

    // Verifica se la colonna esiste già
    $stmt = $pdo->query("SHOW COLUMNS FROM clienti LIKE 'attivo'");
    if ($stmt->rowCount() === 0) {
        // La colonna non esiste, la aggiungiamo
        $pdo->exec("ALTER TABLE clienti ADD COLUMN attivo TINYINT(1) NOT NULL DEFAULT 1");
        echo "Colonna 'attivo' aggiunta con successo alla tabella clienti\n";
        
        // Impostiamo tutti i clienti esistenti come attivi
        $pdo->exec("UPDATE clienti SET attivo = 1");
        echo "Tutti i clienti esistenti sono stati impostati come attivi\n";
    } else {
        echo "La colonna 'attivo' esiste già nella tabella clienti\n";
    }

} catch (PDOException $e) {
    die("Errore durante la migrazione: " . $e->getMessage());
}
