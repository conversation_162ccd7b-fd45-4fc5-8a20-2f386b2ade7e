# Analisi Database e Aggiornamenti Necessari - Studio Tecnico

## 📋 Analisi Situazione Attuale

### 1. **<PERSON><PERSON> `pratiche` - Problemi Identificati**

#### ❌ **PROBLEMA CRITICO: Stati Enum Incompleti**
**Stato Attuale:**
```sql
`stato` enum('in_attesa','in_revisione','approvata') DEFAULT 'in_attesa'
```

**Stati Richiesti dal PraticheModel:**
- `in_attesa` ✅ (presente)
- `in_revisione` ✅ (presente)  
- `approvata` ✅ (presente)
- `completata` ❌ (MANCANTE)
- `sospesa` ❌ (MANCANTE)
- `respinta` ❌ (MANCANTE)

**Impatto:** Il workflow automatizzato non può funzionare correttamente senza tutti gli stati.

#### ✅ **Campi Supportati Correttamente**
Tutti i campi richiesti dal PraticheModel sono presenti:
- `id`, `progetto_id`, `tipo_pratica`, `tipo_documento`
- `data_apertura`, `data_scadenza`, `data_scadenza_integrazione`
- `note`, `note_interne`, `numero_pratica`, `ente_riferimento`
- `protocollo`, `data_protocollo`, `importo_diritti`
- `documenti_richiesti`, `responsabile`

### 2. **Sistema Notifiche - Tabelle Mancanti**

#### ❌ **PROBLEMA CRITICO: Tabelle Notifiche Non Esistenti**
Le seguenti tabelle sono **completamente assenti** dal database:
- `notifiche` ❌ (MANCANTE)
- `notifiche_preferenze` ❌ (MANCANTE)

**Impatto:** Il sistema notifiche non può funzionare senza queste tabelle.

### 3. **Indici e Performance**

#### ⚠️ **Indici Mancanti per Performance**
Indici attuali per `pratiche`:
```sql
PRIMARY KEY (`id`)
KEY `progetto_id` (`progetto_id`)
```

**Indici Necessari per le Nuove Funzionalità:**
- Indice su `stato` per filtri workflow
- Indice su `data_scadenza` per controllo scadenze
- Indice su `data_scadenza_integrazione` per controllo scadenze
- Indice composto per query statistiche

## 🔧 Script di Aggiornamento Database

### FASE 1: Aggiornamento Tabella Pratiche

#### 1.1 Aggiornamento Enum Stati
```sql
-- Aggiorna l'enum degli stati per supportare il workflow completo
ALTER TABLE `pratiche` 
MODIFY COLUMN `stato` ENUM(
    'in_attesa',
    'in_revisione', 
    'approvata',
    'completata',
    'sospesa',
    'respinta'
) DEFAULT 'in_attesa';
```

#### 1.2 Aggiunta Indici Performance
```sql
-- Indice per filtri per stato
CREATE INDEX `idx_pratiche_stato` ON `pratiche` (`stato`);

-- Indice per controllo scadenze
CREATE INDEX `idx_pratiche_data_scadenza` ON `pratiche` (`data_scadenza`);
CREATE INDEX `idx_pratiche_data_scadenza_integrazione` ON `pratiche` (`data_scadenza_integrazione`);

-- Indice composto per query statistiche
CREATE INDEX `idx_pratiche_stato_data` ON `pratiche` (`stato`, `data_apertura`);

-- Indice per ricerche per ente
CREATE INDEX `idx_pratiche_ente` ON `pratiche` (`ente_riferimento`);

-- Indice per ricerche per responsabile
CREATE INDEX `idx_pratiche_responsabile` ON `pratiche` (`responsabile`);
```

### FASE 2: Creazione Sistema Notifiche

#### 2.1 Tabella Notifiche Principale
```sql
-- Tabella principale per le notifiche
CREATE TABLE IF NOT EXISTS `notifiche` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `tipo` enum('scadenza','pratica','progetto','sistema','fattura','cliente','documento') NOT NULL,
  `titolo` varchar(255) NOT NULL,
  `messaggio` text NOT NULL,
  `priorita` enum('bassa','media','alta') DEFAULT 'media',
  `link_azione` varchar(255) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `letta` boolean DEFAULT FALSE,
  `data_creazione` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `data_lettura` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_letta` (`user_id`, `letta`),
  KEY `idx_data_creazione` (`data_creazione`),
  KEY `idx_tipo` (`tipo`),
  KEY `idx_priorita` (`priorita`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### 2.2 Tabella Preferenze Notifiche
```sql
-- Tabella per le preferenze di notifica degli utenti
CREATE TABLE IF NOT EXISTS `notifiche_preferenze` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `tipo_notifica` varchar(50) NOT NULL,
  `email_enabled` boolean DEFAULT TRUE,
  `push_enabled` boolean DEFAULT TRUE,
  `soglia_giorni` int(11) DEFAULT 7,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_tipo` (`user_id`, `tipo_notifica`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### FASE 3: Dati Iniziali e Configurazione

#### 3.1 Preferenze Default per Utenti Esistenti
```sql
-- Inserimento preferenze di default per tutti gli utenti esistenti
INSERT IGNORE INTO `notifiche_preferenze` (`user_id`, `tipo_notifica`, `email_enabled`, `push_enabled`, `soglia_giorni`)
SELECT 
    u.id,
    tipo.tipo_notifica,
    TRUE,
    TRUE,
    CASE 
        WHEN tipo.tipo_notifica IN ('scadenza', 'pratica') THEN 7
        WHEN tipo.tipo_notifica = 'sistema' THEN 1
        ELSE 3
    END
FROM `users` u
CROSS JOIN (
    SELECT 'scadenza' as tipo_notifica
    UNION SELECT 'pratica'
    UNION SELECT 'progetto' 
    UNION SELECT 'sistema'
    UNION SELECT 'fattura'
    UNION SELECT 'cliente'
    UNION SELECT 'documento'
) tipo;
```

#### 3.2 Notifiche di Sistema Iniziali
```sql
-- Inserimento notifiche di sistema iniziali
INSERT IGNORE INTO `notifiche` (`user_id`, `tipo`, `titolo`, `messaggio`, `priorita`, `metadata`)
VALUES 
(1, 'sistema', 'Sistema aggiornato', 'Il database è stato aggiornato con successo per supportare il nuovo workflow pratiche.', 'media', '{"tipo_notifica": "sistema_aggiornato"}'),
(1, 'sistema', 'Workflow pratiche attivato', 'Il sistema di workflow automatizzato per le pratiche è ora attivo.', 'bassa', '{"tipo_notifica": "workflow_attivato"}');
```

### FASE 4: Funzionalità Avanzate

#### 4.1 Vista Statistiche
```sql
-- Vista per statistiche notifiche
CREATE OR REPLACE VIEW `v_notifiche_stats` AS
SELECT 
    u.id as user_id,
    u.username,
    COUNT(n.id) as totale_notifiche,
    SUM(CASE WHEN n.letta = FALSE THEN 1 ELSE 0 END) as non_lette,
    SUM(CASE WHEN n.letta = TRUE THEN 1 ELSE 0 END) as lette,
    SUM(CASE WHEN n.priorita = 'alta' AND n.letta = FALSE THEN 1 ELSE 0 END) as alta_priorita_non_lette,
    MAX(n.data_creazione) as ultima_notifica
FROM `users` u
LEFT JOIN `notifiche` n ON u.id = n.user_id
GROUP BY u.id, u.username;
```

#### 4.2 Stored Procedure Pulizia
```sql
-- Stored procedure per pulizia automatica notifiche vecchie
DELIMITER //
CREATE OR REPLACE PROCEDURE `sp_pulisci_notifiche_vecchie`(IN giorni_retention INT)
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    -- Elimina notifiche lette più vecchie del periodo specificato
    DELETE FROM `notifiche` 
    WHERE `letta` = TRUE 
    AND `data_creazione` < DATE_SUB(NOW(), INTERVAL giorni_retention DAY);
    
    SET deleted_count = ROW_COUNT();
    
    -- Log dell'operazione
    INSERT INTO `notifiche` (`user_id`, `tipo`, `titolo`, `messaggio`, `priorita`)
    SELECT 1, 'sistema', 'Pulizia automatica notifiche', 
           CONCAT('Eliminate ', deleted_count, ' notifiche vecchie di ', giorni_retention, ' giorni'), 'bassa'
    WHERE deleted_count > 0;
    
    SELECT deleted_count as notifiche_eliminate;
END //
DELIMITER ;
```

#### 4.3 Trigger Automatici
```sql
-- Trigger per aggiornare data_lettura quando una notifica viene marcata come letta
DELIMITER //
CREATE OR REPLACE TRIGGER `tr_notifiche_update_data_lettura`
BEFORE UPDATE ON `notifiche`
FOR EACH ROW
BEGIN
    IF NEW.letta = TRUE AND OLD.letta = FALSE THEN
        SET NEW.data_lettura = NOW();
    END IF;
END //
DELIMITER ;
```

#### 4.4 Funzione Conteggio
```sql
-- Funzione per contare notifiche non lette per utente
DELIMITER //
CREATE OR REPLACE FUNCTION `fn_count_notifiche_non_lette`(user_id_param INT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE count_result INT DEFAULT 0;
    
    SELECT COUNT(*) INTO count_result
    FROM `notifiche`
    WHERE `user_id` = user_id_param AND `letta` = FALSE;
    
    RETURN count_result;
END //
DELIMITER ;
```

## ⚠️ Compatibilità Dati Esistenti

### Verifica Pre-Aggiornamento
```sql
-- Verifica dati esistenti nella tabella pratiche
SELECT 
    stato,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT id) as pratiche_ids
FROM pratiche 
GROUP BY stato;
```

### Migrazione Sicura Stati
```sql
-- Backup dati prima della modifica
CREATE TABLE `pratiche_backup_stati` AS 
SELECT id, stato, data_apertura 
FROM pratiche;

-- Verifica che non ci siano stati non supportati
SELECT DISTINCT stato 
FROM pratiche 
WHERE stato NOT IN ('in_attesa', 'in_revisione', 'approvata');
```

## 🎯 Ordine di Esecuzione

### Sequenza Corretta:
1. **Backup Database Completo**
2. **Verifica Compatibilità Dati**
3. **FASE 1: Aggiornamento Pratiche**
4. **FASE 2: Creazione Notifiche**
5. **FASE 3: Dati Iniziali**
6. **FASE 4: Funzionalità Avanzate**
7. **Test Funzionalità**
8. **Verifica Integrità**

### Rollback Plan:
- Backup automatico prima di ogni fase
- Script di rollback per ogni modifica
- Verifica integrità dopo ogni step
