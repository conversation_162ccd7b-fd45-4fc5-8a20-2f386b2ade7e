-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- <PERSON><PERSON><PERSON> il: Dic 10, 2024 alle 23:49
-- Versione del server: 10.4.32-MariaDB
-- Versione PHP: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `studio_tecnico`
--

-- --------------------------------------------------------

--
-- <PERSON><PERSON><PERSON><PERSON> della tabella `admin_profile`
--

CR<PERSON>TE TABLE `admin_profile` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `nome` varchar(100) NOT NULL,
  `cognome` varchar(100) NOT NULL,
  `codice_fiscale` varchar(16) NOT NULL,
  `partita_iva` varchar(11) NOT NULL,
  `indirizzo` varchar(255) NOT NULL,
  `citta` varchar(100) NOT NULL,
  `cap` varchar(5) NOT NULL,
  `provincia` varchar(2) NOT NULL,
  `telefono` varchar(20) NOT NULL,
  `email` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Struttura della tabella `clienti`
--

CREATE TABLE `clienti` (
  `id` int(11) NOT NULL,
  `nome` varchar(100) NOT NULL,
  `cognome` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `telefono` varchar(20) DEFAULT NULL,
  `indirizzo` text DEFAULT NULL,
  `cap_cliente` varchar(10) DEFAULT NULL,
  `data_registrazione` timestamp NOT NULL DEFAULT current_timestamp(),
  `tipo_cliente` enum('privato','societa') NOT NULL DEFAULT 'privato',
  `ragione_sociale` varchar(200) DEFAULT NULL,
  `partita_iva` varchar(20) DEFAULT NULL,
  `codice_fiscale` varchar(16) DEFAULT NULL,
  `pec` varchar(100) DEFAULT NULL,
  `citta` varchar(100) DEFAULT NULL,
  `provincia` varchar(2) DEFAULT NULL,
  `note` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dump dei dati per la tabella `clienti`
--

INSERT INTO `clienti` (`id`, `nome`, `cognome`, `email`, `telefono`, `indirizzo`, `cap_cliente`, `data_registrazione`, `tipo_cliente`, `ragione_sociale`, `partita_iva`, `codice_fiscale`, `pec`, `citta`, `provincia`, `note`) VALUES
(1, 'Mauro', 'Mazzarelli', '<EMAIL>', '3392720049', 'via della Stazione di Ciampino 151', '00118', '2024-12-08 07:04:27', 'privato', '', '', '****************', '<EMAIL>', 'Roma', 'RM', 'sono io, ciao'),
(2, 'Roberto', 'Di Nunzio', '<EMAIL>', '3357421632', 'via Ajani 33', '00044', '2024-12-08 15:45:59', 'privato', '', '', '****************', '<EMAIL>', 'Roma', 'RM', '');

-- --------------------------------------------------------

--
-- Struttura della tabella `pratiche`
--

CREATE TABLE `pratiche` (
  `id` int(11) NOT NULL,
  `progetto_id` int(11) DEFAULT NULL,
  `tipo_pratica` varchar(100) DEFAULT NULL,
  `stato` enum('in_attesa','in_revisione','approvata') DEFAULT 'in_attesa',
  `data_apertura` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_scadenza` date DEFAULT NULL,
  `note` text DEFAULT NULL,
  `numero_pratica` varchar(50) DEFAULT NULL,
  `tipo_documento` enum('cila','scia','permesso_costruire','altro') DEFAULT 'altro',
  `ente_riferimento` varchar(100) DEFAULT NULL,
  `protocollo` varchar(50) DEFAULT NULL,
  `data_protocollo` date DEFAULT NULL,
  `data_scadenza_integrazione` date DEFAULT NULL,
  `importo_diritti` decimal(10,2) DEFAULT NULL,
  `note_interne` text DEFAULT NULL,
  `documenti_richiesti` text DEFAULT NULL,
  `responsabile` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dump dei dati per la tabella `pratiche`
--

INSERT INTO `pratiche` (`id`, `progetto_id`, `tipo_pratica`, `stato`, `data_apertura`, `data_scadenza`, `note`, `numero_pratica`, `tipo_documento`, `ente_riferimento`, `protocollo`, `data_protocollo`, `data_scadenza_integrazione`, `importo_diritti`, `note_interne`, `documenti_richiesti`, `responsabile`) VALUES
(1, 1, NULL, 'in_attesa', '2024-12-07 23:00:00', '2025-02-28', '', '1/2025', 'cila', '', '34566', '2025-01-17', NULL, NULL, '', 'doc id', 'Roberto');

-- --------------------------------------------------------

--
-- Struttura della tabella `progetti`
--

CREATE TABLE `progetti` (
  `id` int(11) NOT NULL,
  `cliente_id` int(11) DEFAULT NULL,
  `nome_progetto` varchar(200) NOT NULL,
  `descrizione` text DEFAULT NULL,
  `data_inizio` date DEFAULT NULL,
  `data_fine_prevista` date DEFAULT NULL,
  `stato` enum('in_corso','completato','sospeso') DEFAULT 'in_corso',
  `importo` decimal(10,2) DEFAULT NULL,
  `comune` varchar(100) DEFAULT NULL,
  `indirizzo_progetto` text DEFAULT NULL,
  `tipo_progetto` enum('ristrutturazione','nuova_costruzione','consulenza','altro') DEFAULT 'altro',
  `priorita` enum('bassa','media','alta') DEFAULT 'media'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dump dei dati per la tabella `progetti`
--

INSERT INTO `progetti` (`id`, `cliente_id`, `nome_progetto`, `descrizione`, `data_inizio`, `data_fine_prevista`, `stato`, `importo`, `comune`, `indirizzo_progetto`, `tipo_progetto`, `priorita`) VALUES
(1, 1, 'Casa', '', '2024-12-08', '2025-04-08', 'in_corso', 50000.00, 'Roma', 'via della Stazione di Ciampino 151', 'ristrutturazione', 'media');

-- --------------------------------------------------------

--
-- Struttura della tabella `scadenze`
--

CREATE TABLE `scadenze` (
  `id` int(11) NOT NULL,
  `progetto_id` int(11) DEFAULT NULL,
  `titolo` varchar(255) NOT NULL,
  `descrizione` text DEFAULT NULL,
  `data_scadenza` date NOT NULL,
  `stato` enum('in_corso','completata','scaduta') NOT NULL DEFAULT 'in_corso',
  `priorita` enum('bassa','media','alta') NOT NULL DEFAULT 'media',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struttura della tabella `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `nome` varchar(100) NOT NULL,
  `cognome` varchar(100) NOT NULL,
  `ruolo` enum('admin','user') NOT NULL DEFAULT 'user',
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indici per le tabelle scaricate
--

--
-- Indici per le tabelle `admin_profile`
--
ALTER TABLE `admin_profile`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`);

--
-- Indici per le tabelle `clienti`
--
ALTER TABLE `clienti`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indici per le tabelle `pratiche`
--
ALTER TABLE `pratiche`
  ADD PRIMARY KEY (`id`),
  ADD KEY `progetto_id` (`progetto_id`);

--
-- Indici per le tabelle `progetti`
--
ALTER TABLE `progetti`
  ADD PRIMARY KEY (`id`),
  ADD KEY `cliente_id` (`cliente_id`);

--
-- Indici per le tabelle `scadenze`
--
ALTER TABLE `scadenze`
  ADD PRIMARY KEY (`id`),
  ADD KEY `progetto_id` (`progetto_id`);

--
-- Indici per le tabelle `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT per le tabelle scaricate
--

--
-- AUTO_INCREMENT per la tabella `admin_profile`
--
ALTER TABLE `admin_profile`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT per la tabella `clienti`
--
ALTER TABLE `clienti`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT per la tabella `pratiche`
--
ALTER TABLE `pratiche`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT per la tabella `progetti`
--
ALTER TABLE `progetti`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT per la tabella `scadenze`
--
ALTER TABLE `scadenze`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT per la tabella `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Limiti per le tabelle scaricate
--

--
-- Limiti per la tabella `admin_profile`
--
ALTER TABLE `admin_profile`
  ADD CONSTRAINT `admin_profile_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Limiti per la tabella `pratiche`
--
ALTER TABLE `pratiche`
  ADD CONSTRAINT `pratiche_ibfk_1` FOREIGN KEY (`progetto_id`) REFERENCES `progetti` (`id`);

--
-- Limiti per la tabella `progetti`
--
ALTER TABLE `progetti`
  ADD CONSTRAINT `progetti_ibfk_1` FOREIGN KEY (`cliente_id`) REFERENCES `clienti` (`id`);

--
-- Limiti per la tabella `scadenze`
--
ALTER TABLE `scadenze`
  ADD CONSTRAINT `scadenze_ibfk_1` FOREIGN KEY (`progetto_id`) REFERENCES `progetti` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
