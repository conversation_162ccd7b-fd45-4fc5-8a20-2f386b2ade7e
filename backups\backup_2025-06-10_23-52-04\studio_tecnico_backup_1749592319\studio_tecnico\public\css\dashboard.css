:root {
    --primary-color: #2563eb;
    --secondary-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --background-color: #f8fafc;
    --card-background: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
}

/* Navbar Styles */
.navbar-custom {
    background-color: var(--card-background);
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 600;
    color: var(--primary-color) !important;
}

.nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    transition: all 0.2s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
    transform: translateY(-1px);
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--card-background);
    border: none;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    transition: all 0.3s ease;
    height: 100%;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

.dashboard-card:hover::before {
    opacity: 1;
}

.card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.card-icon::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: currentColor;
    opacity: 0.1;
    border-radius: inherit;
}

.card-icon i {
    position: relative;
    z-index: 1;
}

.card-stats {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.card-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Card Colors */
.card-primary .card-icon {
    color: var(--primary-color);
}

.card-success .card-icon {
    color: var(--success-color);
}

.card-info .card-icon {
    color: var(--info-color);
}

.card-warning .card-icon {
    color: var(--warning-color);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-card {
    animation: fadeIn 0.5s ease-out forwards;
}

.dashboard-card:nth-child(1) { animation-delay: 0.1s; }
.dashboard-card:nth-child(2) { animation-delay: 0.2s; }
.dashboard-card:nth-child(3) { animation-delay: 0.3s; }
.dashboard-card:nth-child(4) { animation-delay: 0.4s; }

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-card {
        margin-bottom: 1rem;
    }
    
    .card-stats {
        font-size: 2rem;
    }
    
    .card-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }
}
