<?php
$pageTitle = "Gestione Clienti";
include VIEWS_DIR . '/layouts/header.php';
?>

<link rel="stylesheet" href="<?= rtrim(BASE_URL, '/') ?>/assets/css/management.css">

<div class="container-fluid py-4">
    <div class="page-header">
        <h2 class="page-title">
            <i class="fas fa-users"></i>
            Gestione Clienti
        </h2>
        <div class="page-actions">
            <?php 
            $guideTitle = "Guida Rapida - Gestione Clienti";
            $guideContent = '
                <div class="guide-section mb-4">
                    <h6 class="fw-bold"><i class="fas fa-users me-2"></i>Gestione Clienti</h6>
                    <p>Questa sezione ti permette di gestire l\'anagrafica completa dei clienti:</p>
                    <ul class="list-unstyled ps-3">
                        <li><i class="fas fa-plus text-success me-2"></i><strong>Nuovo Cliente:</strong> Aggiungi un nuovo cliente (privato o azienda)</li>
                        <li><i class="fas fa-edit text-primary me-2"></i><strong>Modifica:</strong> Aggiorna i dati dei clienti esistenti</li>
                        <li><i class="fas fa-search text-info me-2"></i><strong>Ricerca:</strong> Usa i filtri per trovare rapidamente i clienti</li>
                        <li><i class="fas fa-file-alt text-warning me-2"></i><strong>Progetti:</strong> Visualizza i progetti associati ad ogni cliente</li>
                    </ul>
                </div>
                <div class="guide-section">
                    <h6 class="fw-bold"><i class="fas fa-lightbulb me-2"></i>Suggerimenti</h6>
                    <ul class="list-unstyled ps-3">
                        <li><i class="fas fa-check text-success me-2"></i>Mantieni sempre aggiornati i contatti dei clienti</li>
                        <li><i class="fas fa-check text-success me-2"></i>Verifica la correttezza dei codici fiscali/P.IVA</li>
                        <li><i class="fas fa-check text-success me-2"></i>Controlla regolarmente lo stato dei progetti associati</li>
                    </ul>
                </div>
            ';
            include VIEWS_DIR . '/components/quick_guide.php'; 
            ?>
            <a href="<?= rtrim(BASE_URL, '/') ?>/clienti/nuovo" class="btn btn-neutral">
                <i class="fas fa-plus"></i>
                Nuovo Cliente
            </a>
        </div>
    </div>

    <div class="container mt-4">
        <?php if (isset($_GET['error'])): ?>
            <?php 
            $errorMessage = '';
            switch ($_GET['error']) {
                case 'cliente_non_trovato':
                    $errorMessage = 'Cliente non trovato.';
                    break;
                case 'impossibile_eliminare_cliente_con_progetti':
                    $errorMessage = 'Impossibile eliminare il cliente perché ha dei progetti collegati.';
                    break;
                case 'errore_eliminazione':
                    $errorMessage = 'Si è verificato un errore durante l\'eliminazione del cliente.';
                    break;
                default:
                    $errorMessage = 'Si è verificato un errore.';
            }
            ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $errorMessage ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['success'])): ?>
            <?php 
            $successMessage = '';
            switch ($_GET['success']) {
                case 'cliente_eliminato':
                    $successMessage = 'Cliente eliminato con successo.';
                    break;
                default:
                    $successMessage = 'Operazione completata con successo.';
            }
            ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $successMessage ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($_SESSION['error']) ?>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <?php if (isset($_GET['msg'])): ?>
        <?php if ($_GET['msg'] === 'success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Cliente inserito con successo!
            </div>
        <?php elseif ($_GET['msg'] === 'update_success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Cliente aggiornato con successo!
            </div>
        <?php elseif ($_GET['msg'] === 'delete_success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Cliente eliminato con successo!
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error) ?>
        </div>
    <?php endif; ?>

    <?php if (empty($clienti)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            Nessun cliente trovato.
        </div>
    <?php else: ?>
        <div class="table-card">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Tipo</th>
                            <th>Nome/Ragione Sociale</th>
                            <th>Email</th>
                            <th>Telefono</th>
                            <th>Città</th>
                            <th class="text-center">Progetti</th>
                            <th class="text-center">Pratiche</th>
                            <th class="text-end">Azioni</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($clienti as $cliente): ?>
                            <tr>
                                <td>
                                    <span class="badge <?= $cliente['tipo_cliente'] === 'privato' ? 'bg-primary' : 'bg-success' ?>">
                                        <?= ucfirst($cliente['tipo_cliente']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($cliente['tipo_cliente'] === 'privato'): ?>
                                        <?= htmlspecialchars($cliente['cognome'] . ' ' . $cliente['nome']) ?>
                                    <?php else: ?>
                                        <?= htmlspecialchars($cliente['ragione_sociale']) ?>
                                    <?php endif; ?>
                                </td>
                                <td><?= htmlspecialchars($cliente['email']) ?></td>
                                <td><?= htmlspecialchars($cliente['telefono']) ?></td>
                                <td><?= htmlspecialchars($cliente['citta']) ?></td>
                                <td class="text-center">
                                    <span class="badge bg-info">
                                        <?= $cliente['num_progetti'] ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-warning">
                                        <?= $cliente['num_pratiche'] ?>
                                    </span>
                                </td>
                                <td class="text-end">
                                    <div class="btn-group">
                                        <a href="<?= rtrim(BASE_URL, '/') ?>/clienti/dettagli/<?= $cliente['id'] ?>" class="btn btn-outline-primary" title="Visualizza">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= rtrim(BASE_URL, '/') ?>/clienti/modifica/<?= $cliente['id'] ?>" class="btn btn-outline-neutral" title="Modifica">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button onclick="confirmDelete(<?= $cliente['id'] ?>)" class="btn btn-outline-danger" title="Elimina">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// Definisci BASE_URL per JavaScript
const BASE_URL = '<?= BASE_URL ?>';

function confirmDelete(id) {
    Swal.fire({
        title: 'Sei sicuro?',
        text: "Questa azione non può essere annullata!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Sì, elimina!',
        cancelButtonText: 'Annulla'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `${BASE_URL}clienti/elimina/${id}`;
        }
    });
}
</script>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>