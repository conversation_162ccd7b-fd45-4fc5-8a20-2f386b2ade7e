<?php
namespace App\Controllers;

class AdminProfileController {
    private $db;
    
    public function __construct($db) {
        $this->db = $db;
    }
    
    public function checkProfile() {
        if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
            header('Location: ' . BASE_URL . 'login');
            exit;
        }
        
        $stmt = $this->db->prepare('SELECT * FROM admin_profile WHERE user_id = ?');
        $stmt->execute([$_SESSION['user']['id']]);
        $profile = $stmt->fetch();
        
        if (!$profile) {
            header('Location: ' . BASE_URL . 'admin/profile/setup');
            exit;
        }
        
        return true;
    }
    
    public function setup() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $required_fields = ['nome', 'cognome', 'codice_fiscale', 'partita_iva', 
                              'indirizzo', 'citta', 'cap', 'provincia', 'telefono', 'email'];
            
            $errors = [];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    $errors[] = "Il campo $field è obbligatorio";
                }
            }
            
            if (!empty($errors)) {
                $_SESSION['errors'] = $errors;
                include VIEWS_DIR . '/admin/profile_setup.php';
                return;
            }
            
            try {
                $stmt = $this->db->prepare('
                    INSERT INTO admin_profile (
                        user_id, nome, cognome, codice_fiscale, partita_iva,
                        indirizzo, citta, cap, provincia, telefono, email
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ');
                
                $result = $stmt->execute([
                    $_SESSION['user']['id'],
                    $_POST['nome'],
                    $_POST['cognome'],
                    $_POST['codice_fiscale'],
                    $_POST['partita_iva'],
                    $_POST['indirizzo'],
                    $_POST['citta'],
                    $_POST['cap'],
                    $_POST['provincia'],
                    $_POST['telefono'],
                    $_POST['email']
                ]);
                
                if ($result) {
                    $_SESSION['success'] = 'Profilo amministratore completato con successo';
                    header('Location: ' . BASE_URL . 'admin');
                    exit;
                } else {
                    throw new \Exception('Errore durante il salvataggio del profilo');
                }
            } catch (\PDOException $e) {
                error_log('Errore PDO nel setup del profilo admin: ' . $e->getMessage());
                $_SESSION['error'] = 'Si è verificato un errore durante il salvataggio del profilo';
                include VIEWS_DIR . '/admin/profile_setup.php';
                return;
            } catch (\Exception $e) {
                error_log('Errore generico nel setup del profilo admin: ' . $e->getMessage());
                $_SESSION['error'] = 'Si è verificato un errore imprevisto';
                include VIEWS_DIR . '/admin/profile_setup.php';
                return;
            }
        } else {
            // GET request - mostra il form
            include VIEWS_DIR . '/admin/profile_setup.php';
            return;
        }
    }
}
