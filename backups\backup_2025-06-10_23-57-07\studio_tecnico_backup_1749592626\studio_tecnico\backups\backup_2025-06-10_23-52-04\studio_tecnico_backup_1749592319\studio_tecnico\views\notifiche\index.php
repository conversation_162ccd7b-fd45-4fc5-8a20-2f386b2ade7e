<?php require_once ROOT_PATH . '/includes/header.php'; ?>

<div class="container py-4">
    <div class="row">
        <!-- <PERSON><PERSON>na <PERSON> -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Notifiche</h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="segnaLetteTutte()">
                            <i class="fas fa-check-double"></i> Segna tutte come lette
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="archiviaTutte()">
                            <i class="fas fa-archive"></i> Archivia tutte
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <?php if (empty($notifiche)): ?>
                            <div class="text-center p-4 text-muted">
                                <i class="fas fa-bell-slash fa-2x mb-3"></i>
                                <p>Non ci sono notifiche da visualizzare</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($notifiche as $notifica): ?>
                                <div class="list-group-item list-group-item-action py-3 <?php echo !$notifica['letta'] ? 'bg-light' : ''; ?>"
                                     data-id="<?php echo $notifica['id']; ?>">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-1">
                                            <?php if ($notifica['priorita'] === 'alta'): ?>
                                                <span class="badge bg-danger me-2">Alta</span>
                                            <?php elseif ($notifica['priorita'] === 'media'): ?>
                                                <span class="badge bg-warning me-2">Media</span>
                                            <?php endif; ?>
                                            <?php echo htmlspecialchars($notifica['titolo']); ?>
                                        </h6>
                                        <small class="text-muted">
                                            <?php echo date('d/m/Y H:i', strtotime($notifica['data_creazione'])); ?>
                                        </small>
                                    </div>
                                    <p class="mb-1"><?php echo htmlspecialchars($notifica['messaggio']); ?></p>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <?php if ($notifica['link']): ?>
                                            <a href="<?php echo BASE_URL . $notifica['link']; ?>" class="btn btn-sm btn-link px-0">
                                                Visualizza
                                                <i class="fas fa-chevron-right ms-1"></i>
                                            </a>
                                        <?php else: ?>
                                            <div></div>
                                        <?php endif; ?>
                                        <div class="btn-group">
                                            <?php if (!$notifica['letta']): ?>
                                                <button type="button" class="btn btn-sm btn-outline-secondary"
                                                        onclick="segnaNotificaLetta(<?php echo $notifica['id']; ?>)">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                                    onclick="archiviaNotifica(<?php echo $notifica['id']; ?>)">
                                                <i class="fas fa-archive"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Colonna Preferenze -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Preferenze Notifiche</h5>
                </div>
                <div class="card-body">
                    <form id="formPreferenze">
                        <?php echo $csrf_field; ?>
                        
                        <?php foreach ($preferenze as $pref): ?>
                            <div class="mb-4">
                                <h6 class="mb-3"><?php echo ucfirst(str_replace('_', ' ', $pref['tipo_notifica'])); ?></h6>
                                
                                <div class="form-check mb-2">
                                    <input type="checkbox" class="form-check-input" 
                                           id="email_<?php echo $pref['tipo_notifica']; ?>"
                                           name="preferenze[<?php echo $pref['tipo_notifica']; ?>][email]"
                                           <?php echo $pref['email'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="email_<?php echo $pref['tipo_notifica']; ?>">
                                        Notifiche via email
                                    </label>
                                </div>
                                
                                <div class="form-check mb-2">
                                    <input type="checkbox" class="form-check-input"
                                           id="browser_<?php echo $pref['tipo_notifica']; ?>"
                                           name="preferenze[<?php echo $pref['tipo_notifica']; ?>][browser]"
                                           <?php echo $pref['browser'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="browser_<?php echo $pref['tipo_notifica']; ?>">
                                        Notifiche nel browser
                                    </label>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Anticipo notifica (giorni)</label>
                                    <input type="number" class="form-control form-control-sm"
                                           name="preferenze[<?php echo $pref['tipo_notifica']; ?>][anticipo_giorni]"
                                           value="<?php echo $pref['anticipo_giorni']; ?>"
                                           min="1" max="30">
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <button type="submit" class="btn btn-primary">
                            Salva Preferenze
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function segnaNotificaLetta(id) {
    const formData = new FormData();
    formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);
    
    fetch(`<?php echo BASE_URL; ?>notifiche/segnaLetta/${id}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notifica = document.querySelector(`[data-id="${id}"]`);
            notifica.classList.remove('bg-light');
            notifica.querySelector('.btn-outline-secondary').remove();
        }
    })
    .catch(error => showError('Errore nell\'aggiornamento della notifica'));
}

function archiviaNotifica(id) {
    if (!confirm('Sei sicuro di voler archiviare questa notifica?')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);
    
    fetch(`<?php echo BASE_URL; ?>notifiche/archivia/${id}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notifica = document.querySelector(`[data-id="${id}"]`);
            notifica.remove();
            
            if (document.querySelectorAll('.list-group-item').length === 0) {
                location.reload();
            }
        }
    })
    .catch(error => showError('Errore nell\'archiviazione della notifica'));
}

function segnaLetteTutte() {
    const notifiche = document.querySelectorAll('.list-group-item.bg-light');
    notifiche.forEach(notifica => {
        segnaNotificaLetta(notifica.dataset.id);
    });
}

function archiviaTutte() {
    if (!confirm('Sei sicuro di voler archiviare tutte le notifiche?')) {
        return;
    }
    
    const notifiche = document.querySelectorAll('.list-group-item');
    notifiche.forEach(notifica => {
        archiviaNotifica(notifica.dataset.id);
    });
}

document.getElementById('formPreferenze').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('<?php echo BASE_URL; ?>notifiche/salvaPreferenze', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Preferenze salvate con successo');
        } else {
            showError(data.error || 'Errore nel salvataggio delle preferenze');
        }
    })
    .catch(error => showError('Errore di connessione'));
});

function showSuccess(message) {
    Swal.fire({
        icon: 'success',
        title: 'Successo',
        text: message,
        timer: 2000,
        showConfirmButton: false
    });
}

function showError(message) {
    Swal.fire({
        icon: 'error',
        title: 'Errore',
        text: message
    });
}
</script>

<?php require_once ROOT_PATH . '/includes/footer.php'; ?>
