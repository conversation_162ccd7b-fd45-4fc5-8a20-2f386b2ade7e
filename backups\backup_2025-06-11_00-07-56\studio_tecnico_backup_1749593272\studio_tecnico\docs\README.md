# WebApp Studio Tecnico

## Panoramica del Progetto

**WebApp Studio Tecnico** è un'applicazione gestionale progettata per studi di architettura e ingegneria. L'obiettivo principale è centralizzare e ottimizzare la gestione di clienti, progetti, pratiche e scadenze, migliorando l'efficienza operativa dello studio e riducendo errori e tempi morti.

Questa applicazione web fornisce una piattaforma intuitiva per tracciare tutte le attività cruciali, dalla prima anagrafica del cliente fino alla conclusione e archiviazione delle pratiche edilizie, catastali o di altro tipo.

## Funzionalità Principali

*   **Gestione Clienti**:
    *   Anagrafica completa di clienti privati e aziende.
    *   Storico dei progetti e delle pratiche associate a ciascun cliente.
    *   Ricerca e filtraggio avanzato.

*   **Gestione Progetti**:
    *   Creazione e monitoraggio dello stato di avanzamento dei progetti.
    *   Associazione di clienti, pratiche e scadenze ai progetti.
    *   Dettaglio dei costi, delle tempistiche e delle risorse impiegate.

*   **Gestione Pratiche**:
    *   Organizzazione di tutte le pratiche (edilizie, catastali, comunali, ecc.).
    *   Collegamento diretto ai progetti e ai clienti.
    *   Tracciamento dello stato, delle date di scadenza e dei documenti allegati.

*   **Gestione Allegati**:
    *   Upload e download sicuro di documenti relativi a pratiche e progetti.
    *   Categorizzazione e ricerca degli allegati.

*   **Gestione Scadenze e Notifiche**:
    *   Sistema di monitoraggio delle scadenze per pratiche e pagamenti.
    *   Notifiche automatiche per avvisare gli utenti delle scadenze imminenti.

*   **Dashboard Amministrativa**:
    *   Panoramica generale dello stato dell'attività dello studio.
    *   Statistiche su clienti, progetti attivi e pratiche in corso.
    *   Gestione utenti e log di sistema.

*   **Configurazione Flessibile**:
    *   Personalizzazione delle impostazioni dell'applicazione (nome, logo, fuso orario, ecc.).
    *   Impostazioni per la gestione delle email.

## Stack Tecnologico (Attuale)

*   **Backend**: PHP (struttura custom MVC-like)
*   **Frontend**: HTML, CSS, JavaScript (con l'ausilio di librerie come jQuery, Bootstrap, DataTables, SweetAlert2, Chart.js)
*   **Database**: MySQL / MariaDB
*   **Server Web**: Apache (tipicamente in ambiente XAMPP/LAMPP)

## Obiettivi di Ammodernamento (Vedi `docs/aggiornamento.md`)

Il progetto è attualmente in una fase di analisi per un piano di ammodernamento che include:
*   Adozione completa dei modelli (Models) e potenziale introduzione di un Service Layer.
*   Standardizzazione dell'ereditarietà dei Controller.
*   Introduzione di Dependency Injection.
*   Aderenza rigorosa a PSR-12 e utilizzo di Type Hinting.
*   Rafforzamento della sicurezza (CSRF, validazione input, gestione sessioni).
*   Introduzione di un sistema di migrazioni per il database.
*   Implementazione di test unitari e di integrazione.

## Come Iniziare (Setup Ambiente Locale)

1.  **Prerequisiti**:
    *   XAMPP (o un ambiente LAMP/WAMP/MAMP equivalente) con Apache, MySQL e PHP.
    *   Git (opzionale, per il versionamento).
2.  **Clonare il Repository** (se applicabile) o copiare i file del progetto nella directory `htdocs` (o equivalente) del server web.
    ```bash
    # Esempio se si usa Git
    # git clone [URL_DEL_REPOSITORY] nome_cartella_progetto
    # cd nome_cartella_progetto
    ```
3.  **Configurazione Database**:
    *   Creare un database MySQL (es. `studio_tecnico`).
    *   Importare lo schema del database (se fornito un file `.sql`) o assicurarsi che le tabelle necessarie esistano.
    *   Aggiornare le credenziali del database nel file `config/config.php`:
        ```php
        define('DB_HOST', 'localhost');
        define('DB_NAME', 'nome_vostro_database');
        define('DB_USER', 'vostro_utente_db');
        define('DB_PASS', 'vostra_password_db');
        ```
4.  **Configurazione URL Base**:
    *   Modificare il file `config/config.php` per impostare correttamente `BASE_URL`. Se il progetto si trova in una sottocartella (es. `http://localhost/progetti/studio_tecnico/`), `$projectFolder` deve essere impostato di conseguenza:
        ```php
        $projectFolder = 'progetti/studio_tecnico'; // Modificare se necessario
        // ...
        define('BASE_URL', $protocol . $host . '/' . $projectFolder . '/');
        ```
    *   Assicurarsi che il file `.htaccess` nella root del progetto sia configurato correttamente, in particolare la direttiva `RewriteBase`:
        ```apacheconf
        RewriteBase /progetti/studio_tecnico/ # Modificare se necessario
        ```
5.  **Avviare Apache e MySQL** dal pannello di controllo di XAMPP.
6.  **Accedere all'Applicazione**: Aprire il browser e navigare a `BASE_URL` (es. `http://localhost/progetti/studio_tecnico/`).

## Contribuire

Informazioni su come contribuire al progetto (se applicabile).

---
*Questo README è stato generato e sarà mantenuto da Cascade, assistente AI, in collaborazione con l'utente.*
