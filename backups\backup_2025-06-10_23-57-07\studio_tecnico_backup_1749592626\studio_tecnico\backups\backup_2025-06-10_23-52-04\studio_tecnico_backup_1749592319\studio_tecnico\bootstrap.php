<?php
// Caricamento delle configurazioni di base
require_once __DIR__ . '/config/config.php';

// Definizione delle costanti aggiuntive
define('APP_PATH', ROOT_PATH . '/app');

// Autoload delle classi
spl_autoload_register(function ($class) {
    $class = str_replace('\\', DIRECTORY_SEPARATOR, $class);
    $file = ROOT_PATH . DIRECTORY_SEPARATOR . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Impostazione del reporting degli errori
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Funzione per il debug
function dd($var) {
    echo '<pre>';
    var_dump($var);
    echo '</pre>';
    die();
}
