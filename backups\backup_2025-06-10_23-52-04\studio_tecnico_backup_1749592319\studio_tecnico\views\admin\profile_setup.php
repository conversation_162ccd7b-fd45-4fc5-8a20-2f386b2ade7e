<?php include VIEWS_DIR . '/layouts/header.php'; ?>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Completa il tuo profilo amministratore</h4>
                </div>
                <div class="card-body">
                    <?php if (isset($errors) && !empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?= htmlspecialchars($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="<?= BASE_URL ?>admin/profile/setup">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="nome" class="form-label">Nome</label>
                                <input type="text" class="form-control" id="nome" name="nome" required>
                            </div>
                            <div class="col-md-6">
                                <label for="cognome" class="form-label">Cognome</label>
                                <input type="text" class="form-control" id="cognome" name="cognome" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="codice_fiscale" class="form-label">Codice Fiscale</label>
                                <input type="text" class="form-control" id="codice_fiscale" name="codice_fiscale" 
                                       maxlength="16" required>
                            </div>
                            <div class="col-md-6">
                                <label for="partita_iva" class="form-label">Partita IVA</label>
                                <input type="text" class="form-control" id="partita_iva" name="partita_iva" 
                                       maxlength="11" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="indirizzo" class="form-label">Indirizzo</label>
                            <input type="text" class="form-control" id="indirizzo" name="indirizzo" required>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="citta" class="form-label">Città</label>
                                <input type="text" class="form-control" id="citta" name="citta" required>
                            </div>
                            <div class="col-md-3">
                                <label for="cap" class="form-label">CAP</label>
                                <input type="text" class="form-control" id="cap" name="cap" 
                                       maxlength="5" required>
                            </div>
                            <div class="col-md-3">
                                <label for="provincia" class="form-label">Provincia</label>
                                <input type="text" class="form-control" id="provincia" name="provincia" 
                                       maxlength="2" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="telefono" class="form-label">Telefono</label>
                                <input type="tel" class="form-control" id="telefono" name="telefono" required>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                Completa Profilo
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>
