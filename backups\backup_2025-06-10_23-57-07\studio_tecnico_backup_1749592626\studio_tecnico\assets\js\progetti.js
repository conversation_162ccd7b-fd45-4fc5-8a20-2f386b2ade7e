const ProgettiManager = {
    init() {
        this.bindEvents();
        this.initFormValidation();
        this.initDateRangeValidation();
    },

    bindEvents() {
        // Gestione eliminazione progetto
        $('.delete-progetto').on('click', this.handleDelete.bind(this));
        
        // Aggiornamento stato progetto
        $('.update-stato').on('change', this.handleStatoUpdate.bind(this));
        
        // Calcolo automatico date
        $('#data_inizio').on('change', this.updateDataFinePrevista.bind(this));
        
        // Gestione importo
        $('#importo').on('input', this.formatImporto.bind(this));
    },

    initFormValidation() {
        const form = document.querySelector('#progettoForm');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    },

    initDateRangeValidation() {
        const dataInizio = document.getElementById('data_inizio');
        const dataFine = document.getElementById('data_fine_prevista');

        if (dataInizio && dataFine) {
            dataInizio.addEventListener('change', () => {
                dataFine.min = dataInizio.value;
            });
            dataFine.addEventListener('change', () => {
                dataInizio.max = dataFine.value;
            });
        }
    },

    async handleDelete(e) {
        e.preventDefault();
        const id = $(e.currentTarget).data('id');
        
        try {
            const result = await StudioApp.confirm({
                text: 'Verranno eliminate anche tutte le pratiche associate a questo progetto.',
                confirmText: 'Sì, elimina',
                icon: 'warning'
            });

            if (result.isConfirmed) {
                const response = await $.ajax({
                    url: `/api/progetti/elimina.php`,
                    method: 'POST',
                    data: { id }
                });

                if (response.success) {
                    StudioApp.notify('Progetto eliminato con successo');
                    window.location.reload();
                }
            }
        } catch (error) {
            StudioApp.handleError(error);
        }
    },

    async handleStatoUpdate(e) {
        const id = $(e.currentTarget).data('id');
        const stato = $(e.currentTarget).val();
        
        try {
            const response = await $.ajax({
                url: `/api/progetti/aggiorna-stato.php`,
                method: 'POST',
                data: { id, stato }
            });

            if (response.success) {
                StudioApp.notify('Stato aggiornato con successo');
                this.updateProgettoCard(id, response.data);
            }
        } catch (error) {
            StudioApp.handleError(error);
        }
    },

    updateDataFinePrevista(e) {
        const dataInizio = new Date(e.target.value);
        const tipoProg = $('#tipo_progetto').val();
        
        // Aggiungi giorni in base al tipo di progetto
        let giorniDaAggiungere = 30; // default
        switch(tipoProg) {
            case 'ristrutturazione':
                giorniDaAggiungere = 60;
                break;
            case 'nuova_costruzione':
                giorniDaAggiungere = 90;
                break;
            case 'consulenza':
                giorniDaAggiungere = 15;
                break;
        }
        
        const dataFine = new Date(dataInizio);
        dataFine.setDate(dataFine.getDate() + giorniDaAggiungere);
        
        $('#data_fine_prevista').val(dataFine.toISOString().split('T')[0]);
    },

    formatImporto(e) {
        let value = e.target.value.replace(/[^\d]/g, '');
        if (value.length > 2) {
            value = value.slice(0, -2) + '.' + value.slice(-2);
        }
        e.target.value = value;
    },

    updateProgettoCard(id, data) {
        const card = $(`#progetto-${id}`);
        if (!card.length) return;

        // Aggiorna lo stato
        card.find('.stato-badge')
            .removeClass('bg-primary bg-success bg-warning')
            .addClass(`bg-${this.getStatoBadgeClass(data.stato)}`)
            .text(this.formatStato(data.stato));

        // Aggiorna la priorità
        card.find('.priorita-badge')
            .removeClass('bg-danger bg-warning bg-info')
            .addClass(`bg-${this.getPrioritaBadgeClass(data.priorita)}`)
            .text(data.priorita);
    },

    getStatoBadgeClass(stato) {
        switch(stato) {
            case 'in_corso': return 'primary';
            case 'completato': return 'success';
            case 'sospeso': return 'warning';
            default: return 'secondary';
        }
    },

    getPrioritaBadgeClass(priorita) {
        switch(priorita) {
            case 'alta': return 'danger';
            case 'media': return 'warning';
            case 'bassa': return 'info';
            default: return 'secondary';
        }
    },

    formatStato(stato) {
        return stato.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
};

$(document).ready(() => {
    ProgettiManager.init();
}); 