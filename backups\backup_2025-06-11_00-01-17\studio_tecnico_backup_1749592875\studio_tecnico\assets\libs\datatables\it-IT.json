{"emptyTable": "<PERSON><PERSON><PERSON> dato disponibile nella tabella", "info": "Visualizzazione da _START_ a _END_ di _TOTAL_ elementi", "infoEmpty": "Visualizzazione da 0 a 0 di 0 elementi", "infoFiltered": "(filtrati da _MAX_ elementi totali)", "infoThousands": ".", "lengthMenu": "Visualizza _MENU_ elementi", "loadingRecords": "Caricamento...", "processing": "Elaborazione...", "search": "Cerca:", "zeroRecords": "La ricerca non ha portato alcun risultato", "thousands": ".", "paginate": {"first": "<PERSON><PERSON><PERSON>", "previous": "Precedente", "next": "Successivo", "last": "Fine"}, "aria": {"sortAscending": ": attiva per ordinare la colonna in ordine crescente", "sortDescending": ": attiva per ordinare la colonna in ordine decrescente"}, "autoFill": {"cancel": "<PERSON><PERSON><PERSON>", "fill": "<PERSON><PERSON><PERSON><PERSON> tutte le celle con <i>%d</i>", "fillHorizontal": "Riempi celle orizzontalmente", "fillVertical": "Riempi celle verticalmente"}, "buttons": {"collection": "Collezione <span class=\"ui-button-icon-primary ui-icon ui-icon-triangle-1-s\"></span>", "colvis": "Visibilità Colonna", "colvisRestore": "Ripristina visibil<PERSON>", "copy": "Copia", "copyKeys": "Premi ctrl o u2318 + C per copiare i dati della tabella nella tua clipboard di sistema.<br><br>Per annullare, clicca questo messaggio o premi ESC.", "copySuccess": {"1": "Copiata 1 riga nella clipboard", "_": "Copiate %d righe nella clipboard"}, "copyTitle": "Copia nella Clipboard", "csv": "CSV", "excel": "Excel", "pageLength": {"-1": "<PERSON>ra tutte le righe", "_": "Mostra %d righe"}, "pdf": "PDF", "print": "Stampa", "createState": "<PERSON><PERSON>", "removeAllStates": "<PERSON><PERSON><PERSON><PERSON> gli <PERSON>", "removeState": "<PERSON><PERSON><PERSON><PERSON>", "renameState": "Rinomina", "savedStates": "Stati Salvati", "stateRestore": "Stato %d", "updateState": "Aggiorna"}, "searchBuilder": {"add": "Aggiungi Condizione", "button": {"0": "Generatore di Ricerca", "_": "Generatore di Ricerca (%d)"}, "clearAll": "<PERSON><PERSON><PERSON><PERSON>", "condition": "Condizione", "conditions": {"date": {"after": "<PERSON><PERSON>", "before": "Prima", "between": "Tra", "empty": "<PERSON><PERSON><PERSON>", "equals": "Uguale A", "not": "Non", "notBetween": "Non Tra", "notEmpty": "Non Vuoto"}, "number": {"between": "Tra", "empty": "<PERSON><PERSON><PERSON>", "equals": "Uguale A", "gt": "Maggiore Di", "gte": "Maggiore O Uguale A", "lt": "<PERSON><PERSON>", "lte": "Minore O Uguale A", "not": "Non", "notBetween": "Non Tra", "notEmpty": "Non Vuoto"}, "string": {"contains": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>", "endsWith": "Finisce Con", "equals": "Uguale A", "not": "Non", "notEmpty": "Non Vuoto", "startsWith": "Inizia Con", "notContains": "Non Contiene", "notStartsWith": "Non Inizia Con", "notEndsWith": "Non Finisce Con"}}, "data": "<PERSON><PERSON>", "deleteTitle": "Elimina regola filtro", "leftTitle": "Criterio di Riduzione Rientro", "logicAnd": "E", "logicOr": "O", "rightTitle": "Criterio di Aumento Rientro", "title": {"0": "Generatore di Ricerca", "_": "Generatore di Ricerca (%d)"}, "value": "Valore"}, "searchPanes": {"clearMessage": "<PERSON><PERSON><PERSON><PERSON>", "collapse": {"0": "Pannello di Ricerca", "_": "Pannello di Ricerca (%d)"}, "count": "{total}", "countFiltered": "{shown} ({total})", "emptyPanes": "<PERSON><PERSON><PERSON> Ricerca", "loadMessage": "Caricamento Pannello di Ricerca", "title": "<PERSON><PERSON><PERSON> Attivi - %d", "showMessage": "<PERSON><PERSON>", "collapseMessage": "<PERSON><PERSON><PERSON>"}, "select": {"cells": {"1": "1 cella selezionata", "_": "%d celle selezionate"}, "columns": {"1": "1 colonna selezionata", "_": "%d colonne selezionate"}, "rows": {"1": "1 riga selezionata", "_": "%d righe selezionate"}}, "datetime": {"previous": "Precedente", "next": "Successivo", "hours": "Ore", "minutes": "Minuti", "seconds": "Secondi", "unknown": "-", "amPm": ["am", "pm"], "months": {"0": "Gennaio", "1": "<PERSON><PERSON><PERSON>", "10": "Novembre", "11": "Dicembre", "2": "<PERSON><PERSON>", "3": "<PERSON>e", "4": "Maggio", "5": "<PERSON><PERSON><PERSON>", "6": "<PERSON><PERSON><PERSON>", "7": "Agosto", "8": "Settembre", "9": "Ottobre"}, "weekdays": ["Dom", "<PERSON>n", "Mar", "<PERSON><PERSON>", "Gio", "Ven", "<PERSON>b"]}, "editor": {"close": "<PERSON><PERSON>", "create": {"button": "Nuovo", "title": "Crea nuovo elemento", "submit": "<PERSON><PERSON>"}, "edit": {"button": "Modifica", "title": "Modifica elemento", "submit": "Aggiorna"}, "remove": {"button": "Elimina", "title": "Elimina", "submit": "Elimina", "confirm": {"_": "Sei sicuro di voler eliminare %d righe?", "1": "Sei sicuro di voler eliminare 1 riga?"}}, "error": {"system": "Si è verificato un errore di sistema"}, "multi": {"title": "Valori Multipli", "restore": "<PERSON><PERSON><PERSON><PERSON>", "noMulti": "Questo input può essere modificato individualmente, ma non come parte di un gruppo"}}}