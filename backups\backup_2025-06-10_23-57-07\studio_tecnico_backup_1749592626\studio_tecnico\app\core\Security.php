<?php
// app/core/Security.php - Classe per la gestione della sicurezza dell'applicazione
namespace App\Core;

class Security {

    /**
     * Genera un token CSRF sicuro e lo memorizza in sessione
     * Metodo principale per la generazione di token CSRF
     */
    public static function generateCSRFToken(): string {
        try {
            if (!isset($_SESSION['csrf_token']) || empty($_SESSION['csrf_token'])) {
                $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
                error_log("Security::generateCSRFToken() - Nuovo token CSRF generato");
            }
            return $_SESSION['csrf_token'];
        } catch (\Exception $e) {
            error_log("Errore in Security::generateCSRFToken(): " . $e->getMessage());
            // Fallback con token meno sicuro ma funzionante
            $_SESSION['csrf_token'] = hash('sha256', uniqid(mt_rand(), true));
            return $_SESSION['csrf_token'];
        }
    }

    /**
     * Valida un token CSRF confrontandolo con quello in sessione
     */
    public static function validateCSRFToken(string $token): bool {
        try {
            if (empty($token)) {
                error_log("Security::validateCSRFToken() - Token vuoto fornito");
                return false;
            }

            if (!isset($_SESSION['csrf_token']) || empty($_SESSION['csrf_token'])) {
                error_log("Security::validateCSRFToken() - Nessun token in sessione");
                return false;
            }

            $isValid = hash_equals($_SESSION['csrf_token'], $token);

            if (!$isValid) {
                error_log("Security::validateCSRFToken() - Token non valido. Atteso: " .
                         substr($_SESSION['csrf_token'], 0, 8) . "..., Ricevuto: " .
                         substr($token, 0, 8) . "...");
            }

            return $isValid;
        } catch (\Exception $e) {
            error_log("Errore in Security::validateCSRFToken(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Genera un campo hidden HTML con il token CSRF
     */
    public static function csrfField(): string {
        $token = self::generateCSRFToken();
        return '<input type="hidden" name="_token" value="' . htmlspecialchars($token, ENT_QUOTES, 'UTF-8') . '">';
    }



    /**
     * Sanifica input utente per prevenire XSS e altri attacchi
     */
    public static function sanitizeInput(mixed $data): mixed {
        try {
            if (is_array($data)) {
                $sanitized = [];
                foreach ($data as $key => $value) {
                    // Sanifica anche le chiavi dell'array
                    $cleanKey = self::sanitizeInput($key);
                    $sanitized[$cleanKey] = self::sanitizeInput($value);
                }
                return $sanitized;
            } elseif (is_string($data)) {
                // Rimuove spazi iniziali e finali
                $data = trim($data);

                // Converte caratteri speciali in entità HTML
                $data = htmlspecialchars($data, ENT_QUOTES | ENT_HTML5, 'UTF-8');

                // Rimuove caratteri di controllo potenzialmente pericolosi
                $data = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $data);

                return $data;
            } elseif (is_numeric($data)) {
                // Per i numeri, verifica che siano validi
                return filter_var($data, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            } else {
                // Per altri tipi di dati, restituisce così com'è
                return $data;
            }
        } catch (\Exception $e) {
            error_log("Errore in Security::sanitizeInput(): " . $e->getMessage());
            return '';
        }
    }

    /**
     * Metodo legacy per compatibilità
     * @deprecated Usa sanitizeInput() invece
     */
    public static function sanitize(mixed $data): mixed {
        return self::sanitizeInput($data);
    }

    /**
     * Valida file upload per sicurezza
     */
    public static function validateFileUpload(array $file): array {
        $errors = [];

        try {
            // Verifica che il file sia stato caricato correttamente
            if (!isset($file['error']) || $file['error'] !== UPLOAD_ERR_OK) {
                $uploadErrors = [
                    UPLOAD_ERR_INI_SIZE => 'Il file supera la dimensione massima consentita dal server',
                    UPLOAD_ERR_FORM_SIZE => 'Il file supera la dimensione massima consentita dal form',
                    UPLOAD_ERR_PARTIAL => 'Il file è stato caricato solo parzialmente',
                    UPLOAD_ERR_NO_FILE => 'Nessun file è stato caricato',
                    UPLOAD_ERR_NO_TMP_DIR => 'Cartella temporanea mancante',
                    UPLOAD_ERR_CANT_WRITE => 'Impossibile scrivere il file su disco',
                    UPLOAD_ERR_EXTENSION => 'Upload bloccato da estensione PHP'
                ];

                $errorCode = $file['error'] ?? UPLOAD_ERR_NO_FILE;
                $errors[] = $uploadErrors[$errorCode] ?? 'Errore sconosciuto durante l\'upload';
                return $errors;
            }

            // Controllo dimensione file (max 10MB)
            $maxSize = 10 * 1024 * 1024; // 10MB in bytes
            if ($file['size'] > $maxSize) {
                $errors[] = 'File troppo grande (massimo 10MB consentiti)';
            }

            // Controllo tipo MIME
            $allowedMimeTypes = [
                'application/pdf',
                'image/jpeg',
                'image/jpg',
                'image/png',
                'image/gif',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'text/plain',
                'application/zip'
            ];

            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $file['tmp_name']);
            finfo_close($finfo);

            if (!in_array($mimeType, $allowedMimeTypes)) {
                $errors[] = 'Tipo di file non consentito. Tipi permessi: PDF, immagini, documenti Office, file di testo, ZIP';
            }

            // Controllo estensione file
            $allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'zip'];
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

            if (!in_array($extension, $allowedExtensions)) {
                $errors[] = 'Estensione file non consentita';
            }

            // Verifica che l'estensione corrisponda al tipo MIME
            $mimeToExtension = [
                'application/pdf' => ['pdf'],
                'image/jpeg' => ['jpg', 'jpeg'],
                'image/png' => ['png'],
                'image/gif' => ['gif'],
                'application/msword' => ['doc'],
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => ['docx'],
                'application/vnd.ms-excel' => ['xls'],
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => ['xlsx'],
                'text/plain' => ['txt'],
                'application/zip' => ['zip']
            ];

            if (isset($mimeToExtension[$mimeType]) && !in_array($extension, $mimeToExtension[$mimeType])) {
                $errors[] = 'L\'estensione del file non corrisponde al suo contenuto';
            }

            // Controllo nome file per caratteri pericolosi
            $filename = $file['name'];
            if (preg_match('/[<>:"|?*]/', $filename)) {
                $errors[] = 'Il nome del file contiene caratteri non consentiti';
            }

            // Controllo lunghezza nome file
            if (strlen($filename) > 255) {
                $errors[] = 'Il nome del file è troppo lungo (massimo 255 caratteri)';
            }

            // Verifica che non sia un file eseguibile mascherato
            $dangerousExtensions = ['exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'php', 'asp', 'aspx'];
            if (in_array($extension, $dangerousExtensions)) {
                $errors[] = 'Tipo di file potenzialmente pericoloso non consentito';
            }

        } catch (\Exception $e) {
            error_log("Errore in Security::validateFileUpload(): " . $e->getMessage());
            $errors[] = 'Errore durante la validazione del file';
        }

        return $errors;
    }

    /**
     * Valida input con regole specifiche
     */
    public static function validateInput(array $input, array $rules): array {
        $errors = [];

        try {
            foreach ($rules as $field => $rule) {
                $value = $input[$field] ?? '';

                if (strpos($rule, 'required') !== false && empty($value)) {
                    $errors[$field] = "Il campo è obbligatorio";
                    continue;
                }

                if (!empty($value)) {
                    if (strpos($rule, 'email') !== false && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[$field] = "Email non valida";
                    }

                    if (strpos($rule, 'numeric') !== false && !is_numeric($value)) {
                        $errors[$field] = "Il campo deve essere numerico";
                    }

                    if (preg_match('/min:(\d+)/', $rule, $matches)) {
                        $min = (int)$matches[1];
                        if (strlen($value) < $min) {
                            $errors[$field] = "Il campo deve essere lungo almeno $min caratteri";
                        }
                    }

                    if (preg_match('/max:(\d+)/', $rule, $matches)) {
                        $max = (int)$matches[1];
                        if (strlen($value) > $max) {
                            $errors[$field] = "Il campo non può superare $max caratteri";
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            error_log("Errore in Security::validateInput(): " . $e->getMessage());
            $errors['general'] = 'Errore durante la validazione dei dati';
        }

        return $errors;
    }

    /**
     * Genera un nome file sicuro per l'upload
     */
    public static function generateSafeFilename(string $originalName): string {
        try {
            $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
            $basename = pathinfo($originalName, PATHINFO_FILENAME);

            // Rimuove caratteri speciali e spazi
            $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
            $basename = preg_replace('/_+/', '_', $basename); // Rimuove underscore multipli
            $basename = trim($basename, '_');

            // Se il nome è vuoto, usa un nome generico
            if (empty($basename)) {
                $basename = 'file_' . date('Y-m-d_H-i-s');
            }

            // Aggiunge timestamp per unicità
            $timestamp = date('Y-m-d_H-i-s');
            $randomString = bin2hex(random_bytes(4));

            return $basename . '_' . $timestamp . '_' . $randomString . '.' . $extension;
        } catch (\Exception $e) {
            error_log("Errore in Security::generateSafeFilename(): " . $e->getMessage());
            return 'file_' . date('Y-m-d_H-i-s') . '_' . bin2hex(random_bytes(4)) . '.txt';
        }
    }

    /**
     * Metodi di utilità per CSRF
     */

    /**
     * Verifica se la richiesta corrente ha un token CSRF valido
     */
    public static function verifyCSRFToken(): bool {
        $token = $_POST['_token'] ?? $_GET['_token'] ?? '';
        return self::validateCSRFToken($token);
    }

    /**
     * Lancia un'eccezione se il token CSRF non è valido
     */
    public static function requireValidCSRF(): void {
        if (!self::verifyCSRFToken()) {
            error_log("Security::requireValidCSRF() - Token CSRF non valido o mancante");
            throw new \Exception('Token CSRF non valido o mancante');
        }
    }
}
