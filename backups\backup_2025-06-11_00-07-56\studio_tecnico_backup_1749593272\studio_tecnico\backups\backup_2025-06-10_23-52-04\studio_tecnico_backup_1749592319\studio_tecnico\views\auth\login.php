<?php 
// Non includere il navbar nella pagina di login
$hideNavbar = true;
$bodyClass = 'login-page';
include VIEWS_DIR . '/layouts/header.php'; 
?>

<link href="<?= BASE_URL ?>assets/css/login.css" rel="stylesheet">

<div class="container">
    <div class="login-container">
        <div class="app-info">
            <?php
            $config = require_once ROOT_PATH . '/config/app.php';
            if (!empty($config['logo_path']) && file_exists(ROOT_PATH . '/public/' . $config['logo_path'])) {
                echo '<img src="' . htmlspecialchars(BASE_URL . $config['logo_path']) . '" alt="Logo" class="app-logo">';
            } else {
                require_once VIEWS_DIR . '/components/default-logo.php';
                echo str_replace('40', '100', getDefaultLogo(100, 100));
            }
            ?>
            <h1 class="h3 mb-2">Gestionale Studio Tecnico</h1>
            <p class="text-muted mb-2">Sistema integrato di gestione professionale</p>
            <span class="version-badge">v1.0.0</span>
        </div>
        
        <div class="card">
            <div class="card-header text-center">
                <h2 class="h4 mb-0">Accesso</h2>
            </div>
            <div class="card-body p-4">
                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger d-flex align-items-center" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <div>
                            <?php 
                            echo $_SESSION['error'];
                            unset($_SESSION['error']);
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="<?= BASE_URL ?>login">
                    <?= $csrf_field ?>
                    <div class="mb-3">
                        <label for="username" class="form-label text-muted">Username</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user fa-sm"></i></span>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="password" class="form-label text-muted">Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock fa-sm"></i></span>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <span class="input-group-text toggle-password" id="togglePassword" style="cursor: pointer;">
                                <i class="fas fa-eye fa-sm"></i>
                            </span>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-neutral">
                            <i class="fas fa-sign-in-alt fa-sm"></i>
                            <span>Accedi</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('togglePassword').addEventListener('click', function (e) {
    const password = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        password.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});
</script>

<style>
.toggle-password {
    background-color: #fff;
    border-left: none;
    transition: all 0.2s ease-in-out;
}

.toggle-password:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

.input-group-text {
    color: #6c757d;
    background-color: #fff;
    border: 1px solid #ced4da;
}

.input-group-text i {
    width: 16px;
    text-align: center;
}

.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
</style>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>
