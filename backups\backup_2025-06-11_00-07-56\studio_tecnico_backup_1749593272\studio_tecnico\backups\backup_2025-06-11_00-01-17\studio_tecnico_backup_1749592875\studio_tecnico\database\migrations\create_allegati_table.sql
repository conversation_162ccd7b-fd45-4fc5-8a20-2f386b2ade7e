CREATE TABLE IF NOT EXISTS allegati (
    id INT PRIMARY KEY AUTO_INCREMENT,
    pratica_id INT NOT NULL,
    nome_file VARCHAR(255) NOT NULL,
    percorso_file VARCHAR(255) NOT NULL,
    tipo_file VARCHAR(100) NOT NULL,
    dimensione INT NOT NULL,
    data_caricamento DATETIME NOT NULL,
    descrizione TEXT,
    categoria VARCHAR(50) NOT NULL DEFAULT 'generale',
    utente_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (pratica_id) REFERENCES pratiche(id) ON DELETE CASCADE,
    FOREIGN KEY (utente_id) REFERENCES utenti(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
