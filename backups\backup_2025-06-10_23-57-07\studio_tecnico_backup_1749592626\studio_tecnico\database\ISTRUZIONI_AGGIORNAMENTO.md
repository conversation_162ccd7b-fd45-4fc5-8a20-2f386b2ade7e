# Istruzioni Aggiornamento Database - Studio Tecnico

## 🚨 IMPORTANTE: Leggere Completamente Prima di Procedere

### ⚠️ **PROBLEMI CRITICI IDENTIFICATI**

Il database attuale **NON supporta** completamente le funzionalità implementate:

1. **❌ STATI PRATICHE INCOMPLETI**
   - Attuali: `in_attesa`, `in_revisione`, `approvata`
   - Mancanti: `completata`, `sospesa`, `respinta`
   - **Impatto**: Workflow automatizzato non funziona

2. **❌ TABELLE NOTIFICHE ASSENTI**
   - <PERSON>bella `notifiche` non esiste
   - Tabella `notifiche_preferenze` non esiste
   - **Impatto**: Sistema notifiche non funziona

3. **⚠️ INDICI PERFORMANCE MANCANTI**
   - Nessun indice su `stato`, `data_scadenza`
   - **Impatto**: Query lente su grandi dataset

## 📋 Pre-Requisiti

### Verifica Ambiente
- ✅ MySQL 5.7+ o MariaDB 10.2+
- ✅ Accesso amministrativo al database
- ✅ Spazio disco sufficiente per backup
- ✅ Permessi per creare tabelle, indici, stored procedures

### Backup Obbligatorio
```bash
# Backup completo database
mysqldump -u username -p --single-transaction --routines --triggers studio_tecnico > backup_pre_update_$(date +%Y%m%d_%H%M%S).sql

# Verifica backup
ls -la backup_pre_update_*.sql
```

## 🔧 Procedura di Aggiornamento

### FASE 1: Preparazione

#### 1.1 Verifica Stato Attuale
```sql
-- Connetti al database
mysql -u username -p studio_tecnico

-- Verifica stati pratiche esistenti
SELECT stato, COUNT(*) FROM pratiche GROUP BY stato;

-- Verifica esistenza tabelle notifiche
SHOW TABLES LIKE 'notifiche%';
```

#### 1.2 Controllo Compatibilità
```sql
-- Verifica che non ci siano stati non supportati
SELECT DISTINCT stato FROM pratiche 
WHERE stato NOT IN ('in_attesa', 'in_revisione', 'approvata');

-- Se il risultato è vuoto, procedi. Altrimenti contatta il supporto.
```

### FASE 2: Esecuzione Aggiornamento

#### 2.1 Esecuzione Script Principale
```bash
# Esegui lo script di aggiornamento
mysql -u username -p studio_tecnico < database/update_database_complete.sql

# Monitora output per errori
# Lo script include verifiche automatiche e report
```

#### 2.2 Verifica Immediata
```bash
# Esegui script di verifica
mysql -u username -p studio_tecnico < database/verify_database_update.sql

# Controlla output per conferma successo
```

### FASE 3: Test Funzionalità

#### 3.1 Test PraticheModel
```bash
# Accedi via browser
http://localhost/progetti/studio_tecnico/test_pratiche.php

# Verifica che tutti i test passino
# Controlla log per errori
```

#### 3.2 Test Sistema Notifiche
```bash
# Test notifiche
http://localhost/progetti/studio_tecnico/test_notifiche.php

# Verifica dashboard notifiche
http://localhost/progetti/studio_tecnico/notifiche
```

## 📊 Cosa Viene Modificato

### Tabella `pratiche`
```sql
-- PRIMA (3 stati)
stato enum('in_attesa','in_revisione','approvata')

-- DOPO (6 stati)
stato enum('in_attesa','in_revisione','approvata','completata','sospesa','respinta')
```

### Nuove Tabelle
```sql
-- Tabella notifiche (nuova)
CREATE TABLE notifiche (
  id, user_id, tipo, titolo, messaggio, priorita,
  link_azione, metadata, letta, data_creazione, data_lettura
);

-- Tabella preferenze (nuova)  
CREATE TABLE notifiche_preferenze (
  id, user_id, tipo_notifica, email_enabled, 
  push_enabled, soglia_giorni
);
```

### Nuovi Indici
```sql
-- Performance pratiche
idx_pratiche_stato
idx_pratiche_data_scadenza
idx_pratiche_stato_data

-- Performance notifiche
idx_user_letta
idx_data_creazione
idx_tipo
```

## 🔄 Rollback (Se Necessario)

### In Caso di Problemi
```bash
# Stop applicazione
# Ripristina backup
mysql -u username -p studio_tecnico < backup_pre_update_YYYYMMDD_HHMMSS.sql

# Verifica ripristino
mysql -u username -p -e "SELECT COUNT(*) FROM studio_tecnico.pratiche;"
```

### Rollback Parziale
```sql
-- Solo stati pratiche (se necessario)
ALTER TABLE pratiche 
MODIFY COLUMN stato ENUM('in_attesa','in_revisione','approvata') DEFAULT 'in_attesa';

-- Rimuovi tabelle notifiche (se necessario)
DROP TABLE IF EXISTS notifiche_preferenze;
DROP TABLE IF EXISTS notifiche;
```

## ✅ Verifica Successo

### Checklist Post-Aggiornamento
- [ ] **Stati Pratiche**: 6 stati disponibili
- [ ] **Tabelle Notifiche**: Esistenti e popolate
- [ ] **Indici**: Creati correttamente
- [ ] **Foreign Keys**: Funzionanti
- [ ] **Stored Procedures**: Operative
- [ ] **Triggers**: Attivi
- [ ] **Test Pratiche**: Tutti passati
- [ ] **Test Notifiche**: Tutti passati
- [ ] **Dashboard**: Funzionante
- [ ] **Performance**: Migliorata

### Query di Verifica Rapida
```sql
-- Verifica stati pratiche
SELECT 'Stati Pratiche' as test, COUNT(DISTINCT stato) as count FROM pratiche;
-- Risultato atteso: count = 6 (o meno se non tutti gli stati sono usati)

-- Verifica tabelle notifiche
SELECT 'Tabelle Notifiche' as test, COUNT(*) as notifiche FROM notifiche;
-- Risultato atteso: count > 0

-- Verifica preferenze
SELECT 'Preferenze' as test, COUNT(*) as preferenze FROM notifiche_preferenze;
-- Risultato atteso: count > 0
```

## 🚨 Risoluzione Problemi

### Errore: "Unknown column 'completata'"
```sql
-- Verifica che l'enum sia stato aggiornato
SHOW COLUMNS FROM pratiche LIKE 'stato';
-- Se non aggiornato, esegui manualmente:
ALTER TABLE pratiche MODIFY COLUMN stato ENUM('in_attesa','in_revisione','approvata','completata','sospesa','respinta') DEFAULT 'in_attesa';
```

### Errore: "Table 'notifiche' doesn't exist"
```sql
-- Verifica esistenza tabelle
SHOW TABLES LIKE 'notifiche%';
-- Se mancanti, esegui di nuovo la sezione FASE 2 dello script
```

### Errore: "Foreign key constraint fails"
```sql
-- Verifica utenti esistenti
SELECT COUNT(*) FROM users;
-- Se 0, crea almeno un utente admin prima di procedere
```

### Performance Lente
```sql
-- Verifica indici
SHOW INDEX FROM pratiche;
SHOW INDEX FROM notifiche;
-- Se mancanti, esegui di nuovo la sezione indici dello script
```

## 📞 Supporto

### Log da Controllare
- `/var/log/mysql/error.log` (MySQL)
- `error_log` PHP (per test applicazione)
- Browser Console (per test frontend)

### Informazioni per Supporto
- Versione MySQL/MariaDB
- Output script aggiornamento
- Output script verifica
- Log errori specifici
- Backup disponibili

## 🎯 Risultato Atteso

Dopo l'aggiornamento avrai:
- ✅ **Workflow Pratiche** con 6 stati e transizioni validate
- ✅ **Sistema Notifiche** completo e configurato
- ✅ **Dashboard Avanzata** con statistiche real-time
- ✅ **Performance Ottimizzate** con indici dedicati
- ✅ **Controllo Scadenze** automatico
- ✅ **Compatibilità Completa** con dati esistenti

---

**⚠️ IMPORTANTE**: Non procedere senza aver fatto il backup completo del database!
