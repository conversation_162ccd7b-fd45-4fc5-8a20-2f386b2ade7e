<?php
namespace App\Controllers;

use PDO;
use PDOException;
use ZipArchive;

class AdminController {
    private $db;
    private $config;

    public function __construct(PDO $db) {
        $this->db = $db;
        $this->config = require_once ROOT_PATH . '/config/app.php';
        
        // Verifica se l'utente è admin
        if (!isset($_SESSION['user']) || !isset($_SESSION['user']['role']) || $_SESSION['user']['role'] !== 'admin') {
            $_SESSION['error'] = "Accesso negato. È richiesto il ruolo di amministratore.";
            header('Location: ' . BASE_URL . 'login');
            exit;
        }
    }

    public function index() {
        // Recupera le statistiche per la dashboard
        try {
            $stats = [
                'users' => $this->db->query("SELECT COUNT(*) FROM users")->fetchColumn(),
                'active_users' => $this->db->query("SELECT COUNT(*) FROM users WHERE active = 1")->fetchColumn(),
                'projects' => $this->db->query("SELECT COUNT(*) FROM progetti")->fetchColumn(),
                'active_projects' => $this->db->query("SELECT COUNT(*) FROM progetti WHERE stato = 'in_corso'")->fetchColumn(),
                'clients' => $this->db->query("SELECT COUNT(*) FROM clienti")->fetchColumn(),
                'active_clients' => $this->db->query("SELECT COUNT(*) FROM clienti")->fetchColumn(),
                'practices' => $this->db->query("SELECT COUNT(*) FROM pratiche")->fetchColumn(),
                'active_practices' => $this->db->query("SELECT COUNT(*) FROM pratiche WHERE stato = 'in_revisione'")->fetchColumn()
            ];
            
            include VIEWS_DIR . '/admin/index.php';
        } catch (PDOException $e) {
            error_log("Errore nel recupero delle statistiche: " . $e->getMessage());
            // In caso di errore, inizializza l'array stats con valori di default
            $stats = [
                'users' => 0,
                'active_users' => 0,
                'projects' => 0,
                'active_projects' => 0,
                'clients' => 0,
                'active_clients' => 0,
                'practices' => 0,
                'active_practices' => 0
            ];
            $_SESSION['error'] = "Si è verificato un errore nel recupero delle statistiche.";
            include VIEWS_DIR . '/admin/index.php';
        }
    }

    public function users() {
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
        error_log("AdminController::users() called");
        
        try {
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $perPage = 10;
            $offset = ($page - 1) * $perPage;

            error_log("Executing users query with page: $page, perPage: $perPage");

            // Costruzione della query base
            $baseQuery = "FROM users";
            $whereConditions = [];
            $params = [];

            // Filtri
            if (!empty($_GET['search'])) {
                $whereConditions[] = "username LIKE ?";
                $params[] = "%" . $_GET['search'] . "%";
            }

            if (!empty($_GET['role'])) {
                $whereConditions[] = "ruolo = ?";
                $params[] = $_GET['role'];
            }

            // Composizione WHERE
            $whereClause = '';
            if (!empty($whereConditions)) {
                $whereClause = "WHERE " . implode(" AND ", $whereConditions);
            }

            // Conteggio totale per la paginazione
            $countQuery = "SELECT COUNT(*) " . $baseQuery . " " . $whereClause;
            $stmt = $this->db->prepare($countQuery);
            $stmt->execute($params);
            $totalUsers = $stmt->fetchColumn();
            $totalPages = ceil($totalUsers / $perPage);

            error_log("Total users found: $totalUsers, Total pages: $totalPages");

            // Query principale con paginazione
            $query = "SELECT * " . $baseQuery . " " . $whereClause . " 
                     ORDER BY id DESC LIMIT " . $perPage . " OFFSET " . $offset;
            error_log("Executing query: $query");
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

            error_log("Found " . count($users) . " users");
            error_log("Users data: " . print_r($users, true));

            include VIEWS_DIR . '/admin/users/index.php';
        } catch (PDOException $e) {
            error_log("Database error in users(): " . $e->getMessage());
            error_log($e->getTraceAsString());
            $_SESSION['error'] = "Si è verificato un errore nel recupero degli utenti.";
            header('Location: ' . BASE_URL . 'admin');
            exit;
        } catch (Exception $e) {
            error_log("General error in users(): " . $e->getMessage());
            error_log($e->getTraceAsString());
            $_SESSION['error'] = "Si è verificato un errore imprevisto.";
            header('Location: ' . BASE_URL . 'admin');
            exit;
        }
    }

    public function createUser() {
        try {
            $data = $_POST;
            
            // Validazione
            if (empty($data['username']) || empty($data['password'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Username e password sono obbligatori']);
                return;
            }

            // Verifica se l'username esiste già
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
            $stmt->execute([$data['username']]);
            if ($stmt->fetchColumn() > 0) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Username già in uso']);
                return;
            }

            // Inserimento nuovo utente
            $stmt = $this->db->prepare("INSERT INTO users (username, password, ruolo) VALUES (?, ?, ?)");
            $success = $stmt->execute([
                $data['username'],
                password_hash($data['password'], PASSWORD_DEFAULT),
                $data['role'] ?? 'user'
            ]);

            echo json_encode(['success' => $success]);
        } catch (PDOException $e) {
            error_log("Errore nella creazione dell'utente: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => "Errore durante la creazione dell'utente"]);
        }
    }

    public function updateUser() {
        try {
            $data = $_POST;
            
            // Validazione
            if (empty($data['id']) || empty($data['username'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Dati mancanti']);
                return;
            }

            // Verifica se l'username esiste già per altri utenti
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?");
            $stmt->execute([$data['username'], $data['id']]);
            if ($stmt->fetchColumn() > 0) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Username già in uso']);
                return;
            }

            // Preparazione query di update
            $updateFields = ["username = ?", "ruolo = ?"];
            $params = [$data['username'], $data['role']];

            // Se è stata fornita una nuova password
            if (!empty($data['password'])) {
                $updateFields[] = "password = ?";
                $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
            }

            $params[] = $data['id'];

            // Aggiornamento utente
            $query = "UPDATE users SET " . implode(", ", $updateFields) . " WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $success = $stmt->execute($params);

            echo json_encode(['success' => $success]);
        } catch (PDOException $e) {
            error_log("Errore nell'aggiornamento dell'utente: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => "Errore durante l'aggiornamento dell'utente"]);
        }
    }

    public function deleteUser() {
        try {
            if (empty($_POST['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ID utente mancante']);
                return;
            }

            // Non permettere l'eliminazione dell'utente admin principale
            $stmt = $this->db->prepare("SELECT username, ruolo FROM users WHERE id = ?");
            $stmt->execute([$_POST['id']]);
            $user = $stmt->fetch();

            if ($user['username'] === 'admin' && $user['ruolo'] === 'admin') {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => "Non è possibile eliminare l'amministratore principale"]);
                return;
            }

            $stmt = $this->db->prepare("DELETE FROM users WHERE id = ?");
            $success = $stmt->execute([$_POST['id']]);

            echo json_encode(['success' => $success]);
        } catch (PDOException $e) {
            error_log("Errore nell'eliminazione dell'utente: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => "Errore durante l'eliminazione dell'utente"]);
        }
    }

    public function viewLogs() {
        if ($_SESSION['user']['role'] !== 'admin') {
            header('Location: ' . BASE_URL);
            exit;
        }
        include ROOT_PATH . '/logs/log_viewer.php';
    }

    public function refreshLogs() {
        if ($_SESSION['user']['role'] !== 'admin') {
            header('HTTP/1.1 403 Forbidden');
            exit;
        }

        $logFile = ROOT_PATH . '/logs/error.log';
        if (file_exists($logFile)) {
            $logs = file_get_contents($logFile);
            echo htmlspecialchars($logs);
        } else {
            echo "File di log non trovato.";
        }
    }
}
