/* Variabili CSS per tema chiaro e scuro */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --hover-color: #3b82f6;
    --danger-color: #ef4444;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
}

[data-theme="dark"] {
    --primary-color: #3b82f6;
    --secondary-color: #94a3b8;
    --background-color: #0f172a;
    --surface-color: #1e293b;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --border-color: #334155;
    --hover-color: #60a5fa;
    --danger-color: #f87171;
    --success-color: #4ade80;
    --warning-color: #fbbf24;
    --info-color: #60a5fa;
}

/* Reset e stili base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: var(--background-color);
    color: var(--text-primary);
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    line-height: 1.6;
    transition: background-color 0.3s, color 0.3s;
}

/* Layout */
.container {
    max-width: 1400px;
    padding: 0 1rem;
    margin: 0 auto;
}

/* Navbar */
.navbar {
    background-color: var(--surface-color) !important;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

.navbar-brand {
    color: var(--primary-color) !important;
    font-weight: 700;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-brand i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.navbar-toggler {
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    padding: 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color), 0.25);
}

.navbar-toggler-icon {
    background-image: none !important;
    position: relative;
}

.navbar-toggler-icon::before {
    content: '\f0c9';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-primary);
}

.nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    font-size: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link i {
    color: inherit;
    font-size: 1rem;
    width: 1.25rem;
    text-align: center;
}

.nav-link:hover, 
.nav-link.active {
    color: var(--primary-color) !important;
    background-color: var(--background-color);
}

.dropdown .btn-link.nav-link {
    padding: 0.5rem;
    font-size: 1.25rem;
}

.dropdown .btn-link.nav-link:hover {
    background: none;
    color: var(--primary-color) !important;
}

.dropdown-menu {
    border-color: var(--border-color);
    box-shadow: var(--shadow-md);
    padding: 0.5rem;
}

.dropdown-item {
    color: var(--text-primary);
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
}

.dropdown-item:hover {
    background-color: var(--background-color);
    color: var(--primary-color);
}

/* Card */
.card {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.card-header {
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem;
    border-radius: 1rem 1rem 0 0;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.card-text {
    color: var(--text-secondary);
}

/* Bottoni */
.btn {
    padding: 0.5rem 1.25rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

.btn-primary:hover {
    background-color: var(--hover-color);
    border-color: var(--hover-color);
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: #fff;
}

.btn-outline-primary.btn-sm {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-width: 1.5px;
}

.btn-outline-primary.btn-sm i {
    font-size: 1rem;
}

/* Form */
.form-control {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.form-label {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Tabelle */
.table {
    background-color: var(--surface-color);
    color: var(--text-primary);
    border-radius: 0.5rem;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.table th {
    background-color: var(--background-color);
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table-hover tbody tr:hover {
    background-color: var(--background-color);
}

/* Alert */
.alert {
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    border: none;
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.alert-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
}

/* Badge */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 500;
    border-radius: 9999px;
}

.badge.bg-primary { background-color: var(--primary-color) !important; }
.badge.bg-success { background-color: var(--success-color) !important; }
.badge.bg-danger { background-color: var(--danger-color) !important; }
.badge.bg-warning { background-color: var(--warning-color) !important; }
.badge.bg-info { background-color: var(--info-color) !important; }

/* Utilities */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

/* Animazioni */
.fade-enter {
    opacity: 0;
    transform: translateY(10px);
}

.fade-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s, transform 0.3s;
}

/* Media Queries */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: 0.5rem;
        box-shadow: var(--shadow-sm);
    }
    
    .container {
        padding: 0 0.5rem;
    }
}