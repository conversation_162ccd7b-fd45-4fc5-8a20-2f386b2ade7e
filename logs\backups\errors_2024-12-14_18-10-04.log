[14-Dec-2024 17:33:02 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:33:02 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:33:02 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:33:02 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:33:02 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:33:02 Europe/Rome] Router inizializzato
[14-Dec-2024 17:33:02 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:33:02 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:33:02 Europe/Rome] Route home definita
[14-Dec-2024 17:33:02 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:33:02 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:33:02 Europe/Rome] Route progetti definite
[14-Dec-2024 17:33:02 Europe/Rome] Route clienti definite
[14-Dec-2024 17:33:02 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:33:02 Europe/Rome] URL richiesto: 
[14-Dec-2024 17:33:02 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 17:33:02 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:33:02 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:33:02 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:33:02 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:33:02 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:33:02 Europe/Rome] Router inizializzato
[14-Dec-2024 17:33:02 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:33:02 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:33:02 Europe/Rome] Route home definita
[14-Dec-2024 17:33:02 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:33:02 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:33:02 Europe/Rome] Route progetti definite
[14-Dec-2024 17:33:02 Europe/Rome] Route clienti definite
[14-Dec-2024 17:33:02 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:33:02 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 17:33:02 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 17:33:02 Europe/Rome] 
==================================================
[14-Dec-2024 17:33:02 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 17:33:02 Europe/Rome] ==================================================

[14-Dec-2024 17:33:02 Europe/Rome] Clienti totali: 2
[14-Dec-2024 17:33:02 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 17:33:02 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 17:33:02 Europe/Rome] Progetti totali: 1
[14-Dec-2024 17:33:02 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 17:33:02 Europe/Rome] 
==================================================
[14-Dec-2024 17:33:02 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 17:33:02 Europe/Rome] ==================================================

[14-Dec-2024 17:33:02 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:33:02 Europe/Rome] Route dispatched
[14-Dec-2024 17:33:05 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:33:05 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:34:54 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:34:54 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:34:54 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:34:54 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:34:54 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:34:54 Europe/Rome] Router inizializzato
[14-Dec-2024 17:34:54 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:34:54 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:34:54 Europe/Rome] Route home definita
[14-Dec-2024 17:34:54 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:34:54 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:34:54 Europe/Rome] Route progetti definite
[14-Dec-2024 17:34:54 Europe/Rome] Route clienti definite
[14-Dec-2024 17:34:54 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:34:54 Europe/Rome] URL richiesto: clienti/nuovo
[14-Dec-2024 17:34:54 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 17:34:54 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:34:54 Europe/Rome] Route dispatched
[14-Dec-2024 17:34:57 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:34:57 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:35:40 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:35:40 Europe/Rome] Errore nel recupero dei clienti: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'c.attivo' in 'where clause'
[14-Dec-2024 17:35:40 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:35:50 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:35:50 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:35:50 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:35:50 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:35:50 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:35:50 Europe/Rome] Router inizializzato
[14-Dec-2024 17:35:50 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:35:50 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:35:50 Europe/Rome] Route home definita
[14-Dec-2024 17:35:50 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:35:50 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:35:50 Europe/Rome] Route progetti definite
[14-Dec-2024 17:35:50 Europe/Rome] Route clienti definite
[14-Dec-2024 17:35:50 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:35:50 Europe/Rome] URL richiesto: test/log_viewer.php
[14-Dec-2024 17:35:50 Europe/Rome] Route dispatched
[14-Dec-2024 17:36:02 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:36:02 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:36:02 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:36:02 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:36:02 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:36:02 Europe/Rome] Router inizializzato
[14-Dec-2024 17:36:02 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:36:02 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:36:02 Europe/Rome] Route home definita
[14-Dec-2024 17:36:02 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:36:02 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:36:02 Europe/Rome] Route progetti definite
[14-Dec-2024 17:36:02 Europe/Rome] Route clienti definite
[14-Dec-2024 17:36:02 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:36:02 Europe/Rome] URL richiesto: logout
[14-Dec-2024 17:36:02 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:36:02 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:36:02 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:36:02 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:36:02 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:36:02 Europe/Rome] Router inizializzato
[14-Dec-2024 17:36:02 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:36:02 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:36:02 Europe/Rome] Route home definita
[14-Dec-2024 17:36:02 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:36:02 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:36:02 Europe/Rome] Route progetti definite
[14-Dec-2024 17:36:02 Europe/Rome] Route clienti definite
[14-Dec-2024 17:36:02 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:36:02 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:36:02 Europe/Rome] Route dispatched
[14-Dec-2024 17:36:05 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:36:05 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:36:05 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:36:05 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:36:05 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:36:05 Europe/Rome] Router inizializzato
[14-Dec-2024 17:36:05 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:36:05 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:36:05 Europe/Rome] Route home definita
[14-Dec-2024 17:36:05 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:36:05 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:36:05 Europe/Rome] Route progetti definite
[14-Dec-2024 17:36:05 Europe/Rome] Route clienti definite
[14-Dec-2024 17:36:05 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:36:05 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:36:05 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 17:36:05 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 17:36:05 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 17:36:05 Europe/Rome] Password verificata con successo
[14-Dec-2024 17:36:05 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[14-Dec-2024 17:36:05 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:36:05 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:36:05 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:36:05 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:36:05 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:36:05 Europe/Rome] Router inizializzato
[14-Dec-2024 17:36:05 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:36:05 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:36:05 Europe/Rome] Route home definita
[14-Dec-2024 17:36:05 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:36:05 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:36:05 Europe/Rome] Route progetti definite
[14-Dec-2024 17:36:05 Europe/Rome] Route clienti definite
[14-Dec-2024 17:36:05 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:36:05 Europe/Rome] URL richiesto: admin
[14-Dec-2024 17:36:05 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 17:36:05 Europe/Rome] Route dispatched
[14-Dec-2024 17:36:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:36:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:36:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:36:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:36:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:36:24 Europe/Rome] Router inizializzato
[14-Dec-2024 17:36:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:36:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:36:24 Europe/Rome] Route home definita
[14-Dec-2024 17:36:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:36:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:36:24 Europe/Rome] Route progetti definite
[14-Dec-2024 17:36:24 Europe/Rome] Route clienti definite
[14-Dec-2024 17:36:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:36:24 Europe/Rome] URL richiesto: admin/users
[14-Dec-2024 17:36:24 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 17:36:24 Europe/Rome] AdminController::users() called
[14-Dec-2024 17:36:24 Europe/Rome] Executing users query with page: 1, perPage: 10
[14-Dec-2024 17:36:24 Europe/Rome] Total users found: 2, Total pages: 1
[14-Dec-2024 17:36:24 Europe/Rome] Executing query: SELECT * FROM users  
                     ORDER BY id DESC LIMIT 10 OFFSET 0
[14-Dec-2024 17:36:24 Europe/Rome] Found 2 users
[14-Dec-2024 17:36:24 Europe/Rome] Users data: Array
(
    [0] => Array
        (
            [id] => 6
            [username] => Mauro
            [email] => 
            [password] => $2y$10$RQ704WI9LMMrkTkX4A8OjOMDm12m6ovhOVpkT1C2RRSwPwx3W/a7e
            [ruolo] => user
            [active] => 1
        )

    [1] => Array
        (
            [id] => 5
            [username] => admin
            [email] => <EMAIL>
            [password] => $2y$10$VT/tvsPz72/pHDdpDdGCieS6CqO/ouQhwAcuMHsoDZxUwR2YXHsdC
            [ruolo] => admin
            [active] => 1
        )

)

[14-Dec-2024 17:36:24 Europe/Rome] Route dispatched
[14-Dec-2024 17:36:27 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:36:27 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:36:27 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:36:27 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:36:27 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:36:27 Europe/Rome] Router inizializzato
[14-Dec-2024 17:36:27 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:36:27 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:36:27 Europe/Rome] Route home definita
[14-Dec-2024 17:36:27 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:36:27 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:36:27 Europe/Rome] Route progetti definite
[14-Dec-2024 17:36:27 Europe/Rome] Route clienti definite
[14-Dec-2024 17:36:27 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:36:27 Europe/Rome] URL richiesto: 
[14-Dec-2024 17:36:27 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 17:36:27 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:36:27 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:36:27 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:36:27 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:36:27 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:36:27 Europe/Rome] Router inizializzato
[14-Dec-2024 17:36:27 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:36:27 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:36:27 Europe/Rome] Route home definita
[14-Dec-2024 17:36:27 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:36:27 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:36:27 Europe/Rome] Route progetti definite
[14-Dec-2024 17:36:27 Europe/Rome] Route clienti definite
[14-Dec-2024 17:36:27 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:36:27 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 17:36:27 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 17:36:27 Europe/Rome] 
==================================================
[14-Dec-2024 17:36:27 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 17:36:27 Europe/Rome] ==================================================

[14-Dec-2024 17:36:27 Europe/Rome] Clienti totali: 2
[14-Dec-2024 17:36:27 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 17:36:27 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 17:36:27 Europe/Rome] Progetti totali: 1
[14-Dec-2024 17:36:27 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 17:36:27 Europe/Rome] 
==================================================
[14-Dec-2024 17:36:27 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 17:36:27 Europe/Rome] ==================================================

[14-Dec-2024 17:36:27 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:36:27 Europe/Rome] Route dispatched
[14-Dec-2024 17:36:29 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:36:29 Europe/Rome] Errore nel recupero dei clienti: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'c.attivo' in 'where clause'
[14-Dec-2024 17:36:29 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:38:43 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:38:43 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:38:50 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:38:50 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:38:50 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:38:50 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:38:50 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:38:50 Europe/Rome] Router inizializzato
[14-Dec-2024 17:38:50 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:38:50 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:38:50 Europe/Rome] Route home definita
[14-Dec-2024 17:38:50 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:38:50 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:38:50 Europe/Rome] Route progetti definite
[14-Dec-2024 17:38:50 Europe/Rome] Route clienti definite
[14-Dec-2024 17:38:50 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:38:50 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 17:38:50 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 17:38:50 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:38:50 Europe/Rome] Route dispatched
[14-Dec-2024 17:38:56 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:38:56 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:38:56 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:38:56 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:38:56 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:38:56 Europe/Rome] Router inizializzato
[14-Dec-2024 17:38:56 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:38:56 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:38:56 Europe/Rome] Route home definita
[14-Dec-2024 17:38:56 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:38:56 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:38:56 Europe/Rome] Route progetti definite
[14-Dec-2024 17:38:56 Europe/Rome] Route clienti definite
[14-Dec-2024 17:38:56 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:38:56 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 17:38:56 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 17:38:56 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 17:38:56 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 17:38:56 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 17:38:56 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 17:38:56 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => cila
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 17:38:56 Europe/Rome] Route dispatched
[14-Dec-2024 17:38:56 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:38:56 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => cila
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 17:38:56 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 17:38:56 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 17:38:57 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:38:57 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:39:05 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:39:05 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:39:05 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:39:05 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:39:05 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:39:05 Europe/Rome] Router inizializzato
[14-Dec-2024 17:39:05 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:39:05 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:39:05 Europe/Rome] Route home definita
[14-Dec-2024 17:39:05 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:39:05 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:39:05 Europe/Rome] Route progetti definite
[14-Dec-2024 17:39:05 Europe/Rome] Route clienti definite
[14-Dec-2024 17:39:05 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:39:05 Europe/Rome] URL richiesto: logout
[14-Dec-2024 17:39:05 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:39:05 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:39:05 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:39:05 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:39:05 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:39:05 Europe/Rome] Router inizializzato
[14-Dec-2024 17:39:05 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:39:05 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:39:05 Europe/Rome] Route home definita
[14-Dec-2024 17:39:05 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:39:05 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:39:05 Europe/Rome] Route progetti definite
[14-Dec-2024 17:39:05 Europe/Rome] Route clienti definite
[14-Dec-2024 17:39:05 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:39:05 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:39:05 Europe/Rome] Route dispatched
[14-Dec-2024 17:39:08 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:39:08 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:39:08 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:39:08 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:39:08 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:39:08 Europe/Rome] Router inizializzato
[14-Dec-2024 17:39:08 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:39:08 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:39:08 Europe/Rome] Route home definita
[14-Dec-2024 17:39:08 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:39:08 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:39:08 Europe/Rome] Route progetti definite
[14-Dec-2024 17:39:08 Europe/Rome] Route clienti definite
[14-Dec-2024 17:39:08 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:39:08 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:39:08 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 17:39:08 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 17:39:08 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 17:39:08 Europe/Rome] Password verificata con successo
[14-Dec-2024 17:39:08 Europe/Rome] Login riuscito per l'utente: Mauro con ruolo: user
[14-Dec-2024 17:39:08 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:39:08 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:39:08 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:39:08 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:39:08 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:39:08 Europe/Rome] Router inizializzato
[14-Dec-2024 17:39:08 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:39:08 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:39:08 Europe/Rome] Route home definita
[14-Dec-2024 17:39:08 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:39:08 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:39:08 Europe/Rome] Route progetti definite
[14-Dec-2024 17:39:08 Europe/Rome] Route clienti definite
[14-Dec-2024 17:39:08 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:39:08 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 17:39:08 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:39:08 Europe/Rome] 
==================================================
[14-Dec-2024 17:39:08 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 17:39:08 Europe/Rome] ==================================================

[14-Dec-2024 17:39:08 Europe/Rome] Clienti totali: 2
[14-Dec-2024 17:39:08 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 17:39:08 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 17:39:08 Europe/Rome] Progetti totali: 1
[14-Dec-2024 17:39:08 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 17:39:08 Europe/Rome] 
==================================================
[14-Dec-2024 17:39:08 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 17:39:08 Europe/Rome] ==================================================

[14-Dec-2024 17:39:08 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:39:08 Europe/Rome] Route dispatched
[14-Dec-2024 17:39:11 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:39:11 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:42:46 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:42:46 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:42:46 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:42:46 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:42:46 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:42:46 Europe/Rome] Router inizializzato
[14-Dec-2024 17:42:46 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:42:46 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:42:46 Europe/Rome] Route home definita
[14-Dec-2024 17:42:46 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:42:46 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:42:46 Europe/Rome] Route progetti definite
[14-Dec-2024 17:42:46 Europe/Rome] Route clienti definite
[14-Dec-2024 17:42:46 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:42:46 Europe/Rome] URL richiesto: 
[14-Dec-2024 17:42:46 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:42:46 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:42:46 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:42:46 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:42:46 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:42:46 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:42:46 Europe/Rome] Router inizializzato
[14-Dec-2024 17:42:46 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:42:46 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:42:46 Europe/Rome] Route home definita
[14-Dec-2024 17:42:46 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:42:46 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:42:46 Europe/Rome] Route progetti definite
[14-Dec-2024 17:42:46 Europe/Rome] Route clienti definite
[14-Dec-2024 17:42:46 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:42:46 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 17:42:46 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:42:46 Europe/Rome] 
==================================================
[14-Dec-2024 17:42:46 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 17:42:46 Europe/Rome] ==================================================

[14-Dec-2024 17:42:46 Europe/Rome] Clienti totali: 2
[14-Dec-2024 17:42:46 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 17:42:46 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 17:42:46 Europe/Rome] Progetti totali: 1
[14-Dec-2024 17:42:46 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 17:42:46 Europe/Rome] 
==================================================
[14-Dec-2024 17:42:46 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 17:42:46 Europe/Rome] ==================================================

[14-Dec-2024 17:42:46 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:42:46 Europe/Rome] Route dispatched
[14-Dec-2024 17:42:49 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:42:49 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:42:54 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:42:54 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:42:54 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:42:54 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:42:54 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:42:54 Europe/Rome] Router inizializzato
[14-Dec-2024 17:42:54 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:42:54 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:42:54 Europe/Rome] Route home definita
[14-Dec-2024 17:42:54 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:42:54 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:42:54 Europe/Rome] Route progetti definite
[14-Dec-2024 17:42:54 Europe/Rome] Route clienti definite
[14-Dec-2024 17:42:54 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:42:54 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:42:54 Europe/Rome] Route dispatched
[14-Dec-2024 17:43:37 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:43:37 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:43:37 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:43:37 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:43:37 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:43:37 Europe/Rome] Router inizializzato
[14-Dec-2024 17:43:37 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:43:37 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:43:37 Europe/Rome] Route home definita
[14-Dec-2024 17:43:37 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:43:37 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:43:37 Europe/Rome] Route progetti definite
[14-Dec-2024 17:43:37 Europe/Rome] Route clienti definite
[14-Dec-2024 17:43:37 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:43:37 Europe/Rome] URL richiesto: 
[14-Dec-2024 17:43:37 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:43:37 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:43:37 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:43:37 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:43:37 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:43:37 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:43:37 Europe/Rome] Router inizializzato
[14-Dec-2024 17:43:37 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:43:37 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:43:37 Europe/Rome] Route home definita
[14-Dec-2024 17:43:37 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:43:37 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:43:37 Europe/Rome] Route progetti definite
[14-Dec-2024 17:43:37 Europe/Rome] Route clienti definite
[14-Dec-2024 17:43:37 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:43:37 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 17:43:37 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:43:37 Europe/Rome] 
==================================================
[14-Dec-2024 17:43:37 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 17:43:37 Europe/Rome] ==================================================

[14-Dec-2024 17:43:37 Europe/Rome] Clienti totali: 2
[14-Dec-2024 17:43:37 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 17:43:37 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 17:43:37 Europe/Rome] Progetti totali: 1
[14-Dec-2024 17:43:37 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 17:43:37 Europe/Rome] 
==================================================
[14-Dec-2024 17:43:37 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 17:43:37 Europe/Rome] ==================================================

[14-Dec-2024 17:43:37 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:43:37 Europe/Rome] Route dispatched
[14-Dec-2024 17:43:46 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:43:46 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:43:46 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:43:46 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:43:46 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:43:46 Europe/Rome] Router inizializzato
[14-Dec-2024 17:43:46 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:43:46 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:43:46 Europe/Rome] Route home definita
[14-Dec-2024 17:43:46 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:43:46 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:43:46 Europe/Rome] Route progetti definite
[14-Dec-2024 17:43:46 Europe/Rome] Route clienti definite
[14-Dec-2024 17:43:46 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:43:46 Europe/Rome] URL richiesto: 
[14-Dec-2024 17:43:46 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:43:46 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:43:46 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:43:46 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:43:46 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:43:46 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:43:46 Europe/Rome] Router inizializzato
[14-Dec-2024 17:43:46 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:43:46 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:43:46 Europe/Rome] Route home definita
[14-Dec-2024 17:43:46 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:43:46 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:43:46 Europe/Rome] Route progetti definite
[14-Dec-2024 17:43:46 Europe/Rome] Route clienti definite
[14-Dec-2024 17:43:46 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:43:46 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 17:43:46 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:43:46 Europe/Rome] 
==================================================
[14-Dec-2024 17:43:46 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 17:43:46 Europe/Rome] ==================================================

[14-Dec-2024 17:43:46 Europe/Rome] Clienti totali: 2
[14-Dec-2024 17:43:46 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 17:43:46 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 17:43:46 Europe/Rome] Progetti totali: 1
[14-Dec-2024 17:43:46 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 17:43:46 Europe/Rome] 
==================================================
[14-Dec-2024 17:43:46 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 17:43:46 Europe/Rome] ==================================================

[14-Dec-2024 17:43:46 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:43:46 Europe/Rome] Route dispatched
[14-Dec-2024 17:44:21 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:21 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:44:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:44:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:44:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:44:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:44:24 Europe/Rome] Router inizializzato
[14-Dec-2024 17:44:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:44:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:44:24 Europe/Rome] Route home definita
[14-Dec-2024 17:44:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:44:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:44:24 Europe/Rome] Route progetti definite
[14-Dec-2024 17:44:24 Europe/Rome] Route clienti definite
[14-Dec-2024 17:44:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:44:24 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:44:24 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:44:24 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:44:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:24 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:44:26 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:44:26 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:44:26 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:44:26 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:26 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:44:26 Europe/Rome] Router inizializzato
[14-Dec-2024 17:44:26 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:44:26 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:44:26 Europe/Rome] Route home definita
[14-Dec-2024 17:44:26 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:44:26 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:44:26 Europe/Rome] Route progetti definite
[14-Dec-2024 17:44:26 Europe/Rome] Route clienti definite
[14-Dec-2024 17:44:26 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:44:26 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:44:26 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:44:26 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:44:26 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:26 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:44:28 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:44:28 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:44:28 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:44:28 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:28 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:44:28 Europe/Rome] Router inizializzato
[14-Dec-2024 17:44:28 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:44:28 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:44:28 Europe/Rome] Route home definita
[14-Dec-2024 17:44:28 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:44:28 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:44:28 Europe/Rome] Route progetti definite
[14-Dec-2024 17:44:28 Europe/Rome] Route clienti definite
[14-Dec-2024 17:44:28 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:44:28 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:44:28 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:44:28 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:44:28 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:28 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:44:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:44:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:44:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:44:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:44:30 Europe/Rome] Router inizializzato
[14-Dec-2024 17:44:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:44:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:44:30 Europe/Rome] Route home definita
[14-Dec-2024 17:44:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:44:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:44:30 Europe/Rome] Route progetti definite
[14-Dec-2024 17:44:30 Europe/Rome] Route clienti definite
[14-Dec-2024 17:44:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:44:30 Europe/Rome] URL richiesto: clienti/dettagli/2
[14-Dec-2024 17:44:30 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:44:30 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:44:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:30 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:44:32 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:44:32 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:44:32 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:44:32 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:32 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:44:32 Europe/Rome] Router inizializzato
[14-Dec-2024 17:44:32 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:44:32 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:44:32 Europe/Rome] Route home definita
[14-Dec-2024 17:44:32 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:44:32 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:44:32 Europe/Rome] Route progetti definite
[14-Dec-2024 17:44:32 Europe/Rome] Route clienti definite
[14-Dec-2024 17:44:32 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:44:32 Europe/Rome] URL richiesto: clienti/modifica/1
[14-Dec-2024 17:44:32 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:44:32 Europe/Rome] Modifica cliente - ID ricevuto: 1
[14-Dec-2024 17:44:32 Europe/Rome] Visualizzazione form modifica con dati cliente: Array
(
    [id] => 1
    [nome] => Mauro
    [cognome] => Mazzarelli
    [email] => <EMAIL>
    [telefono] => 3392720049
    [indirizzo] => via della Stazione di Ciampino 151
    [cap_cliente] => 00118
    [data_registrazione] => 2024-12-08 08:04:27
    [tipo_cliente] => privato
    [ragione_sociale] => 
    [partita_iva] => 
    [codice_fiscale] => ****************
    [pec] => <EMAIL>
    [citta] => Roma
    [provincia] => RM
    [note] => sono io, ciao
    [attivo] => 1
)

[14-Dec-2024 17:44:32 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:44:32 Europe/Rome] Route dispatched
[14-Dec-2024 17:44:34 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:34 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:44:36 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:44:36 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:44:36 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:44:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:36 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:44:36 Europe/Rome] Router inizializzato
[14-Dec-2024 17:44:36 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:44:36 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:44:36 Europe/Rome] Route home definita
[14-Dec-2024 17:44:36 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:44:36 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:44:36 Europe/Rome] Route progetti definite
[14-Dec-2024 17:44:36 Europe/Rome] Route clienti definite
[14-Dec-2024 17:44:36 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:44:36 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:44:36 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:44:36 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:44:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:44:36 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:46:19 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:46:19 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:46:21 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:46:21 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:46:21 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:46:21 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:46:21 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:46:21 Europe/Rome] Router inizializzato
[14-Dec-2024 17:46:21 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:46:21 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:46:21 Europe/Rome] Route home definita
[14-Dec-2024 17:46:21 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:46:21 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:46:21 Europe/Rome] Route progetti definite
[14-Dec-2024 17:46:21 Europe/Rome] Route clienti definite
[14-Dec-2024 17:46:21 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:46:21 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:46:21 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:46:21 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:46:21 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:46:21 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:46:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:46:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:46:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:46:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:46:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:46:24 Europe/Rome] Router inizializzato
[14-Dec-2024 17:46:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:46:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:46:24 Europe/Rome] Route home definita
[14-Dec-2024 17:46:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:46:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:46:24 Europe/Rome] Route progetti definite
[14-Dec-2024 17:46:24 Europe/Rome] Route clienti definite
[14-Dec-2024 17:46:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:46:24 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:46:24 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:46:24 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:46:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:46:24 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:46:28 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:46:28 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:46:28 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:46:28 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:46:28 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:46:28 Europe/Rome] Router inizializzato
[14-Dec-2024 17:46:28 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:46:28 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:46:28 Europe/Rome] Route home definita
[14-Dec-2024 17:46:28 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:46:28 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:46:28 Europe/Rome] Route progetti definite
[14-Dec-2024 17:46:28 Europe/Rome] Route clienti definite
[14-Dec-2024 17:46:28 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:46:28 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:46:28 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:46:28 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:46:29 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:46:29 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:48:42 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:48:42 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:48:42 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:48:42 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:48:42 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:48:42 Europe/Rome] Router inizializzato
[14-Dec-2024 17:48:42 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:48:42 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:48:42 Europe/Rome] Route home definita
[14-Dec-2024 17:48:42 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:48:42 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:48:42 Europe/Rome] Route progetti definite
[14-Dec-2024 17:48:42 Europe/Rome] Route clienti definite
[14-Dec-2024 17:48:42 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:48:42 Europe/Rome] URL richiesto: logout
[14-Dec-2024 17:48:42 Europe/Rome] Dispatching URL: logout [GET]
[14-Dec-2024 17:48:42 Europe/Rome] Matching URL: logout
[14-Dec-2024 17:48:42 Europe/Rome] Route matched: /^logout$/i
[14-Dec-2024 17:48:42 Europe/Rome] Matches: Array
(
    [0] => logout
)

[14-Dec-2024 17:48:42 Europe/Rome] Route params: Array
(
)

[14-Dec-2024 17:48:42 Europe/Rome] Controller: AuthController
[14-Dec-2024 17:48:42 Europe/Rome] Action: logout
[14-Dec-2024 17:48:42 Europe/Rome] Params: Array
(
)

[14-Dec-2024 17:48:42 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:48:42 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:48:42 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:48:42 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:48:42 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:48:42 Europe/Rome] Router inizializzato
[14-Dec-2024 17:48:42 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:48:42 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:48:42 Europe/Rome] Route home definita
[14-Dec-2024 17:48:42 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:48:42 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:48:42 Europe/Rome] Route progetti definite
[14-Dec-2024 17:48:42 Europe/Rome] Route clienti definite
[14-Dec-2024 17:48:42 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:48:42 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:48:42 Europe/Rome] Dispatching URL: login [GET]
[14-Dec-2024 17:48:42 Europe/Rome] Matching URL: login
[14-Dec-2024 17:48:42 Europe/Rome] Route matched: /^login$/i
[14-Dec-2024 17:48:42 Europe/Rome] Matches: Array
(
    [0] => login
)

[14-Dec-2024 17:48:42 Europe/Rome] Route params: Array
(
)

[14-Dec-2024 17:48:42 Europe/Rome] Controller: AuthController
[14-Dec-2024 17:48:42 Europe/Rome] Action: showLoginForm
[14-Dec-2024 17:48:42 Europe/Rome] Params: Array
(
)

[14-Dec-2024 17:48:42 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:01 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:01 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:01 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:01 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:01 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:01 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:01 Europe/Rome] Route home definita
[14-Dec-2024 17:49:01 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:01 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:01 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:01 Europe/Rome] URL richiesto: 
[14-Dec-2024 17:49:01 Europe/Rome] Dispatching URL:  [GET]
[14-Dec-2024 17:49:01 Europe/Rome] Matching URL: 
[14-Dec-2024 17:49:01 Europe/Rome] Route matched: /^$/i
[14-Dec-2024 17:49:01 Europe/Rome] Matches: Array
(
    [0] => 
)

[14-Dec-2024 17:49:01 Europe/Rome] Route params: Array
(
)

[14-Dec-2024 17:49:01 Europe/Rome] Controller: HomeController
[14-Dec-2024 17:49:01 Europe/Rome] Action: index
[14-Dec-2024 17:49:01 Europe/Rome] Params: Array
(
)

[14-Dec-2024 17:49:01 Europe/Rome] Utente non autenticato, redirect al login
[14-Dec-2024 17:49:01 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:01 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:01 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:01 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:01 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:01 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:01 Europe/Rome] Route home definita
[14-Dec-2024 17:49:01 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:01 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:01 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:01 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:49:01 Europe/Rome] Dispatching URL: login [GET]
[14-Dec-2024 17:49:01 Europe/Rome] Matching URL: login
[14-Dec-2024 17:49:01 Europe/Rome] Route matched: /^login$/i
[14-Dec-2024 17:49:01 Europe/Rome] Matches: Array
(
    [0] => login
)

[14-Dec-2024 17:49:01 Europe/Rome] Route params: Array
(
)

[14-Dec-2024 17:49:01 Europe/Rome] Controller: AuthController
[14-Dec-2024 17:49:01 Europe/Rome] Action: showLoginForm
[14-Dec-2024 17:49:01 Europe/Rome] Params: Array
(
)

[14-Dec-2024 17:49:01 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:01 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:01 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:01 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:01 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:01 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:01 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:01 Europe/Rome] Route home definita
[14-Dec-2024 17:49:01 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:01 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:01 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:01 Europe/Rome] URL richiesto: assets/css/style.css
[14-Dec-2024 17:49:01 Europe/Rome] Dispatching URL: assets/css/style.css [GET]
[14-Dec-2024 17:49:01 Europe/Rome] Matching URL: assets/css/style.css
[14-Dec-2024 17:49:01 Europe/Rome] No route matched for URL: assets/css/style.css
[14-Dec-2024 17:49:01 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:01 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:01 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:01 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:01 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:01 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:01 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:01 Europe/Rome] Route home definita
[14-Dec-2024 17:49:01 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:01 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:01 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:01 Europe/Rome] URL richiesto: assets/css/navbar.css
[14-Dec-2024 17:49:01 Europe/Rome] Dispatching URL: assets/css/navbar.css [GET]
[14-Dec-2024 17:49:01 Europe/Rome] Matching URL: assets/css/navbar.css
[14-Dec-2024 17:49:01 Europe/Rome] No route matched for URL: assets/css/navbar.css
[14-Dec-2024 17:49:01 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:01 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:01 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:01 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:01 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:01 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:01 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:01 Europe/Rome] Route home definita
[14-Dec-2024 17:49:01 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:01 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:01 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:01 Europe/Rome] URL richiesto: assets/css/management.css
[14-Dec-2024 17:49:01 Europe/Rome] Dispatching URL: assets/css/management.css [GET]
[14-Dec-2024 17:49:01 Europe/Rome] Matching URL: assets/css/management.css
[14-Dec-2024 17:49:01 Europe/Rome] No route matched for URL: assets/css/management.css
[14-Dec-2024 17:49:01 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:01 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:01 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:01 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:01 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:01 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:01 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:01 Europe/Rome] Route home definita
[14-Dec-2024 17:49:01 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:01 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:01 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:01 Europe/Rome] URL richiesto: assets/css/theme.css
[14-Dec-2024 17:49:01 Europe/Rome] Dispatching URL: assets/css/theme.css [GET]
[14-Dec-2024 17:49:01 Europe/Rome] Matching URL: assets/css/theme.css
[14-Dec-2024 17:49:01 Europe/Rome] No route matched for URL: assets/css/theme.css
[14-Dec-2024 17:49:01 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:01 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:01 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:01 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:01 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:01 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:01 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:01 Europe/Rome] Route home definita
[14-Dec-2024 17:49:01 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:01 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:01 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:01 Europe/Rome] URL richiesto: assets/css/login.css
[14-Dec-2024 17:49:01 Europe/Rome] Dispatching URL: assets/css/login.css [GET]
[14-Dec-2024 17:49:01 Europe/Rome] Matching URL: assets/css/login.css
[14-Dec-2024 17:49:01 Europe/Rome] No route matched for URL: assets/css/login.css
[14-Dec-2024 17:49:01 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:01 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:01 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:01 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:01 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:01 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:01 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:01 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:01 Europe/Rome] Route home definita
[14-Dec-2024 17:49:01 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:01 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:01 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:01 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:01 Europe/Rome] URL richiesto: img/logo.png
[14-Dec-2024 17:49:01 Europe/Rome] Dispatching URL: img/logo.png [GET]
[14-Dec-2024 17:49:01 Europe/Rome] Matching URL: img/logo.png
[14-Dec-2024 17:49:01 Europe/Rome] No route matched for URL: img/logo.png
[14-Dec-2024 17:49:01 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:14 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:14 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:14 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:14 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:14 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:14 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:14 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:14 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:14 Europe/Rome] Route home definita
[14-Dec-2024 17:49:14 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:14 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:14 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:14 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:14 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:14 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:49:14 Europe/Rome] Dispatching URL: login [POST]
[14-Dec-2024 17:49:14 Europe/Rome] Matching URL: login
[14-Dec-2024 17:49:14 Europe/Rome] Route matched: /^login$/i
[14-Dec-2024 17:49:14 Europe/Rome] Matches: Array
(
    [0] => login
)

[14-Dec-2024 17:49:14 Europe/Rome] Route params: Array
(
)

[14-Dec-2024 17:49:14 Europe/Rome] Controller: AuthController
[14-Dec-2024 17:49:14 Europe/Rome] Action: login
[14-Dec-2024 17:49:14 Europe/Rome] Params: Array
(
)

[14-Dec-2024 17:49:14 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 17:49:14 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 17:49:14 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 17:49:14 Europe/Rome] Password verificata con successo
[14-Dec-2024 17:49:14 Europe/Rome] Login riuscito per l'utente: Mauro con ruolo: user
[14-Dec-2024 17:49:14 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:14 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:14 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:14 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:14 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:14 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:14 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:14 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:14 Europe/Rome] Route home definita
[14-Dec-2024 17:49:14 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:14 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:14 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:14 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:14 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:14 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 17:49:14 Europe/Rome] Dispatching URL: dashboard [GET]
[14-Dec-2024 17:49:14 Europe/Rome] Matching URL: dashboard
[14-Dec-2024 17:49:14 Europe/Rome] Route matched: /^dashboard$/i
[14-Dec-2024 17:49:14 Europe/Rome] Matches: Array
(
    [0] => dashboard
)

[14-Dec-2024 17:49:14 Europe/Rome] Route params: Array
(
)

[14-Dec-2024 17:49:14 Europe/Rome] Controller: DashboardController
[14-Dec-2024 17:49:14 Europe/Rome] Action: index
[14-Dec-2024 17:49:14 Europe/Rome] Params: Array
(
)

[14-Dec-2024 17:49:14 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:49:14 Europe/Rome] 
==================================================
[14-Dec-2024 17:49:14 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 17:49:14 Europe/Rome] ==================================================

[14-Dec-2024 17:49:14 Europe/Rome] Clienti totali: 2
[14-Dec-2024 17:49:14 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 17:49:14 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 17:49:14 Europe/Rome] Progetti totali: 1
[14-Dec-2024 17:49:14 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 17:49:14 Europe/Rome] 
==================================================
[14-Dec-2024 17:49:15 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 17:49:15 Europe/Rome] ==================================================

[14-Dec-2024 17:49:15 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:49:15 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:15 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:15 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:15 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:15 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:15 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:15 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:15 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:15 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:15 Europe/Rome] Route home definita
[14-Dec-2024 17:49:15 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:15 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:15 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:15 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:15 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:15 Europe/Rome] URL richiesto: assets/css/style.css
[14-Dec-2024 17:49:15 Europe/Rome] Dispatching URL: assets/css/style.css [GET]
[14-Dec-2024 17:49:15 Europe/Rome] Matching URL: assets/css/style.css
[14-Dec-2024 17:49:15 Europe/Rome] No route matched for URL: assets/css/style.css
[14-Dec-2024 17:49:15 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:15 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:15 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:15 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:15 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:15 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:15 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:15 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:15 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:15 Europe/Rome] Route home definita
[14-Dec-2024 17:49:15 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:15 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:15 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:15 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:15 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:15 Europe/Rome] URL richiesto: assets/css/navbar.css
[14-Dec-2024 17:49:15 Europe/Rome] Dispatching URL: assets/css/navbar.css [GET]
[14-Dec-2024 17:49:15 Europe/Rome] Matching URL: assets/css/navbar.css
[14-Dec-2024 17:49:15 Europe/Rome] No route matched for URL: assets/css/navbar.css
[14-Dec-2024 17:49:15 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:15 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:15 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:15 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:15 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:15 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:15 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:15 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:15 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:15 Europe/Rome] Route home definita
[14-Dec-2024 17:49:15 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:15 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:15 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:15 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:15 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:15 Europe/Rome] URL richiesto: assets/css/management.css
[14-Dec-2024 17:49:15 Europe/Rome] Dispatching URL: assets/css/management.css [GET]
[14-Dec-2024 17:49:15 Europe/Rome] Matching URL: assets/css/management.css
[14-Dec-2024 17:49:15 Europe/Rome] No route matched for URL: assets/css/management.css
[14-Dec-2024 17:49:15 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:15 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:15 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:15 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:15 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:15 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:15 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:15 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:15 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:15 Europe/Rome] Route home definita
[14-Dec-2024 17:49:15 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:15 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:15 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:15 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:15 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:15 Europe/Rome] URL richiesto: assets/css/theme.css
[14-Dec-2024 17:49:15 Europe/Rome] Dispatching URL: assets/css/theme.css [GET]
[14-Dec-2024 17:49:15 Europe/Rome] Matching URL: assets/css/theme.css
[14-Dec-2024 17:49:15 Europe/Rome] No route matched for URL: assets/css/theme.css
[14-Dec-2024 17:49:15 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:15 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:15 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:15 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:15 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:15 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:15 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:15 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:15 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:15 Europe/Rome] Route home definita
[14-Dec-2024 17:49:15 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:15 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:15 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:15 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:15 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:15 Europe/Rome] URL richiesto: img/logo.png
[14-Dec-2024 17:49:15 Europe/Rome] Dispatching URL: img/logo.png [GET]
[14-Dec-2024 17:49:15 Europe/Rome] Matching URL: img/logo.png
[14-Dec-2024 17:49:15 Europe/Rome] No route matched for URL: img/logo.png
[14-Dec-2024 17:49:15 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:22 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:22 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:22 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:22 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:22 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:22 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:22 Europe/Rome] Route home definita
[14-Dec-2024 17:49:22 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:22 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:22 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:22 Europe/Rome] URL richiesto: clienti
[14-Dec-2024 17:49:22 Europe/Rome] Dispatching URL: clienti [GET]
[14-Dec-2024 17:49:22 Europe/Rome] Matching URL: clienti
[14-Dec-2024 17:49:22 Europe/Rome] Route matched: /^clienti$/i
[14-Dec-2024 17:49:22 Europe/Rome] Matches: Array
(
    [0] => clienti
)

[14-Dec-2024 17:49:22 Europe/Rome] Route params: Array
(
)

[14-Dec-2024 17:49:22 Europe/Rome] Controller: ClientiController
[14-Dec-2024 17:49:22 Europe/Rome] Action: index
[14-Dec-2024 17:49:22 Europe/Rome] Params: Array
(
)

[14-Dec-2024 17:49:22 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:49:22 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:49:22 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:22 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:22 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:22 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:22 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:22 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:22 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:22 Europe/Rome] Route home definita
[14-Dec-2024 17:49:22 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:22 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:22 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:22 Europe/Rome] URL richiesto: assets/css/style.css
[14-Dec-2024 17:49:22 Europe/Rome] Dispatching URL: assets/css/style.css [GET]
[14-Dec-2024 17:49:22 Europe/Rome] Matching URL: assets/css/style.css
[14-Dec-2024 17:49:22 Europe/Rome] No route matched for URL: assets/css/style.css
[14-Dec-2024 17:49:22 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:22 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:22 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:22 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:22 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:22 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:22 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:22 Europe/Rome] Route home definita
[14-Dec-2024 17:49:22 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:22 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:22 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:22 Europe/Rome] URL richiesto: assets/css/management.css
[14-Dec-2024 17:49:22 Europe/Rome] Dispatching URL: assets/css/management.css [GET]
[14-Dec-2024 17:49:22 Europe/Rome] Matching URL: assets/css/management.css
[14-Dec-2024 17:49:22 Europe/Rome] No route matched for URL: assets/css/management.css
[14-Dec-2024 17:49:22 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:22 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:22 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:22 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:22 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:22 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:22 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:22 Europe/Rome] Route home definita
[14-Dec-2024 17:49:22 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:22 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:22 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:22 Europe/Rome] URL richiesto: assets/css/navbar.css
[14-Dec-2024 17:49:22 Europe/Rome] Dispatching URL: assets/css/navbar.css [GET]
[14-Dec-2024 17:49:22 Europe/Rome] Matching URL: assets/css/navbar.css
[14-Dec-2024 17:49:22 Europe/Rome] No route matched for URL: assets/css/navbar.css
[14-Dec-2024 17:49:22 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:22 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:22 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:22 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:22 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:22 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:22 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:22 Europe/Rome] Route home definita
[14-Dec-2024 17:49:22 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:22 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:22 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:22 Europe/Rome] URL richiesto: assets/css/theme.css
[14-Dec-2024 17:49:22 Europe/Rome] Dispatching URL: assets/css/theme.css [GET]
[14-Dec-2024 17:49:22 Europe/Rome] Matching URL: assets/css/theme.css
[14-Dec-2024 17:49:22 Europe/Rome] No route matched for URL: assets/css/theme.css
[14-Dec-2024 17:49:22 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:22 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:22 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:22 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:22 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:22 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:22 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:22 Europe/Rome] Route home definita
[14-Dec-2024 17:49:22 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:22 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:22 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:22 Europe/Rome] URL richiesto: img/logo.png
[14-Dec-2024 17:49:22 Europe/Rome] Dispatching URL: img/logo.png [GET]
[14-Dec-2024 17:49:22 Europe/Rome] Matching URL: img/logo.png
[14-Dec-2024 17:49:22 Europe/Rome] No route matched for URL: img/logo.png
[14-Dec-2024 17:49:22 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:22 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:22 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:22 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:22 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:22 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:22 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:22 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:22 Europe/Rome] Route home definita
[14-Dec-2024 17:49:22 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:22 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:22 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:22 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:22 Europe/Rome] URL richiesto: assets/css/management.css
[14-Dec-2024 17:49:22 Europe/Rome] Dispatching URL: assets/css/management.css [GET]
[14-Dec-2024 17:49:22 Europe/Rome] Matching URL: assets/css/management.css
[14-Dec-2024 17:49:22 Europe/Rome] No route matched for URL: assets/css/management.css
[14-Dec-2024 17:49:22 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:24 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:24 Europe/Rome] Route home definita
[14-Dec-2024 17:49:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:24 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:24 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:49:24 Europe/Rome] Dispatching URL: clienti/dettagli/1 [GET]
[14-Dec-2024 17:49:24 Europe/Rome] Matching URL: clienti/dettagli/1
[14-Dec-2024 17:49:24 Europe/Rome] Route matched: /^clienti\/dettagli\/(?P<id>[0-9]+)$/i
[14-Dec-2024 17:49:24 Europe/Rome] Matches: Array
(
    [0] => clienti/dettagli/1
    [id] => 1
    [1] => 1
)

[14-Dec-2024 17:49:24 Europe/Rome] Route params: Array
(
    [0] => 1
)

[14-Dec-2024 17:49:24 Europe/Rome] Controller: ClientiController
[14-Dec-2024 17:49:24 Europe/Rome] Action: dettagli
[14-Dec-2024 17:49:24 Europe/Rome] Params: Array
(
    [0] => 1
)

[14-Dec-2024 17:49:24 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:49:24 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:49:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:24 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:24 Europe/Rome] Route home definita
[14-Dec-2024 17:49:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:24 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:24 Europe/Rome] URL richiesto: clienti
[14-Dec-2024 17:49:24 Europe/Rome] Dispatching URL: clienti [GET]
[14-Dec-2024 17:49:24 Europe/Rome] Matching URL: clienti
[14-Dec-2024 17:49:24 Europe/Rome] Route matched: /^clienti$/i
[14-Dec-2024 17:49:24 Europe/Rome] Matches: Array
(
    [0] => clienti
)

[14-Dec-2024 17:49:24 Europe/Rome] Route params: Array
(
)

[14-Dec-2024 17:49:24 Europe/Rome] Controller: ClientiController
[14-Dec-2024 17:49:24 Europe/Rome] Action: index
[14-Dec-2024 17:49:24 Europe/Rome] Params: Array
(
)

[14-Dec-2024 17:49:24 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:49:24 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:49:24 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:24 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:24 Europe/Rome] Route home definita
[14-Dec-2024 17:49:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:24 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:24 Europe/Rome] URL richiesto: assets/css/style.css
[14-Dec-2024 17:49:24 Europe/Rome] Dispatching URL: assets/css/style.css [GET]
[14-Dec-2024 17:49:24 Europe/Rome] Matching URL: assets/css/style.css
[14-Dec-2024 17:49:24 Europe/Rome] No route matched for URL: assets/css/style.css
[14-Dec-2024 17:49:24 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:24 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:24 Europe/Rome] Route home definita
[14-Dec-2024 17:49:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:24 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:24 Europe/Rome] URL richiesto: assets/css/theme.css
[14-Dec-2024 17:49:24 Europe/Rome] Dispatching URL: assets/css/theme.css [GET]
[14-Dec-2024 17:49:24 Europe/Rome] Matching URL: assets/css/theme.css
[14-Dec-2024 17:49:24 Europe/Rome] No route matched for URL: assets/css/theme.css
[14-Dec-2024 17:49:24 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:24 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:24 Europe/Rome] Route home definita
[14-Dec-2024 17:49:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:24 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:24 Europe/Rome] URL richiesto: assets/css/navbar.css
[14-Dec-2024 17:49:24 Europe/Rome] Dispatching URL: assets/css/navbar.css [GET]
[14-Dec-2024 17:49:24 Europe/Rome] Matching URL: assets/css/navbar.css
[14-Dec-2024 17:49:24 Europe/Rome] No route matched for URL: assets/css/navbar.css
[14-Dec-2024 17:49:24 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:24 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:24 Europe/Rome] Route home definita
[14-Dec-2024 17:49:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:24 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:24 Europe/Rome] URL richiesto: assets/css/management.css
[14-Dec-2024 17:49:24 Europe/Rome] Dispatching URL: assets/css/management.css [GET]
[14-Dec-2024 17:49:24 Europe/Rome] Matching URL: assets/css/management.css
[14-Dec-2024 17:49:24 Europe/Rome] No route matched for URL: assets/css/management.css
[14-Dec-2024 17:49:24 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:24 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:24 Europe/Rome] Route home definita
[14-Dec-2024 17:49:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:24 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:24 Europe/Rome] URL richiesto: img/logo.png
[14-Dec-2024 17:49:24 Europe/Rome] Dispatching URL: img/logo.png [GET]
[14-Dec-2024 17:49:24 Europe/Rome] Matching URL: img/logo.png
[14-Dec-2024 17:49:24 Europe/Rome] No route matched for URL: img/logo.png
[14-Dec-2024 17:49:24 Europe/Rome] Route dispatched
[14-Dec-2024 17:49:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:49:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:49:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:49:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:49:24 Europe/Rome] Router inizializzato
[14-Dec-2024 17:49:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:49:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:49:24 Europe/Rome] Route home definita
[14-Dec-2024 17:49:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:49:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:49:24 Europe/Rome] Route progetti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route clienti definite
[14-Dec-2024 17:49:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:49:24 Europe/Rome] URL richiesto: assets/css/management.css
[14-Dec-2024 17:49:24 Europe/Rome] Dispatching URL: assets/css/management.css [GET]
[14-Dec-2024 17:49:24 Europe/Rome] Matching URL: assets/css/management.css
[14-Dec-2024 17:49:24 Europe/Rome] No route matched for URL: assets/css/management.css
[14-Dec-2024 17:49:24 Europe/Rome] Route dispatched
[14-Dec-2024 17:52:48 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:52:48 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:52:48 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:52:48 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:52:48 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:52:48 Europe/Rome] Router inizializzato
[14-Dec-2024 17:52:48 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:52:48 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:52:48 Europe/Rome] Route home definita
[14-Dec-2024 17:52:48 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:52:48 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:52:48 Europe/Rome] Route progetti definite
[14-Dec-2024 17:52:48 Europe/Rome] Route clienti definite
[14-Dec-2024 17:52:48 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:52:48 Europe/Rome] URL richiesto: 
[14-Dec-2024 17:52:48 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:52:48 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:52:48 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:52:48 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:52:48 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:52:48 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:52:48 Europe/Rome] Router inizializzato
[14-Dec-2024 17:52:48 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:52:48 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:52:48 Europe/Rome] Route home definita
[14-Dec-2024 17:52:48 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:52:48 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:52:48 Europe/Rome] Route progetti definite
[14-Dec-2024 17:52:48 Europe/Rome] Route clienti definite
[14-Dec-2024 17:52:48 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:52:48 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 17:52:48 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:52:48 Europe/Rome] 
==================================================
[14-Dec-2024 17:52:48 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 17:52:48 Europe/Rome] ==================================================

[14-Dec-2024 17:52:48 Europe/Rome] Clienti totali: 2
[14-Dec-2024 17:52:48 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 17:52:48 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 17:52:48 Europe/Rome] Progetti totali: 1
[14-Dec-2024 17:52:48 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 17:52:48 Europe/Rome] 
==================================================
[14-Dec-2024 17:52:48 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 17:52:48 Europe/Rome] ==================================================

[14-Dec-2024 17:52:48 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:52:48 Europe/Rome] Route dispatched
[14-Dec-2024 17:52:52 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:52:52 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:52:52 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:52:52 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:52:52 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:52:52 Europe/Rome] Router inizializzato
[14-Dec-2024 17:52:52 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:52:52 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:52:52 Europe/Rome] Route home definita
[14-Dec-2024 17:52:52 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:52:52 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:52:52 Europe/Rome] Route progetti definite
[14-Dec-2024 17:52:52 Europe/Rome] Route clienti definite
[14-Dec-2024 17:52:52 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:52:52 Europe/Rome] URL richiesto: logout
[14-Dec-2024 17:52:52 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:52:52 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:52:52 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:52:52 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:52:52 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:52:52 Europe/Rome] Router inizializzato
[14-Dec-2024 17:52:52 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:52:52 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:52:52 Europe/Rome] Route home definita
[14-Dec-2024 17:52:52 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:52:52 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:52:52 Europe/Rome] Route progetti definite
[14-Dec-2024 17:52:52 Europe/Rome] Route clienti definite
[14-Dec-2024 17:52:52 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:52:52 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:52:52 Europe/Rome] Route dispatched
[14-Dec-2024 17:52:56 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:52:56 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:52:56 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:52:56 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:52:56 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:52:56 Europe/Rome] Router inizializzato
[14-Dec-2024 17:52:56 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:52:56 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:52:56 Europe/Rome] Route home definita
[14-Dec-2024 17:52:56 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:52:56 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:52:56 Europe/Rome] Route progetti definite
[14-Dec-2024 17:52:56 Europe/Rome] Route clienti definite
[14-Dec-2024 17:52:56 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:52:56 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:52:56 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 17:52:56 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 17:52:56 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 17:52:56 Europe/Rome] Password verificata con successo
[14-Dec-2024 17:52:56 Europe/Rome] Login riuscito per l'utente: Mauro con ruolo: user
[14-Dec-2024 17:52:56 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:52:56 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:52:56 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:52:56 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:52:56 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:52:56 Europe/Rome] Router inizializzato
[14-Dec-2024 17:52:56 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:52:56 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:52:56 Europe/Rome] Route home definita
[14-Dec-2024 17:52:56 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:52:56 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:52:56 Europe/Rome] Route progetti definite
[14-Dec-2024 17:52:56 Europe/Rome] Route clienti definite
[14-Dec-2024 17:52:56 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:52:56 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 17:52:56 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:52:56 Europe/Rome] 
==================================================
[14-Dec-2024 17:52:56 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 17:52:56 Europe/Rome] ==================================================

[14-Dec-2024 17:52:56 Europe/Rome] Clienti totali: 2
[14-Dec-2024 17:52:56 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 17:52:56 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 17:52:56 Europe/Rome] Progetti totali: 1
[14-Dec-2024 17:52:56 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 17:52:56 Europe/Rome] 
==================================================
[14-Dec-2024 17:52:56 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 17:52:56 Europe/Rome] ==================================================

[14-Dec-2024 17:52:56 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:52:56 Europe/Rome] Route dispatched
[14-Dec-2024 17:52:58 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:52:58 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:53:00 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:53:00 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:53:00 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:53:00 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:00 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:53:00 Europe/Rome] Router inizializzato
[14-Dec-2024 17:53:00 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:53:00 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:53:00 Europe/Rome] Route home definita
[14-Dec-2024 17:53:00 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:53:00 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:53:00 Europe/Rome] Route progetti definite
[14-Dec-2024 17:53:00 Europe/Rome] Route clienti definite
[14-Dec-2024 17:53:00 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:53:00 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:53:00 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:53:00 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:53:00 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:00 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:53:07 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:53:07 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:53:07 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:53:07 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:07 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:53:07 Europe/Rome] Router inizializzato
[14-Dec-2024 17:53:07 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:53:07 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:53:07 Europe/Rome] Route home definita
[14-Dec-2024 17:53:07 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:53:07 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:53:07 Europe/Rome] Route progetti definite
[14-Dec-2024 17:53:07 Europe/Rome] Route clienti definite
[14-Dec-2024 17:53:07 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:53:07 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:53:07 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:53:07 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:53:07 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:07 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:53:10 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:53:10 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:53:10 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:53:10 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:10 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:53:10 Europe/Rome] Router inizializzato
[14-Dec-2024 17:53:10 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:53:10 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:53:10 Europe/Rome] Route home definita
[14-Dec-2024 17:53:10 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:53:10 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:53:10 Europe/Rome] Route progetti definite
[14-Dec-2024 17:53:10 Europe/Rome] Route clienti definite
[14-Dec-2024 17:53:10 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:53:10 Europe/Rome] URL richiesto: logout
[14-Dec-2024 17:53:10 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:53:10 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:53:10 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:53:10 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:10 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:53:10 Europe/Rome] Router inizializzato
[14-Dec-2024 17:53:10 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:53:10 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:53:10 Europe/Rome] Route home definita
[14-Dec-2024 17:53:10 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:53:10 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:53:10 Europe/Rome] Route progetti definite
[14-Dec-2024 17:53:10 Europe/Rome] Route clienti definite
[14-Dec-2024 17:53:10 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:53:10 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:53:10 Europe/Rome] Route dispatched
[14-Dec-2024 17:53:13 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:53:13 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:53:13 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:53:13 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:13 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:53:13 Europe/Rome] Router inizializzato
[14-Dec-2024 17:53:13 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:53:13 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:53:13 Europe/Rome] Route home definita
[14-Dec-2024 17:53:13 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:53:13 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:53:13 Europe/Rome] Route progetti definite
[14-Dec-2024 17:53:13 Europe/Rome] Route clienti definite
[14-Dec-2024 17:53:13 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:53:13 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:53:13 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 17:53:13 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 17:53:13 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 17:53:13 Europe/Rome] Password verificata con successo
[14-Dec-2024 17:53:13 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[14-Dec-2024 17:53:13 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:53:13 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:53:13 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:53:13 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:13 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:53:13 Europe/Rome] Router inizializzato
[14-Dec-2024 17:53:13 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:53:13 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:53:13 Europe/Rome] Route home definita
[14-Dec-2024 17:53:13 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:53:13 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:53:13 Europe/Rome] Route progetti definite
[14-Dec-2024 17:53:13 Europe/Rome] Route clienti definite
[14-Dec-2024 17:53:13 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:53:13 Europe/Rome] URL richiesto: admin
[14-Dec-2024 17:53:13 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 17:53:13 Europe/Rome] Route dispatched
[14-Dec-2024 17:53:25 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:53:25 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:53:25 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:53:25 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:25 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:53:25 Europe/Rome] Router inizializzato
[14-Dec-2024 17:53:25 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:53:25 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:53:25 Europe/Rome] Route home definita
[14-Dec-2024 17:53:25 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:53:25 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:53:25 Europe/Rome] Route progetti definite
[14-Dec-2024 17:53:25 Europe/Rome] Route clienti definite
[14-Dec-2024 17:53:25 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:53:25 Europe/Rome] URL richiesto: admin/users
[14-Dec-2024 17:53:25 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 17:53:25 Europe/Rome] AdminController::users() called
[14-Dec-2024 17:53:25 Europe/Rome] Executing users query with page: 1, perPage: 10
[14-Dec-2024 17:53:25 Europe/Rome] Total users found: 2, Total pages: 1
[14-Dec-2024 17:53:25 Europe/Rome] Executing query: SELECT * FROM users  
                     ORDER BY id DESC LIMIT 10 OFFSET 0
[14-Dec-2024 17:53:25 Europe/Rome] Found 2 users
[14-Dec-2024 17:53:25 Europe/Rome] Users data: Array
(
    [0] => Array
        (
            [id] => 6
            [username] => Mauro
            [email] => 
            [password] => $2y$10$RQ704WI9LMMrkTkX4A8OjOMDm12m6ovhOVpkT1C2RRSwPwx3W/a7e
            [ruolo] => user
            [active] => 1
        )

    [1] => Array
        (
            [id] => 5
            [username] => admin
            [email] => <EMAIL>
            [password] => $2y$10$VT/tvsPz72/pHDdpDdGCieS6CqO/ouQhwAcuMHsoDZxUwR2YXHsdC
            [ruolo] => admin
            [active] => 1
        )

)

[14-Dec-2024 17:53:25 Europe/Rome] Route dispatched
[14-Dec-2024 17:53:34 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:53:34 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:53:34 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:53:34 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:34 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:53:34 Europe/Rome] Router inizializzato
[14-Dec-2024 17:53:34 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:53:34 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:53:34 Europe/Rome] Route home definita
[14-Dec-2024 17:53:34 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:53:34 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:53:34 Europe/Rome] Route progetti definite
[14-Dec-2024 17:53:34 Europe/Rome] Route clienti definite
[14-Dec-2024 17:53:34 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:53:34 Europe/Rome] URL richiesto: admin
[14-Dec-2024 17:53:34 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 17:53:34 Europe/Rome] Route dispatched
[14-Dec-2024 17:53:38 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:53:38 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:53:38 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:53:38 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:38 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:53:38 Europe/Rome] Router inizializzato
[14-Dec-2024 17:53:38 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:53:38 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:53:38 Europe/Rome] Route home definita
[14-Dec-2024 17:53:38 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:53:38 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:53:38 Europe/Rome] Route progetti definite
[14-Dec-2024 17:53:38 Europe/Rome] Route clienti definite
[14-Dec-2024 17:53:38 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:53:38 Europe/Rome] URL richiesto: 
[14-Dec-2024 17:53:38 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 17:53:38 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:53:38 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:53:38 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:53:38 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:38 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:53:38 Europe/Rome] Router inizializzato
[14-Dec-2024 17:53:38 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:53:38 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:53:38 Europe/Rome] Route home definita
[14-Dec-2024 17:53:38 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:53:38 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:53:38 Europe/Rome] Route progetti definite
[14-Dec-2024 17:53:38 Europe/Rome] Route clienti definite
[14-Dec-2024 17:53:38 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:53:38 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 17:53:38 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 17:53:38 Europe/Rome] 
==================================================
[14-Dec-2024 17:53:38 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 17:53:38 Europe/Rome] ==================================================

[14-Dec-2024 17:53:38 Europe/Rome] Clienti totali: 2
[14-Dec-2024 17:53:38 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 17:53:38 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 17:53:38 Europe/Rome] Progetti totali: 1
[14-Dec-2024 17:53:38 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 17:53:38 Europe/Rome] 
==================================================
[14-Dec-2024 17:53:38 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 17:53:38 Europe/Rome] ==================================================

[14-Dec-2024 17:53:38 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:53:38 Europe/Rome] Route dispatched
[14-Dec-2024 17:53:40 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:40 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:53:42 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:53:42 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:53:42 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:53:42 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:42 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:53:42 Europe/Rome] Router inizializzato
[14-Dec-2024 17:53:42 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:53:42 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:53:42 Europe/Rome] Route home definita
[14-Dec-2024 17:53:42 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:53:42 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:53:42 Europe/Rome] Route progetti definite
[14-Dec-2024 17:53:42 Europe/Rome] Route clienti definite
[14-Dec-2024 17:53:42 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:53:42 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:53:42 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 17:53:42 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:53:42 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:53:42 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 17:55:27 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:55:27 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:55:27 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:55:27 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:55:27 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:55:27 Europe/Rome] Router inizializzato
[14-Dec-2024 17:55:27 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:55:27 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:55:27 Europe/Rome] Route home definita
[14-Dec-2024 17:55:27 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:55:27 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:55:27 Europe/Rome] Route progetti definite
[14-Dec-2024 17:55:27 Europe/Rome] Route clienti definite
[14-Dec-2024 17:55:27 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:55:27 Europe/Rome] URL richiesto: 
[14-Dec-2024 17:55:27 Europe/Rome] Utente non autenticato, redirect al login
[14-Dec-2024 17:55:27 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:55:27 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:55:27 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:55:27 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:55:27 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:55:27 Europe/Rome] Router inizializzato
[14-Dec-2024 17:55:27 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:55:27 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:55:27 Europe/Rome] Route home definita
[14-Dec-2024 17:55:27 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:55:27 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:55:27 Europe/Rome] Route progetti definite
[14-Dec-2024 17:55:27 Europe/Rome] Route clienti definite
[14-Dec-2024 17:55:27 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:55:27 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:55:27 Europe/Rome] Route dispatched
[14-Dec-2024 17:55:31 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:55:31 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:55:31 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:55:31 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:55:31 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:55:31 Europe/Rome] Router inizializzato
[14-Dec-2024 17:55:31 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:55:31 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:55:31 Europe/Rome] Route home definita
[14-Dec-2024 17:55:31 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:55:31 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:55:31 Europe/Rome] Route progetti definite
[14-Dec-2024 17:55:31 Europe/Rome] Route clienti definite
[14-Dec-2024 17:55:31 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:55:31 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:55:31 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 17:55:31 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 17:55:31 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 17:55:31 Europe/Rome] Password verificata con successo
[14-Dec-2024 17:55:31 Europe/Rome] Login riuscito per l'utente: Mauro con ruolo: user
[14-Dec-2024 17:55:31 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:55:31 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:55:31 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:55:31 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:55:31 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:55:31 Europe/Rome] Router inizializzato
[14-Dec-2024 17:55:31 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:55:31 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:55:31 Europe/Rome] Route home definita
[14-Dec-2024 17:55:31 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:55:31 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:55:31 Europe/Rome] Route progetti definite
[14-Dec-2024 17:55:31 Europe/Rome] Route clienti definite
[14-Dec-2024 17:55:31 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:55:31 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 17:55:31 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:55:31 Europe/Rome] 
==================================================
[14-Dec-2024 17:55:31 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 17:55:31 Europe/Rome] ==================================================

[14-Dec-2024 17:55:31 Europe/Rome] Clienti totali: 2
[14-Dec-2024 17:55:31 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 17:55:31 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 17:55:31 Europe/Rome] Progetti totali: 1
[14-Dec-2024 17:55:31 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 17:55:31 Europe/Rome] 
==================================================
[14-Dec-2024 17:55:31 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 17:55:31 Europe/Rome] ==================================================

[14-Dec-2024 17:55:31 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:55:31 Europe/Rome] Route dispatched
[14-Dec-2024 17:55:35 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:55:35 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:55:37 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:55:37 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:55:37 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:55:37 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:55:37 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:55:37 Europe/Rome] Router inizializzato
[14-Dec-2024 17:55:37 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:55:37 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:55:37 Europe/Rome] Route home definita
[14-Dec-2024 17:55:37 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:55:37 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:55:37 Europe/Rome] Route progetti definite
[14-Dec-2024 17:55:37 Europe/Rome] Route clienti definite
[14-Dec-2024 17:55:37 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:55:37 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:55:37 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:55:37 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.ultimo_aggiornamento' in 'field list'
[14-Dec-2024 17:55:37 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:55:37 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:58:47 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:58:47 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:58:49 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:58:49 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:58:49 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:58:49 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:58:49 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:58:49 Europe/Rome] Router inizializzato
[14-Dec-2024 17:58:49 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:58:49 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:58:49 Europe/Rome] Route home definita
[14-Dec-2024 17:58:49 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:58:49 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:58:49 Europe/Rome] Route progetti definite
[14-Dec-2024 17:58:49 Europe/Rome] Route clienti definite
[14-Dec-2024 17:58:49 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:58:49 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 17:58:49 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 17:58:49 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.data_aggiornamento' in 'field list'
[14-Dec-2024 17:58:49 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:58:49 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 17:59:20 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:59:20 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:59:20 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:59:20 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:59:20 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:59:20 Europe/Rome] Router inizializzato
[14-Dec-2024 17:59:20 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:59:20 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:59:20 Europe/Rome] Route home definita
[14-Dec-2024 17:59:20 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:59:20 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:59:20 Europe/Rome] Route progetti definite
[14-Dec-2024 17:59:20 Europe/Rome] Route clienti definite
[14-Dec-2024 17:59:20 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:59:20 Europe/Rome] URL richiesto: logout
[14-Dec-2024 17:59:20 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:59:20 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:59:20 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:59:20 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:59:20 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:59:20 Europe/Rome] Router inizializzato
[14-Dec-2024 17:59:20 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:59:20 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:59:20 Europe/Rome] Route home definita
[14-Dec-2024 17:59:20 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:59:20 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:59:20 Europe/Rome] Route progetti definite
[14-Dec-2024 17:59:20 Europe/Rome] Route clienti definite
[14-Dec-2024 17:59:20 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:59:20 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:59:20 Europe/Rome] Route dispatched
[14-Dec-2024 17:59:25 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:59:25 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:59:25 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:59:25 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:59:25 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:59:25 Europe/Rome] Router inizializzato
[14-Dec-2024 17:59:25 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:59:25 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:59:25 Europe/Rome] Route home definita
[14-Dec-2024 17:59:25 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:59:25 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:59:25 Europe/Rome] Route progetti definite
[14-Dec-2024 17:59:25 Europe/Rome] Route clienti definite
[14-Dec-2024 17:59:25 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:59:25 Europe/Rome] URL richiesto: login
[14-Dec-2024 17:59:25 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 17:59:25 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 17:59:25 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 17:59:25 Europe/Rome] Password verificata con successo
[14-Dec-2024 17:59:25 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[14-Dec-2024 17:59:25 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 17:59:25 Europe/Rome] Autoloader registrato
[14-Dec-2024 17:59:25 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 17:59:25 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 17:59:25 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 17:59:25 Europe/Rome] Router inizializzato
[14-Dec-2024 17:59:25 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 17:59:25 Europe/Rome] Route profilo admin definite
[14-Dec-2024 17:59:25 Europe/Rome] Route home definita
[14-Dec-2024 17:59:25 Europe/Rome] Route dashboard definita
[14-Dec-2024 17:59:25 Europe/Rome] Route pratiche definite
[14-Dec-2024 17:59:25 Europe/Rome] Route progetti definite
[14-Dec-2024 17:59:25 Europe/Rome] Route clienti definite
[14-Dec-2024 17:59:25 Europe/Rome] Route scadenze definite
[14-Dec-2024 17:59:25 Europe/Rome] URL richiesto: admin
[14-Dec-2024 17:59:25 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 17:59:25 Europe/Rome] Route dispatched
[14-Dec-2024 18:00:23 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:00:23 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:00:23 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:00:23 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:00:23 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:00:23 Europe/Rome] Router inizializzato
[14-Dec-2024 18:00:23 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:00:23 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:00:23 Europe/Rome] Route home definita
[14-Dec-2024 18:00:23 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:00:23 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:00:23 Europe/Rome] Route progetti definite
[14-Dec-2024 18:00:23 Europe/Rome] Route clienti definite
[14-Dec-2024 18:00:23 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:00:23 Europe/Rome] URL richiesto: 
[14-Dec-2024 18:00:23 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:00:23 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:00:23 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:00:23 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:00:23 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:00:23 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:00:23 Europe/Rome] Router inizializzato
[14-Dec-2024 18:00:23 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:00:23 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:00:23 Europe/Rome] Route home definita
[14-Dec-2024 18:00:23 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:00:23 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:00:23 Europe/Rome] Route progetti definite
[14-Dec-2024 18:00:23 Europe/Rome] Route clienti definite
[14-Dec-2024 18:00:23 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:00:23 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 18:00:23 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:00:23 Europe/Rome] 
==================================================
[14-Dec-2024 18:00:23 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 18:00:23 Europe/Rome] ==================================================

[14-Dec-2024 18:00:23 Europe/Rome] Clienti totali: 2
[14-Dec-2024 18:00:23 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 18:00:23 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 18:00:23 Europe/Rome] Progetti totali: 1
[14-Dec-2024 18:00:23 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 18:00:23 Europe/Rome] 
==================================================
[14-Dec-2024 18:00:23 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 18:00:23 Europe/Rome] ==================================================

[14-Dec-2024 18:00:23 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:00:23 Europe/Rome] Route dispatched
[14-Dec-2024 18:00:25 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:00:25 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:00:27 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:00:27 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:00:27 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:00:27 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:00:27 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:00:27 Europe/Rome] Router inizializzato
[14-Dec-2024 18:00:27 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:00:27 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:00:27 Europe/Rome] Route home definita
[14-Dec-2024 18:00:27 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:00:27 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:00:27 Europe/Rome] Route progetti definite
[14-Dec-2024 18:00:27 Europe/Rome] Route clienti definite
[14-Dec-2024 18:00:27 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:00:27 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:00:27 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:00:27 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.nome' in 'field list'
[14-Dec-2024 18:00:27 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:00:27 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:03:52 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:03:52 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:03:52 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:03:52 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:03:52 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:03:52 Europe/Rome] Router inizializzato
[14-Dec-2024 18:03:52 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:03:52 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:03:52 Europe/Rome] Route home definita
[14-Dec-2024 18:03:52 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:03:52 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:03:52 Europe/Rome] Route progetti definite
[14-Dec-2024 18:03:52 Europe/Rome] Route clienti definite
[14-Dec-2024 18:03:52 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:03:52 Europe/Rome] URL richiesto: 
[14-Dec-2024 18:03:52 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:03:52 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:03:52 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:03:52 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:03:52 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:03:52 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:03:52 Europe/Rome] Router inizializzato
[14-Dec-2024 18:03:52 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:03:52 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:03:52 Europe/Rome] Route home definita
[14-Dec-2024 18:03:52 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:03:52 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:03:52 Europe/Rome] Route progetti definite
[14-Dec-2024 18:03:52 Europe/Rome] Route clienti definite
[14-Dec-2024 18:03:52 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:03:52 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 18:03:52 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:03:52 Europe/Rome] 
==================================================
[14-Dec-2024 18:03:52 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 18:03:52 Europe/Rome] ==================================================

[14-Dec-2024 18:03:52 Europe/Rome] Clienti totali: 2
[14-Dec-2024 18:03:52 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 18:03:52 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 18:03:52 Europe/Rome] Progetti totali: 1
[14-Dec-2024 18:03:52 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 18:03:52 Europe/Rome] 
==================================================
[14-Dec-2024 18:03:52 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 18:03:52 Europe/Rome] ==================================================

[14-Dec-2024 18:03:52 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:03:52 Europe/Rome] Route dispatched
[14-Dec-2024 18:03:54 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:03:54 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:03:56 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:03:56 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:03:56 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:03:56 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:03:56 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:03:56 Europe/Rome] Router inizializzato
[14-Dec-2024 18:03:56 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:03:56 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:03:56 Europe/Rome] Route home definita
[14-Dec-2024 18:03:56 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:03:56 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:03:56 Europe/Rome] Route progetti definite
[14-Dec-2024 18:03:56 Europe/Rome] Route clienti definite
[14-Dec-2024 18:03:56 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:03:56 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:03:56 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:03:56 Europe/Rome] Progetti trovati: Array
(
    [0] => Array
        (
            [id] => 1
            [cliente_id] => 1
            [nome_progetto] => Casa
            [descrizione] => 
            [data_inizio] => 2024-12-08
            [data_fine_prevista] => 2025-04-08
            [stato] => in_corso
            [importo] => 50000.00
            [comune] => Roma
            [indirizzo_progetto] => via della Stazione di Ciampino 151
            [tipo_progetto] => ristrutturazione
            [priorita] => media
            [num_pratiche] => 1
            [ultima_modifica] => 2024-12-08 00:00:00
        )

)

[14-Dec-2024 18:03:56 Europe/Rome] Pratiche trovate: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => 
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
        )

)

[14-Dec-2024 18:03:56 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[2024-12-14 18:03:56][🟡 WARN ][admin@::1][error_log.php:94] Undefined array key "cap"
[14-Dec-2024 18:03:56 Europe/Rome] PHP Warning:  Undefined array key "cap" in C:\xampp\htdocs\studio_tecnico\views\clienti\dettagli.php on line 41
[2024-12-14 18:03:56][🟠 ERROR][admin@::1][error_log.php:94] htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated
[14-Dec-2024 18:03:56 Europe/Rome] PHP Deprecated:  htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated in C:\xampp\htdocs\studio_tecnico\views\clienti\dettagli.php on line 41
[2024-12-14 18:03:56][🟡 WARN ][admin@::1][error_log.php:94] Undefined array key "nome"
[14-Dec-2024 18:03:56 Europe/Rome] PHP Warning:  Undefined array key "nome" in C:\xampp\htdocs\studio_tecnico\views\clienti\dettagli.php on line 75
[2024-12-14 18:03:56][🟠 ERROR][admin@::1][error_log.php:94] htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated
[14-Dec-2024 18:03:56 Europe/Rome] PHP Deprecated:  htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated in C:\xampp\htdocs\studio_tecnico\views\clienti\dettagli.php on line 75
[2024-12-14 18:03:56][🟡 WARN ][admin@::1][error_log.php:94] Undefined array key "data_creazione"
[14-Dec-2024 18:03:56 Europe/Rome] PHP Warning:  Undefined array key "data_creazione" in C:\xampp\htdocs\studio_tecnico\views\clienti\dettagli.php on line 76
[2024-12-14 18:03:56][🟠 ERROR][admin@::1][error_log.php:94] strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated
[14-Dec-2024 18:03:56 Europe/Rome] PHP Deprecated:  strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in C:\xampp\htdocs\studio_tecnico\views\clienti\dettagli.php on line 76
[2024-12-14 18:03:56][🟡 WARN ][admin@::1][error_log.php:94] Undefined array key "ultimo_aggiornamento"
[14-Dec-2024 18:03:56 Europe/Rome] PHP Warning:  Undefined array key "ultimo_aggiornamento" in C:\xampp\htdocs\studio_tecnico\views\clienti\dettagli.php on line 135
[2024-12-14 18:03:56][🟠 ERROR][admin@::1][error_log.php:94] strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated
[14-Dec-2024 18:03:56 Europe/Rome] PHP Deprecated:  strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in C:\xampp\htdocs\studio_tecnico\views\clienti\dettagli.php on line 135
[14-Dec-2024 18:03:56 Europe/Rome] Route dispatched
[14-Dec-2024 18:06:52 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:06:52 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:06:52 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:06:52 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:06:52 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:06:52 Europe/Rome] Router inizializzato
[14-Dec-2024 18:06:52 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:06:52 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:06:52 Europe/Rome] Route home definita
[14-Dec-2024 18:06:52 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:06:52 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:06:52 Europe/Rome] Route progetti definite
[14-Dec-2024 18:06:52 Europe/Rome] Route clienti definite
[14-Dec-2024 18:06:52 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:06:52 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:06:52 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:06:52 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.data_modifica' in 'field list'
[14-Dec-2024 18:06:52 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:06:52 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:06:54 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:06:54 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:06:54 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:06:55 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:06:55 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:06:55 Europe/Rome] Router inizializzato
[14-Dec-2024 18:06:55 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:06:55 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:06:55 Europe/Rome] Route home definita
[14-Dec-2024 18:06:55 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:06:55 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:06:55 Europe/Rome] Route progetti definite
[14-Dec-2024 18:06:55 Europe/Rome] Route clienti definite
[14-Dec-2024 18:06:55 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:06:55 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:06:55 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:06:55 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.data_modifica' in 'field list'
[14-Dec-2024 18:06:55 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:06:55 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:06:56 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:06:56 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:06:56 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:06:56 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:06:56 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:06:56 Europe/Rome] Router inizializzato
[14-Dec-2024 18:06:56 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:06:56 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:06:56 Europe/Rome] Route home definita
[14-Dec-2024 18:06:56 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:06:56 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:06:56 Europe/Rome] Route progetti definite
[14-Dec-2024 18:06:56 Europe/Rome] Route clienti definite
[14-Dec-2024 18:06:56 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:06:56 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:06:56 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:06:56 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.data_modifica' in 'field list'
[14-Dec-2024 18:06:56 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:06:56 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:07:02 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:07:02 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:07:02 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:07:02 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:07:02 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:07:02 Europe/Rome] Router inizializzato
[14-Dec-2024 18:07:02 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:07:02 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:07:02 Europe/Rome] Route home definita
[14-Dec-2024 18:07:02 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:07:02 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:07:02 Europe/Rome] Route progetti definite
[14-Dec-2024 18:07:02 Europe/Rome] Route clienti definite
[14-Dec-2024 18:07:02 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:07:02 Europe/Rome] URL richiesto: logout
[14-Dec-2024 18:07:02 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:07:02 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:07:02 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:07:02 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:07:02 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:07:02 Europe/Rome] Router inizializzato
[14-Dec-2024 18:07:02 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:07:02 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:07:02 Europe/Rome] Route home definita
[14-Dec-2024 18:07:02 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:07:02 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:07:02 Europe/Rome] Route progetti definite
[14-Dec-2024 18:07:02 Europe/Rome] Route clienti definite
[14-Dec-2024 18:07:02 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:07:02 Europe/Rome] URL richiesto: login
[14-Dec-2024 18:07:02 Europe/Rome] Route dispatched
[14-Dec-2024 18:07:09 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:07:09 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:07:09 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:07:09 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:07:09 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:07:09 Europe/Rome] Router inizializzato
[14-Dec-2024 18:07:09 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:07:09 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:07:09 Europe/Rome] Route home definita
[14-Dec-2024 18:07:09 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:07:09 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:07:09 Europe/Rome] Route progetti definite
[14-Dec-2024 18:07:09 Europe/Rome] Route clienti definite
[14-Dec-2024 18:07:09 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:07:09 Europe/Rome] URL richiesto: 
[14-Dec-2024 18:07:09 Europe/Rome] Utente non autenticato, redirect al login
[14-Dec-2024 18:07:09 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:07:09 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:07:09 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:07:09 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:07:09 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:07:09 Europe/Rome] Router inizializzato
[14-Dec-2024 18:07:09 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:07:09 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:07:09 Europe/Rome] Route home definita
[14-Dec-2024 18:07:09 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:07:09 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:07:09 Europe/Rome] Route progetti definite
[14-Dec-2024 18:07:09 Europe/Rome] Route clienti definite
[14-Dec-2024 18:07:09 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:07:09 Europe/Rome] URL richiesto: login
[14-Dec-2024 18:07:09 Europe/Rome] Route dispatched
[14-Dec-2024 18:07:12 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:07:12 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:07:12 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:07:12 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:07:12 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:07:12 Europe/Rome] Router inizializzato
[14-Dec-2024 18:07:12 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:07:12 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:07:12 Europe/Rome] Route home definita
[14-Dec-2024 18:07:12 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:07:12 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:07:12 Europe/Rome] Route progetti definite
[14-Dec-2024 18:07:12 Europe/Rome] Route clienti definite
[14-Dec-2024 18:07:12 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:07:12 Europe/Rome] URL richiesto: login
[14-Dec-2024 18:07:12 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 18:07:12 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 18:07:12 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 18:07:12 Europe/Rome] Password verificata con successo
[14-Dec-2024 18:07:12 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[14-Dec-2024 18:07:12 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:07:12 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:07:12 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:07:12 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:07:12 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:07:12 Europe/Rome] Router inizializzato
[14-Dec-2024 18:07:12 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:07:12 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:07:12 Europe/Rome] Route home definita
[14-Dec-2024 18:07:12 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:07:12 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:07:12 Europe/Rome] Route progetti definite
[14-Dec-2024 18:07:12 Europe/Rome] Route clienti definite
[14-Dec-2024 18:07:12 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:07:12 Europe/Rome] URL richiesto: admin
[14-Dec-2024 18:07:12 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 18:07:12 Europe/Rome] Route dispatched
[14-Dec-2024 18:07:16 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:07:16 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:07:16 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:07:16 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:07:16 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:07:16 Europe/Rome] Router inizializzato
[14-Dec-2024 18:07:16 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:07:16 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:07:16 Europe/Rome] Route home definita
[14-Dec-2024 18:07:16 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:07:16 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:07:16 Europe/Rome] Route progetti definite
[14-Dec-2024 18:07:16 Europe/Rome] Route clienti definite
[14-Dec-2024 18:07:16 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:07:16 Europe/Rome] URL richiesto: 
[14-Dec-2024 18:07:16 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:07:16 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:07:16 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:07:16 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:07:16 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:07:16 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:07:16 Europe/Rome] Router inizializzato
[14-Dec-2024 18:07:16 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:07:16 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:07:16 Europe/Rome] Route home definita
[14-Dec-2024 18:07:16 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:07:16 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:07:16 Europe/Rome] Route progetti definite
[14-Dec-2024 18:07:16 Europe/Rome] Route clienti definite
[14-Dec-2024 18:07:16 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:07:16 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 18:07:16 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:07:16 Europe/Rome] 
==================================================
[14-Dec-2024 18:07:16 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 18:07:16 Europe/Rome] ==================================================

[14-Dec-2024 18:07:16 Europe/Rome] Clienti totali: 2
[14-Dec-2024 18:07:16 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 18:07:16 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 18:07:16 Europe/Rome] Progetti totali: 1
[14-Dec-2024 18:07:16 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 18:07:16 Europe/Rome] 
==================================================
[14-Dec-2024 18:07:16 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 18:07:16 Europe/Rome] ==================================================

[14-Dec-2024 18:07:16 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:07:16 Europe/Rome] Route dispatched
[14-Dec-2024 18:07:19 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:07:19 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:07:20 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:07:20 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:07:20 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:07:20 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:07:20 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:07:20 Europe/Rome] Router inizializzato
[14-Dec-2024 18:07:20 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:07:20 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:07:20 Europe/Rome] Route home definita
[14-Dec-2024 18:07:20 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:07:20 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:07:20 Europe/Rome] Route progetti definite
[14-Dec-2024 18:07:20 Europe/Rome] Route clienti definite
[14-Dec-2024 18:07:20 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:07:20 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:07:20 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:07:20 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.data_modifica' in 'field list'
[14-Dec-2024 18:07:20 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:07:20 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:09:29 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:09:29 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:09:29 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:09:29 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:09:29 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:09:29 Europe/Rome] Router inizializzato
[14-Dec-2024 18:09:29 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:09:29 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:09:29 Europe/Rome] Route home definita
[14-Dec-2024 18:09:29 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:09:29 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:09:29 Europe/Rome] Route progetti definite
[14-Dec-2024 18:09:29 Europe/Rome] Route clienti definite
[14-Dec-2024 18:09:29 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:09:29 Europe/Rome] URL richiesto: admin
[14-Dec-2024 18:09:29 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 18:09:29 Europe/Rome] Route dispatched
[14-Dec-2024 18:09:36 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:09:36 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:09:36 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:09:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:09:36 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:09:36 Europe/Rome] Router inizializzato
[14-Dec-2024 18:09:36 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:09:36 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:09:36 Europe/Rome] Route home definita
[14-Dec-2024 18:09:36 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:09:36 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:09:36 Europe/Rome] Route progetti definite
[14-Dec-2024 18:09:36 Europe/Rome] Route clienti definite
[14-Dec-2024 18:09:36 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:09:36 Europe/Rome] URL richiesto: 
[14-Dec-2024 18:09:36 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:09:36 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:09:36 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:09:36 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:09:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:09:36 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:09:36 Europe/Rome] Router inizializzato
[14-Dec-2024 18:09:36 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:09:36 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:09:36 Europe/Rome] Route home definita
[14-Dec-2024 18:09:36 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:09:36 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:09:36 Europe/Rome] Route progetti definite
[14-Dec-2024 18:09:36 Europe/Rome] Route clienti definite
[14-Dec-2024 18:09:36 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:09:36 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 18:09:36 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:09:36 Europe/Rome] 
==================================================
[14-Dec-2024 18:09:36 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 18:09:36 Europe/Rome] ==================================================

[14-Dec-2024 18:09:36 Europe/Rome] Clienti totali: 2
[14-Dec-2024 18:09:36 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 18:09:36 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 18:09:36 Europe/Rome] Progetti totali: 1
[14-Dec-2024 18:09:36 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 18:09:36 Europe/Rome] 
==================================================
[14-Dec-2024 18:09:36 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 18:09:36 Europe/Rome] ==================================================

[14-Dec-2024 18:09:36 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:09:36 Europe/Rome] Route dispatched
[14-Dec-2024 18:09:39 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:09:39 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:09:40 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:09:40 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:09:40 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:09:40 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:09:40 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:09:40 Europe/Rome] Router inizializzato
[14-Dec-2024 18:09:40 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:09:40 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:09:40 Europe/Rome] Route home definita
[14-Dec-2024 18:09:40 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:09:40 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:09:40 Europe/Rome] Route progetti definite
[14-Dec-2024 18:09:40 Europe/Rome] Route clienti definite
[14-Dec-2024 18:09:40 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:09:40 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:09:40 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:09:40 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cap' in 'field list'
[14-Dec-2024 18:09:40 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:09:40 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

