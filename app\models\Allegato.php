<?php

namespace App\Models;

use App\Config\Database;
use PDO;

class Allegato {
    private $db;
    private $table = 'allegati';

    public function __construct() {
        $this->db = Database::getInstance();
    }

    public function create($data) {
        $sql = "INSERT INTO {$this->table} (
            pratica_id, nome_file, percorso_file, tipo_file, 
            dimensione, data_caricamento, descrizione, categoria, utente_id
        ) VALUES (
            :pratica_id, :nome_file, :percorso_file, :tipo_file,
            :dimensione, :data_caricamento, :descrizione, :categoria, :utente_id
        )";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($data);
    }

    public function findByPraticaId($praticaId) {
        $sql = "SELECT * FROM {$this->table} WHERE pratica_id = ? ORDER BY data_caricamento DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$praticaId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function findById($id) {
        $sql = "SELECT * FROM {$this->table} WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function delete($id) {
        $allegato = $this->findById($id);
        if ($allegato && file_exists($allegato['percorso_file'])) {
            unlink($allegato['percorso_file']);
        }

        $sql = "DELETE FROM {$this->table} WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$id]);
    }

    public function deleteByPraticaId($praticaId) {
        $allegati = $this->findByPraticaId($praticaId);
        foreach ($allegati as $allegato) {
            if (file_exists($allegato['percorso_file'])) {
                unlink($allegato['percorso_file']);
            }
        }

        $sql = "DELETE FROM {$this->table} WHERE pratica_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$praticaId]);
    }

    public function updateDescrizione($id, $descrizione) {
        $sql = "UPDATE {$this->table} SET descrizione = ? WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$descrizione, $id]);
    }

    public function getCategorie() {
        return [
            'generale' => 'Generale',
            'documento_identita' => 'Documento di Identità',
            'planimetria' => 'Planimetria',
            'foto' => 'Fotografie',
            'autorizzazione' => 'Autorizzazioni',
            'contratto' => 'Contratti',
            'fattura' => 'Fatture',
            'altro' => 'Altro'
        ];
    }
}
