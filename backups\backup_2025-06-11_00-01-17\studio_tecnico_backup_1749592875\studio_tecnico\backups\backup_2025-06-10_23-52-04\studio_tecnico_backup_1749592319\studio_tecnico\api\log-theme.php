<?php
header('Content-Type: application/json');

// Abilita CORS per le richieste dalla stessa origine
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Se è una richiesta OPTIONS, termina qui
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Verifica che sia una richiesta POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Leggi il corpo della richiesta JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['message']) || !isset($input['timestamp'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid input']);
    exit;
}

// Prepara il messaggio di log
$logMessage = sprintf(
    "[%s] %s\n",
    $input['timestamp'],
    $input['message']
);

// Percorso del file di log
$logFile = dirname(__DIR__) . '/logs/theme.log';

// Scrivi nel file di log
if (file_put_contents($logFile, $logMessage, FILE_APPEND) === false) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to write to log file']);
    exit;
}

// Risposta di successo
echo json_encode(['success' => true]);
