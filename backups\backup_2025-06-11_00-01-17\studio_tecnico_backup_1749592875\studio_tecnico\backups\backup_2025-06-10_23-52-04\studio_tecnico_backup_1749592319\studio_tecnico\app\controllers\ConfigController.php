<?php
namespace App\Controllers;

use PDO;
use PDOException;

class ConfigController {
    private $db;
    private $configFile;

    public function __construct(PDO $db) {
        $this->db = $db;
        $this->configFile = ROOT_PATH . '/config/app.php';
        
        // Verifica se l'utente è admin
        if (!isset($_SESSION['user']) || !isset($_SESSION['user']['role']) || $_SESSION['user']['role'] !== 'admin') {
            $_SESSION['error'] = "Accesso negato. È richiesto il ruolo di amministratore.";
            header('Location: ' . BASE_URL . 'login');
            exit;
        }
    }

    public function index() {
        // Carica la configurazione corrente
        $config = require $this->configFile;
        
        // Assicurati che tutte le chiavi necessarie esistano
        $config = array_merge([
            'app_name' => 'Studio Tecnico',
            'timezone' => 'Europe/Rome',
            'debug' => false,
            'mail' => [
                'host' => '',
                'port' => '',
                'encryption' => 'tls',
                'username' => '',
                'password' => '',
                'from_address' => '',
                'from_name' => ''
            ]
        ], $config);

        // Carica la vista
        include VIEWS_DIR . '/admin/config.php';
    }

    public function saveConfig() {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new \Exception('Metodo non consentito');
            }

            // Validazione dei dati
            $appName = filter_input(INPUT_POST, 'app_name', FILTER_SANITIZE_SPECIAL_CHARS) ?: 'Studio Tecnico';
            $timezone = filter_input(INPUT_POST, 'timezone', FILTER_SANITIZE_SPECIAL_CHARS) ?: 'Europe/Rome';
            $debug = isset($_POST['debug']);

            // Configurazione email
            $mailConfig = [
                'host' => filter_input(INPUT_POST, 'mail_host', FILTER_SANITIZE_SPECIAL_CHARS),
                'port' => filter_input(INPUT_POST, 'mail_port', FILTER_VALIDATE_INT),
                'encryption' => filter_input(INPUT_POST, 'mail_encryption', FILTER_SANITIZE_SPECIAL_CHARS),
                'username' => filter_input(INPUT_POST, 'mail_username', FILTER_SANITIZE_SPECIAL_CHARS),
                'password' => $_POST['mail_password'] ?? '', // Non filtrare la password per permettere caratteri speciali
                'from_address' => filter_input(INPUT_POST, 'mail_from_address', FILTER_VALIDATE_EMAIL),
                'from_name' => filter_input(INPUT_POST, 'mail_from_name', FILTER_SANITIZE_SPECIAL_CHARS)
            ];

            // Creazione array configurazione
            $config = [
                'app_name' => $appName,
                'timezone' => $timezone,
                'debug' => $debug,
                'mail' => $mailConfig
            ];

            // Salvataggio della configurazione
            $success = $this->writeConfig($config);

            if ($success) {
                $_SESSION['success'] = 'Configurazione salvata con successo';
            } else {
                throw new \Exception('Errore durante il salvataggio della configurazione');
            }

            header('Location: ' . BASE_URL . 'admin/config');
            exit;

        } catch (\Exception $e) {
            error_log('Errore nel salvataggio della configurazione: ' . $e->getMessage());
            $_SESSION['error'] = $e->getMessage();
            header('Location: ' . BASE_URL . 'admin/config');
            exit;
        }
    }

    private function writeConfig($config) {
        try {
            $content = "<?php\nreturn " . var_export($config, true) . ";\n";
            return file_put_contents($this->configFile, $content) !== false;
        } catch (\Exception $e) {
            error_log('Errore nella scrittura del file di configurazione: ' . $e->getMessage());
            return false;
        }
    }
}
