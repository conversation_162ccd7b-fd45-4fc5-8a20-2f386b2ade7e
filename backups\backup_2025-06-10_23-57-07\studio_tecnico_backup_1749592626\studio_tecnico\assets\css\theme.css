/* Theme professionale con gradazioni di grigio */
:root {
    /* Colori principali */
    --primary-color: #2c3e50;
    --secondary-color: #95a5a6;
    
    /* Sfondo e superfici */
    --background-color: #f5f6fa;
    --surface-color: #ffffff;
    --surface-color-alt: #f8f9fb;
    
    /* Testo */
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    
    /* Bordi e divisori */
    --border-color: #e9ecef;
    --divider-color: #dee2e6;
    
    /* Hover e focus */
    --hover-color: #3498db;
    --focus-color: #2980b9;
    
    /* Ombre */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.07);
    --shadow-lg: 0 8px 15px rgba(0,0,0,0.1);
}

/* Stili generali */
body {
    background: linear-gradient(135deg, #f5f6fa 0%, #e9ecef 100%) fixed;
    color: var(--text-primary);
    min-height: 100vh;
}

/* Card e contenitori */
.card {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

/* Tabelle */
.table {
    background-color: var(--surface-color);
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--surface-color-alt);
    color: var(--text-primary);
    font-weight: 600;
    border-bottom: 2px solid var(--border-color);
}

.table tbody tr:hover {
    background-color: var(--surface-color-alt);
}

/* Bottoni */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--hover-color);
    border-color: var(--hover-color);
}

/* Form */
.form-control {
    border: 1px solid var(--border-color);
    background-color: var(--surface-color);
}

.form-control:focus {
    border-color: var(--focus-color);
    box-shadow: 0 0 0 0.2rem rgba(41, 128, 185, 0.15);
}

/* Dashboard wrapper */
.dashboard-wrapper {
    background: linear-gradient(135deg, var(--background-color) 0%, #ffffff 100%);
    min-height: calc(100vh - 60px);
    padding: 2rem;
}

/* Statistiche cards */
.stat-card {
    background-color: var(--surface-color);
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

/* Tabella card */
.table-card {
    background-color: var(--surface-color);
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
}

/* Page header */
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.page-title {
    color: var(--text-primary);
    font-weight: 600;
}
