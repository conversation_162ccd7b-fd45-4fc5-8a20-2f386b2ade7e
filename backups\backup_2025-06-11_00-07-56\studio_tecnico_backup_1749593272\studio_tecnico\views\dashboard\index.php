<?php
$pageTitle = "Dashboard";
include VIEWS_DIR . '/layouts/header.php';
?>

<style>
.dashboard-wrapper {
    padding: 2rem;
    background: #f8f9fa;
    min-height: calc(100vh - 60px);
}

.dashboard-card {
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    position: relative;
    border: 1px solid #e9ecef;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
}

.card-header {
    background: transparent;
    border: none;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.dashboard-card:hover .card-icon {
    transform: scale(1.1);
}

.clienti .card-icon {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    color: white;
}

.pratiche .card-icon {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

.progetti .card-icon {
    background: linear-gradient(135deg, #868e96 0%, #6c757d 100%);
    color: white;
}

.scadenze .card-icon {
    background: linear-gradient(135deg, #adb5bd 0%, #868e96 100%);
    color: white;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.card-stats {
    font-size: 2rem;
    font-weight: 700;
    color: #212529;
    margin: 1rem 0;
}

.card-link {
    color: #6c757d;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.card-link:hover {
    color: #495057;
    transform: translateX(5px);
}

.card-link i {
    margin-left: 0.5rem;
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.card-link:hover i {
    transform: translateX(3px);
}

.alert {
    background-color: #ffffff;
    border-color: #e9ecef;
    color: #495057;
}

.alert i {
    color: #007bff;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.5s ease forwards;
}

.dashboard-card {
    animation: fadeInUp 0.5s ease forwards;
    animation-delay: calc(var(--animation-order) * 0.1s);
    opacity: 0;
}
</style>

<div class="dashboard-wrapper">
    <?php if (!$hasStats): ?>
    <div class="alert alert-info d-flex align-items-center animate-fadeInUp" role="alert">
        <i class="fas fa-info-circle me-2"></i>
        <div>
            Dati statistici non disponibili al momento. Verifica la connessione al database o contatta l'amministratore.
        </div>
    </div>
    <?php endif; ?>

    <div class="row g-4">
        <!-- Card Clienti -->
        <div class="col-12 col-sm-6 col-xl-3" style="--animation-order: 1;">
            <div class="dashboard-card clienti">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="card-title">Clienti Totali</h3>
                </div>
                <div class="card-body">
                    <div class="card-stats"><?= number_format($stats['total_clients']) ?></div>
                    <a href="<?= BASE_URL ?>clienti" class="card-link">
                        Gestisci Clienti <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Card Progetti -->
        <div class="col-12 col-sm-6 col-xl-3" style="--animation-order: 3;">
            <div class="dashboard-card progetti">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3 class="card-title">Progetti Totali</h3>
                </div>
                <div class="card-body">
                    <div class="card-stats"><?= number_format($stats['total_projects']) ?></div>
                    <a href="<?= BASE_URL ?>progetti" class="card-link">
                        Gestisci Progetti <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Card Pratiche Attive -->
        <div class="col-12 col-sm-6 col-xl-3" style="--animation-order: 2;">
            <div class="dashboard-card pratiche">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3 class="card-title">Pratiche Attive</h3>
                </div>
                <div class="card-body">
                    <div class="card-stats"><?= number_format($stats['active_practices']) ?></div>
                    <a href="<?= BASE_URL ?>pratiche" class="card-link">
                        Gestisci Pratiche <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Card Scadenze -->
        <div class="col-12 col-sm-6 col-xl-3" style="--animation-order: 4;">
            <div class="dashboard-card scadenze">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="card-title">Scadenze in Arrivo</h3>
                </div>
                <div class="card-body">
                    <div class="card-stats"><?= number_format($stats['pending_deadlines']) ?></div>
                    <a href="<?= BASE_URL ?>pratiche?filter=scadenze" class="card-link">
                        Vedi Scadenze <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($hasStats): ?>
    <!-- Sezione Grafici -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="dashboard-card" style="--animation-order: 5;">
                <div class="card-header">
                    <h3 class="card-title">Andamento Attività</h3>
                </div>
                <div class="card-body">
                    <!-- Qui puoi inserire i grafici se necessario -->
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>
