/* Theme Variables */
:root,
[data-bs-theme="light"] {
    /* Colors */
    --bg-primary: #f5f5f5;
    --bg-secondary: #e9ecef;
    --text-primary: #333333;
    --text-secondary: #666666;
    --border-color: #dee2e6;
    --link-color: #0d6efd;
    --link-hover: #0a58ca;

    /* Components */
    --nav-bg: #ffffff;
    --nav-text: #333333;
    --card-bg: #ffffff;
    --table-stripe: #f8f9fa;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --input-focus-shadow: rgba(13, 110, 253, 0.25);
    
    /* Alerts */
    --alert-danger-bg: rgba(220, 53, 69, 0.15);
    --alert-danger-text: #dc3545;
    --alert-success-bg: rgba(25, 135, 84, 0.15);
    --alert-success-text: #198754;
    --alert-warning-bg: rgba(255, 193, 7, 0.15);
    --alert-warning-text: #ffc107;
    --alert-info-bg: rgba(13, 202, 240, 0.15);
    --alert-info-text: #0dcaf0;
}

[data-bs-theme="dark"] {
    /* Colors */
    --bg-primary: #1a1d20;
    --bg-secondary: #2d3238;
    --text-primary: #e9ecef;
    --text-secondary: #adb5bd;
    --border-color: #495057;
    --link-color: #6ea8fe;
    --link-hover: #9ec5fe;

    /* Components */
    --nav-bg: #212529;
    --nav-text: #e9ecef;
    --card-bg: #2d3238;
    --table-stripe: #2d3238;
    --input-bg: #2d3238;
    --input-border: #495057;
    --input-focus-shadow: rgba(110, 168, 254, 0.25);
    
    /* Alerts */
    --alert-danger-bg: rgba(220, 53, 69, 0.15);
    --alert-danger-text: #ff6b6b;
    --alert-success-bg: rgba(25, 135, 84, 0.15);
    --alert-success-text: #69db7c;
    --alert-warning-bg: rgba(255, 193, 7, 0.15);
    --alert-warning-text: #ffd43b;
    --alert-info-bg: rgba(13, 202, 240, 0.15);
    --alert-info-text: #66d9e8;
}

/* Global Styles */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Navbar */
.navbar {
    background-color: var(--nav-bg) !important;
    border-bottom: 1px solid var(--border-color);
}

.navbar-brand, 
.nav-link,
.navbar .btn-link {
    color: var(--nav-text) !important;
    transition: color 0.2s ease;
}

.navbar .btn-outline-primary {
    color: var(--nav-text) !important;
    border-color: var(--border-color) !important;
    background-color: transparent !important;
    transition: all 0.2s ease;
}

.navbar .btn-outline-primary:hover {
    color: var(--text-primary) !important;
    background-color: var(--bg-secondary) !important;
    border-color: var(--link-color) !important;
}

.navbar-toggler {
    border-color: var(--border-color) !important;
}

.navbar-toggler-icon::before {
    content: '\f0c9';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--nav-text);
}

.navbar .dropdown-menu {
    background-color: var(--nav-bg) !important;
    border-color: var(--border-color) !important;
}

.navbar .dropdown-item {
    color: var(--nav-text) !important;
    transition: all 0.2s ease;
}

.navbar .dropdown-item:hover,
.navbar .dropdown-item:focus,
.nav-link:hover,
.navbar .btn-link:hover {
    color: var(--link-hover) !important;
    background-color: var(--bg-secondary) !important;
}

/* Forms */
.form-control, 
.form-select,
textarea.form-control {
    background-color: var(--input-bg) !important;
    border-color: var(--input-border) !important;
    color: var(--text-primary) !important;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus, 
.form-select:focus {
    background-color: var(--input-bg) !important;
    border-color: var(--link-color) !important;
    color: var(--text-primary) !important;
    box-shadow: 0 0 0 0.25rem var(--input-focus-shadow) !important;
}

.form-label {
    color: var(--text-primary) !important;
}

.form-text {
    color: var(--text-secondary) !important;
}

/* Cards */
.card {
    background-color: var(--card-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
    transition: all 0.3s ease;
}

.card-header,
.card-footer {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

/* Tables */
.table {
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--table-stripe) !important;
}

/* DataTables */
.dataTables_wrapper .dataTables_length, 
.dataTables_wrapper .dataTables_filter, 
.dataTables_wrapper .dataTables_info, 
.dataTables_wrapper .dataTables_processing, 
.dataTables_wrapper .dataTables_paginate {
    color: var(--text-primary) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    color: var(--text-primary) !important;
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    color: var(--text-primary) !important;
    background-color: var(--link-color) !important;
    border-color: var(--link-color) !important;
}

/* Select2 */
.select2-container--bootstrap-5 .select2-selection {
    background-color: var(--input-bg) !important;
    border-color: var(--input-border) !important;
    color: var(--text-primary) !important;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    color: var(--text-primary) !important;
}

.select2-container--bootstrap-5 .select2-dropdown {
    background-color: var(--input-bg) !important;
    border-color: var(--input-border) !important;
}

.select2-container--bootstrap-5 .select2-results__option {
    color: var(--text-primary) !important;
}

.select2-container--bootstrap-5 .select2-results__option--highlighted {
    background-color: var(--link-color) !important;
    color: #ffffff !important;
}

/* Alerts */
.alert {
    border-width: 1px;
    border-style: solid;
}

.alert-danger {
    background-color: var(--alert-danger-bg) !important;
    color: var(--alert-danger-text) !important;
    border-color: var(--alert-danger-text) !important;
}

.alert-success {
    background-color: var(--alert-success-bg) !important;
    color: var(--alert-success-text) !important;
    border-color: var(--alert-success-text) !important;
}

.alert-warning {
    background-color: var(--alert-warning-bg) !important;
    color: var(--alert-warning-text) !important;
    border-color: var(--alert-warning-text) !important;
}

.alert-info {
    background-color: var(--alert-info-bg) !important;
    color: var(--alert-info-text) !important;
    border-color: var(--alert-info-text) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--link-color) !important;
    border-color: var(--link-color) !important;
    color: #ffffff !important;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background-color: var(--link-hover) !important;
    border-color: var(--link-hover) !important;
}

/* Flatpickr */
.flatpickr-calendar {
    background-color: var(--card-bg) !important;
    border-color: var(--border-color) !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) !important;
}

.flatpickr-day {
    color: var(--text-primary) !important;
}

.flatpickr-day.selected {
    background-color: var(--link-color) !important;
    border-color: var(--link-color) !important;
    color: #ffffff !important;
}

.flatpickr-months .flatpickr-month,
.flatpickr-weekdays {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
}

.flatpickr-current-month .flatpickr-monthDropdown-months,
.flatpickr-current-month input.cur-year {
    color: var(--text-primary) !important;
}
