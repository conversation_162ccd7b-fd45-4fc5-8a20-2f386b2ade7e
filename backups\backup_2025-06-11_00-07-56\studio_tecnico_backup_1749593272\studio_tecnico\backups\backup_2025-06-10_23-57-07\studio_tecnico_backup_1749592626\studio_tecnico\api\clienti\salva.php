<?php
require_once '../config.php';
requirePost();

try {
    // Validazione dei campi obbligatori
    $campi_obbligatori = ['tipo_cliente', 'email', 'telefono', 'indirizzo', 'cap', 'citta', 'provincia'];
    
    // Aggiungi campi obbligatori in base al tipo cliente
    if ($_POST['tipo_cliente'] === 'privato') {
        $campi_obbligatori = array_merge($campi_obbligatori, ['nome', 'cognome', 'codice_fiscale']);
    } else {
        $campi_obbligatori = array_merge($campi_obbligatori, ['ragione_sociale', 'partita_iva']);
    }

    foreach ($campi_obbligatori as $campo) {
        if (empty($_POST[$campo])) {
            sendError("Il campo $campo è obbligatorio");
        }
    }

    // Prepara i dati per il database
    $dati = [
        'tipo_cliente' => $_POST['tipo_cliente'],
        'email' => $_POST['email'],
        'telefono' => $_POST['telefono'],
        'indirizzo' => $_POST['indirizzo'],
        'cap' => $_POST['cap'],
        'citta' => $_POST['citta'],
        'provincia' => $_POST['provincia'],
        'note' => $_POST['note'] ?? null
    ];

    // Aggiungi campi in base al tipo cliente
    if ($_POST['tipo_cliente'] === 'privato') {
        $dati = array_merge($dati, [
            'nome' => $_POST['nome'],
            'cognome' => $_POST['cognome'],
            'codice_fiscale' => $_POST['codice_fiscale'],
            'ragione_sociale' => null,
            'partita_iva' => null
        ]);
    } else {
        $dati = array_merge($dati, [
            'nome' => null,
            'cognome' => null,
            'codice_fiscale' => null,
            'ragione_sociale' => $_POST['ragione_sociale'],
            'partita_iva' => $_POST['partita_iva']
        ]);
    }

    if (isset($_POST['id'])) {
        // Aggiornamento
        $sql = "UPDATE clienti SET 
                tipo_cliente = :tipo_cliente,
                nome = :nome,
                cognome = :cognome,
                ragione_sociale = :ragione_sociale,
                codice_fiscale = :codice_fiscale,
                partita_iva = :partita_iva,
                email = :email,
                telefono = :telefono,
                indirizzo = :indirizzo,
                cap = :cap,
                citta = :citta,
                provincia = :provincia,
                note = :note
                WHERE id = :id";
        
        $dati['id'] = $_POST['id'];
    } else {
        // Inserimento
        $sql = "INSERT INTO clienti (
                tipo_cliente, nome, cognome, ragione_sociale, codice_fiscale, 
                partita_iva, email, telefono, indirizzo, cap, citta, provincia, note
                ) VALUES (
                :tipo_cliente, :nome, :cognome, :ragione_sociale, :codice_fiscale,
                :partita_iva, :email, :telefono, :indirizzo, :cap, :citta, :provincia, :note
                )";
    }

    $stmt = $conn->prepare($sql);
    $stmt->execute($dati);

    if (!isset($_POST['id'])) {
        $dati['id'] = $conn->lastInsertId();
    }

    sendResponse([
        'success' => true,
        'message' => isset($_POST['id']) ? 'Cliente aggiornato con successo' : 'Cliente inserito con successo',
        'data' => $dati
    ]);

} catch (PDOException $e) {
    sendError('Errore durante il salvataggio: ' . $e->getMessage());
} 