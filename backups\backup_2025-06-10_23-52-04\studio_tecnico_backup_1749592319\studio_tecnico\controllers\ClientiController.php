<?php
class ClientiController {
    private $conn;
    
    public function __construct($conn) {
        $this->conn = $conn;
    }
    
    public function index($page = 1, $items_per_page = 20) {
        try {
            // Calcolo offset per la paginazione
            $offset = ($page - 1) * $items_per_page;
            
            // Query per il conteggio totale dei clienti
            $count_query = "SELECT COUNT(*) as total FROM clienti";
            $count_stmt = $this->conn->query($count_query);
            $total_items = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // Query per recuperare i clienti con il conteggio delle pratiche attraverso i progetti
            $query = "SELECT c.*, 
                            COALESCE(COUNT(DISTINCT pr.id), 0) as num_pratiche,
                            GROUP_CONCAT(DISTINCT p.nome_progetto) as progetti 
                     FROM clienti c 
                     LEFT JOIN progetti p ON c.id = p.cliente_id 
                     LEFT JOIN pratiche pr ON p.id = pr.progetto_id 
                     GROUP BY c.id 
                     ORDER BY c.cognome, c.nome 
                     LIMIT :limit OFFSET :offset";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindValue(':limit', $items_per_page, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            
            $clienti = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Per ogni cliente, formatta i dati
            foreach ($clienti as &$cliente) {
                $cliente['progetti'] = $cliente['progetti'] ? explode(',', $cliente['progetti']) : [];
            }
            
            // Calcolo informazioni per la paginazione
            $total_pages = ceil($total_items / $items_per_page);
            
            return [
                'clienti' => $clienti,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $total_pages,
                    'items_per_page' => $items_per_page,
                    'total_items' => $total_items,
                    'total_records' => $total_items
                ]
            ];
        } catch (PDOException $e) {
            logError("Errore nella query di index: " . $e->getMessage());
            return false;
        }
    }
    
    public function getAllClienti() {
        try {
            $query = "SELECT * FROM clienti ORDER BY cognome, nome";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            logError("Errore nel recupero dei clienti: " . $e->getMessage());
            return false;
        }
    }
    
    public function getClienteById($id) {
        try {
            $query = "SELECT * FROM clienti WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            logError("Errore nel recupero del cliente ID $id: " . $e->getMessage());
            return false;
        }
    }
    
    public function insertCliente($data) {
        try {
            $query = "INSERT INTO clienti (nome, cognome, email, telefono, indirizzo) 
                     VALUES (:nome, :cognome, :email, :telefono, :indirizzo)";
            $stmt = $this->conn->prepare($query);
            $stmt->execute($data);
            return $this->conn->lastInsertId();
        } catch (PDOException $e) {
            logError("Errore nell'inserimento del cliente: " . $e->getMessage());
            return false;
        }
    }
    
    public function updateCliente($id, $data) {
        try {
            $query = "UPDATE clienti 
                     SET nome = :nome, 
                         cognome = :cognome, 
                         email = :email, 
                         telefono = :telefono, 
                         indirizzo = :indirizzo 
                     WHERE id = :id";
            $data['id'] = $id;
            $stmt = $this->conn->prepare($query);
            return $stmt->execute($data);
        } catch (PDOException $e) {
            logError("Errore nell'aggiornamento del cliente ID $id: " . $e->getMessage());
            return false;
        }
    }
    
    public function deleteCliente($id) {
        try {
            $query = "DELETE FROM clienti WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            return $stmt->execute();
        } catch (PDOException $e) {
            logError("Errore nell'eliminazione del cliente ID $id: " . $e->getMessage());
            return false;
        }
    }
}
