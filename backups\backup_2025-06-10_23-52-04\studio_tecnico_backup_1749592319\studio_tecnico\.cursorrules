Rispondi sempre in italiano. Non sono un programmatore. Guidami step by step e fammi capire cosa devo fare.
Progetto gestionale studio tecnico

Creare una webapp per la gestione di pratiche e clienti per studi di architettura potrebbe risolvere molte problematiche operative e migliorare l’efficienza dello studio. Di seguito, ecco un'idea approfondita:

Obiettivo della Webapp
Una piattaforma che centralizzi la gestione di clienti, progetti e pratiche, ottimizzando il workflow dello studio di architettura e riducendo errori e tempi morti.

Funzionalità principali
1. Gestione dei Clienti
•	Archivio digitale con schede dettagliate per ogni cliente.
•	Cronologia interazioni (email, telefonate, appuntamenti).
•	Accesso rapido ai documenti e alle pratiche relative a ciascun cliente.
2. Gestione delle Pratiche
•	Creazione e aggiornamento di pratiche: possibilità di aggiungere documenti, note, e deadline.
•	Stato della pratica: visualizzazione dello stato attuale (in attesa, in revisione, approvata).
•	Workflow preconfigurati: passi standard per le pratiche edilizie, con scadenze automatizzate.
3. Automazione PDF
•	Moduli precompilati: importazione automatica dei dati del cliente e del progetto in moduli PDF standard.
•	Modifica rapida: strumenti integrati per completare o modificare i PDF senza software aggiuntivi.
4. Collaborazione multiutente
•	Accesso simultaneo per più utenti con gestione dei permessi (es. il responsabile può approvare modifiche, mentre i collaboratori solo leggere o caricare file).
•	Gestione delle priorità per evitare conflitti durante la modifica o l’accesso ai dati.
5. Scadenze e promemoria
•	Notifiche automatiche via email o app per scadenze imminenti.
•	Calendario integrato per organizzare appuntamenti e attività.
6. Analisi e report
•	Statistiche sull’avanzamento dei progetti.
•	Resoconti per il monitoraggio dei costi e dei ricavi per progetto.
7. Integrazione con archiviazione fisica
•	Possibilità di salvare i file su un disco condiviso o su servizi cloud (es. Google Drive, OneDrive) per accedere ai documenti in qualsiasi momento.
Per un hosting Windows Smart su Register.it, le tecnologie supportate saranno tipicamente compatibili con l'ambiente Microsoft, ad esempio:

Tecnologie compatibili
Backend:

ASP.NET: Framework ideale per sviluppare applicazioni web su piattaforme Windows.
PHP: Spesso supportato su hosting Windows, è un’opzione versatile.
Classic ASP: Legacy, ma utile per compatibilità con progetti molto semplici.
Database:

Microsoft SQL Server: Il database più comune su hosting Windows.
MySQL: Solitamente supportato anche su hosting Windows.
Frontend:

HTML5, CSS3, JavaScript (framework come React o Vue.js possono essere utilizzati per il frontend, poiché non dipendono dall'hosting server-side).
Gestione dei PDF:

Librerie compatibili con ASP.NET, come iTextSharp o PDFSharp, per la manipolazione di documenti PDF.
Linguaggi di scripting:

C# o VB.NET per ASP.NET.
PHP per script più leggeri.
Progetto adattato
Per il tuo caso, propongo:

Framework: ASP.NET Core, che offre flessibilità e compatibilità con l'ambiente Windows.
Database: Microsoft SQL Server o MySQL, in base alle specifiche offerte dal tuo piano hosting.
PDF Tools: iTextSharp per compilare moduli PDF in backend.

Spiegami tutto quello che fai passo passo. Quando devi fare delle operazioni 
sui file e le cartelle usa comandi windows o cmd.
Non modificare il codice tutto insieme. Fai modifiche in modo
più grduale per non avere problemi con l'editor.
All'inizio della sessione fai una scansione di tutto l'albero 
e crea un file nella root chiamato app_map.md che mostri tutti i file presenti nelle cartelle.
Prima di creare nuovi file controlla la cartella principale e tutte le sottocartelle per 
vedere se il file già esiste. 
Quando crei o elimini un file aggiorna app_map.md.
app_map.md è un file di documentazione molto importante che serve a:
1.Mappatura del Progetto:
    Mostra la struttura completa di tutte le cartelle e file
    Descrive la funzione di ogni componente
    Fornire una panoramica dell'intera applicazione
2. Guida di Riferimento:
    Contiene informazioni sull'ambiente di sviluppo
    Spiega le regole base da seguire
    Documenta le procedure operative
    Elenca i file critici da non modificare
3. Documentazione Tecnica:
    Descrive l'architettura del sistema
    Spiega il funzionamento del database
    Documenta le API disponibili
    Fornice informazioni sul metodo di calcolo sismico
4. Registro Modifiche:
    Tiene traccia di tutti gli aggiornamenti
    Documenta le nuove funzionalità aggiunte
    Registra le modifiche ai file
    Mantiene uno storico delle versioni
5.Guida alla Manutenzione:
    Procedure di debug
    Gestione degli errori comuni
    Istruzioni per i backup
    Best practices da seguire
È fondamentale mantenere questo file sempre aggiornato quando:
Si creano nuovi file
Si eliminano file esistenti
Si apportano modifiche significative
Si aggiungono nuove funzionalità
Questo file serve come "mappa" dell'applicazione e aiuta chiunque debba lavorare sul progetto a capirne la struttura e il funzionamento.
Non prendere iniziative che modificano il layout delle pagine già costruito
a meno di mie indicazioni
Avvertimi quando la chat o il composer sta diventando troppo lunga e possono generarsi errori.