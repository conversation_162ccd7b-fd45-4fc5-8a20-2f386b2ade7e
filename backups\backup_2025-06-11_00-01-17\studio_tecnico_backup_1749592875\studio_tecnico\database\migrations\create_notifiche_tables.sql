-- Tabella per le notifiche
CREATE TABLE IF NOT EXISTS notifiche (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tipo VARCHAR(50) NOT NULL,
    titolo VARCHAR(255) NOT NULL,
    messaggio TEXT NOT NULL,
    data_creazione DATETIME NOT NULL,
    data_scadenza DATETIME,
    stato ENUM('non_letta', 'letta', 'archiviata') NOT NULL DEFAULT 'non_letta',
    priorita ENUM('bassa', 'media', 'alta') NOT NULL DEFAULT 'media',
    link VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabella per le relazioni tra notifiche e utenti
CREATE TABLE IF NOT EXISTS notifiche_utenti (
    notifica_id INT NOT NULL,
    utente_id INT NOT NULL,
    letta BOOLEAN NOT NULL DEFAULT FALSE,
    data_lettura DATETIME,
    PRIMARY KEY (notifica_id, utente_id),
    FOREIGN KEY (notifica_id) REFERENCES notifiche(id) ON DELETE CASCADE,
    FOREIGN KEY (utente_id) REFERENCES utenti(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabella per le preferenze di notifica degli utenti
CREATE TABLE IF NOT EXISTS preferenze_notifiche (
    utente_id INT NOT NULL,
    tipo_notifica VARCHAR(50) NOT NULL,
    email BOOLEAN NOT NULL DEFAULT TRUE,
    browser BOOLEAN NOT NULL DEFAULT TRUE,
    anticipo_giorni INT NOT NULL DEFAULT 7,
    PRIMARY KEY (utente_id, tipo_notifica),
    FOREIGN KEY (utente_id) REFERENCES utenti(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inserimento delle preferenze di default per i tipi di notifica
INSERT INTO preferenze_notifiche (utente_id, tipo_notifica, email, browser, anticipo_giorni)
SELECT 
    u.id,
    tn.tipo_notifica,
    TRUE,
    TRUE,
    CASE 
        WHEN tn.tipo_notifica = 'scadenza_pratica' THEN 7
        WHEN tn.tipo_notifica = 'scadenza_integrazione' THEN 5
        WHEN tn.tipo_notifica = 'scadenza_pagamento' THEN 10
        ELSE 7
    END as anticipo_giorni
FROM utenti u
CROSS JOIN (
    SELECT 'scadenza_pratica' as tipo_notifica
    UNION SELECT 'scadenza_integrazione'
    UNION SELECT 'scadenza_pagamento'
    UNION SELECT 'nuovo_allegato'
    UNION SELECT 'modifica_pratica'
) tn
ON DUPLICATE KEY UPDATE tipo_notifica = tn.tipo_notifica;
