/* Stili comuni per le pagine di gestione */
.management-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.25rem;
    height: 100%;
    transition: all 0.2s ease;
}

.management-card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.page-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
}

.page-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: #111827;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-title i {
    font-size: 1.1em;
    color: #4b5563;
}

.page-actions {
    display: flex;
    gap: 0.5rem;
    margin-left: auto;
}

.btn {
    font-size: 0.813rem;
    padding: 0.375rem 0.75rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn i {
    font-size: 0.875em;
}

.btn-neutral {
    background-color: #f3f4f6;
    color: #111827;
    border: 1px solid #e5e7eb;
}

.btn-neutral:hover {
    background-color: #e5e7eb;
}

.btn-outline-neutral {
    color: #4b5563;
    border-color: #e5e7eb;
    background: transparent;
}

.btn-outline-neutral:hover {
    background-color: #f3f4f6;
    color: #111827;
}

/* Form styles */
.form-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.form-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.form-section-title {
    font-size: 1rem;
    font-weight: 500;
    color: #4b5563;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.form-control {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #a3a9b3;
    box-shadow: 0 0 0 1px #a3a9b3;
}

/* Table styles */
.table-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: #f9fafb;
    font-size: 0.813rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.table tbody td {
    font-size: 0.875rem;
    color: #4b5563;
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid #e5e7eb;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr:hover {
    background-color: #f9fafb;
}

/* Alert styles */
.alert {
    border: none;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.alert i {
    font-size: 1em;
}

.alert-success {
    background-color: #ecfdf5;
    color: #065f46;
}

.alert-danger {
    background-color: #fef2f2;
    color: #991b1b;
}

.alert-info {
    background-color: #eff6ff;
    color: #1e40af;
}

/* Badge styles */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Status indicators */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-active {
    background-color: #10b981;
}

.status-pending {
    background-color: #f59e0b;
}

.status-completed {
    background-color: #6366f1;
}

.status-cancelled {
    background-color: #ef4444;
}
