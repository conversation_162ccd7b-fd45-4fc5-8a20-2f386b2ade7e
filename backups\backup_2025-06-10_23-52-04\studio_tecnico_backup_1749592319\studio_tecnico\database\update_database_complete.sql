-- database/update_database_complete.sql
-- Script completo per aggiornamento database Studio Tecnico
-- Supporta: Workflow Pratiche + Sistema Notifiche
-- Data: 5 Gennaio 2025

-- =====================================================
-- BACKUP E VERIFICA PRELIMINARE
-- =====================================================

-- Verifica versione MySQL/MariaDB
SELECT VERSION() as database_version;

-- Backup tabella pratiche (stati attuali)
CREATE TABLE IF NOT EXISTS `pratiche_backup_pre_update` AS 
SELECT * FROM `pratiche`;

-- Verifica stati esistenti
SELECT 
    'VERIFICA STATI ESISTENTI' as check_type,
    stato,
    COUNT(*) as count,
    GROUP_CONCAT(id ORDER BY id) as pratiche_ids
FROM pratiche 
GROUP BY stato;

-- =====================================================
-- FASE 1: AGGIORNAMENTO TABELLA PRATICHE
-- =====================================================

-- 1.1 Aggiornamento enum stati per workflow completo
ALTER TABLE `pratiche` 
MODIFY COLUMN `stato` ENUM(
    'in_attesa',
    'in_revisione', 
    'approvata',
    'completata',
    'sospesa',
    'respinta'
) DEFAULT 'in_attesa'
COMMENT 'Stati workflow: in_attesa->in_revisione->approvata->completata, con sospesa/respinta';

-- 1.2 Aggiunta indici per performance
CREATE INDEX IF NOT EXISTS `idx_pratiche_stato` ON `pratiche` (`stato`);
CREATE INDEX IF NOT EXISTS `idx_pratiche_data_scadenza` ON `pratiche` (`data_scadenza`);
CREATE INDEX IF NOT EXISTS `idx_pratiche_data_scadenza_integrazione` ON `pratiche` (`data_scadenza_integrazione`);
CREATE INDEX IF NOT EXISTS `idx_pratiche_stato_data` ON `pratiche` (`stato`, `data_apertura`);
CREATE INDEX IF NOT EXISTS `idx_pratiche_ente` ON `pratiche` (`ente_riferimento`);
CREATE INDEX IF NOT EXISTS `idx_pratiche_responsabile` ON `pratiche` (`responsabile`);

-- 1.3 Aggiornamento commenti tabella
ALTER TABLE `pratiche` 
COMMENT = 'Pratiche edilizie con workflow automatizzato e controllo scadenze';

-- =====================================================
-- FASE 2: CREAZIONE SISTEMA NOTIFICHE
-- =====================================================

-- 2.1 Tabella principale notifiche
CREATE TABLE IF NOT EXISTS `notifiche` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `tipo` enum('scadenza','pratica','progetto','sistema','fattura','cliente','documento') NOT NULL,
  `titolo` varchar(255) NOT NULL,
  `messaggio` text NOT NULL,
  `priorita` enum('bassa','media','alta') DEFAULT 'media',
  `link_azione` varchar(255) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `letta` boolean DEFAULT FALSE,
  `data_creazione` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `data_lettura` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_letta` (`user_id`, `letta`),
  KEY `idx_data_creazione` (`data_creazione`),
  KEY `idx_tipo` (`tipo`),
  KEY `idx_priorita` (`priorita`),
  KEY `idx_notifiche_user_tipo_data` (`user_id`, `tipo`, `data_creazione`),
  KEY `idx_notifiche_letta_data` (`letta`, `data_creazione`),
  CONSTRAINT `fk_notifiche_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT = 'Notifiche sistema con supporto workflow e scadenze automatiche';

-- 2.2 Tabella preferenze notifiche utenti
CREATE TABLE IF NOT EXISTS `notifiche_preferenze` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `tipo_notifica` varchar(50) NOT NULL,
  `email_enabled` boolean DEFAULT TRUE,
  `push_enabled` boolean DEFAULT TRUE,
  `soglia_giorni` int(11) DEFAULT 7,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_tipo` (`user_id`, `tipo_notifica`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_notifiche_preferenze_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT = 'Preferenze notifiche personalizzate per ogni utente';

-- =====================================================
-- FASE 3: DATI INIZIALI E CONFIGURAZIONE
-- =====================================================

-- 3.1 Preferenze default per tutti gli utenti esistenti
INSERT IGNORE INTO `notifiche_preferenze` (`user_id`, `tipo_notifica`, `email_enabled`, `push_enabled`, `soglia_giorni`)
SELECT 
    u.id,
    tipo.tipo_notifica,
    TRUE,
    TRUE,
    CASE 
        WHEN tipo.tipo_notifica IN ('scadenza', 'pratica') THEN 7
        WHEN tipo.tipo_notifica = 'sistema' THEN 1
        ELSE 3
    END
FROM `users` u
CROSS JOIN (
    SELECT 'scadenza' as tipo_notifica
    UNION SELECT 'pratica'
    UNION SELECT 'progetto' 
    UNION SELECT 'sistema'
    UNION SELECT 'fattura'
    UNION SELECT 'cliente'
    UNION SELECT 'documento'
) tipo;

-- 3.2 Notifiche di sistema iniziali
INSERT IGNORE INTO `notifiche` (`user_id`, `tipo`, `titolo`, `messaggio`, `priorita`, `metadata`)
VALUES 
(1, 'sistema', 'Database aggiornato', 'Il database è stato aggiornato con successo per supportare il workflow pratiche e sistema notifiche.', 'media', '{"tipo_notifica": "database_aggiornato", "versione": "2025-01-05"}'),
(1, 'sistema', 'Workflow pratiche attivato', 'Il sistema di workflow automatizzato per le pratiche è ora attivo con 6 stati e transizioni validate.', 'bassa', '{"tipo_notifica": "workflow_attivato", "stati_supportati": 6}'),
(1, 'sistema', 'Sistema notifiche attivato', 'Il sistema di notifiche automatiche è stato configurato e attivato con successo.', 'bassa', '{"tipo_notifica": "notifiche_attivate"});

-- =====================================================
-- FASE 4: FUNZIONALITÀ AVANZATE
-- =====================================================

-- 4.1 Vista statistiche notifiche
CREATE OR REPLACE VIEW `v_notifiche_stats` AS
SELECT 
    u.id as user_id,
    u.username,
    COUNT(n.id) as totale_notifiche,
    SUM(CASE WHEN n.letta = FALSE THEN 1 ELSE 0 END) as non_lette,
    SUM(CASE WHEN n.letta = TRUE THEN 1 ELSE 0 END) as lette,
    SUM(CASE WHEN n.priorita = 'alta' AND n.letta = FALSE THEN 1 ELSE 0 END) as alta_priorita_non_lette,
    MAX(n.data_creazione) as ultima_notifica
FROM `users` u
LEFT JOIN `notifiche` n ON u.id = n.user_id
GROUP BY u.id, u.username;

-- 4.2 Stored procedure pulizia automatica
DELIMITER //
CREATE OR REPLACE PROCEDURE `sp_pulisci_notifiche_vecchie`(IN giorni_retention INT)
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Elimina notifiche lette più vecchie del periodo specificato
    DELETE FROM `notifiche` 
    WHERE `letta` = TRUE 
    AND `data_creazione` < DATE_SUB(NOW(), INTERVAL giorni_retention DAY);
    
    SET deleted_count = ROW_COUNT();
    
    -- Log dell'operazione se sono state eliminate notifiche
    IF deleted_count > 0 THEN
        INSERT INTO `notifiche` (`user_id`, `tipo`, `titolo`, `messaggio`, `priorita`, `metadata`)
        VALUES (1, 'sistema', 'Pulizia automatica notifiche', 
                CONCAT('Eliminate ', deleted_count, ' notifiche vecchie di ', giorni_retention, ' giorni'), 
                'bassa',
                JSON_OBJECT('deleted_count', deleted_count, 'retention_days', giorni_retention));
    END IF;
    
    COMMIT;
    SELECT deleted_count as notifiche_eliminate;
END //
DELIMITER ;

-- 4.3 Trigger aggiornamento data lettura
DELIMITER //
CREATE OR REPLACE TRIGGER `tr_notifiche_update_data_lettura`
BEFORE UPDATE ON `notifiche`
FOR EACH ROW
BEGIN
    IF NEW.letta = TRUE AND OLD.letta = FALSE THEN
        SET NEW.data_lettura = NOW();
    END IF;
END //
DELIMITER ;

-- 4.4 Funzione conteggio notifiche non lette
DELIMITER //
CREATE OR REPLACE FUNCTION `fn_count_notifiche_non_lette`(user_id_param INT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE count_result INT DEFAULT 0;
    
    SELECT COUNT(*) INTO count_result
    FROM `notifiche`
    WHERE `user_id` = user_id_param AND `letta` = FALSE;
    
    RETURN count_result;
END //
DELIMITER ;

-- =====================================================
-- FASE 5: VERIFICA E OTTIMIZZAZIONE
-- =====================================================

-- 5.1 Analizza tabelle per ottimizzazione
ANALYZE TABLE `pratiche`;
ANALYZE TABLE `notifiche`;
ANALYZE TABLE `notifiche_preferenze`;

-- 5.2 Verifica integrità referenziale
SELECT 'VERIFICA FOREIGN KEYS' as check_type;
SELECT 
    TABLE_NAME,
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE
FROM information_schema.TABLE_CONSTRAINTS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('pratiche', 'notifiche', 'notifiche_preferenze')
AND CONSTRAINT_TYPE = 'FOREIGN KEY';

-- 5.3 Verifica indici creati
SELECT 'VERIFICA INDICI' as check_type;
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('pratiche', 'notifiche', 'notifiche_preferenze')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- =====================================================
-- FASE 6: REPORT FINALE
-- =====================================================

-- 6.1 Riepilogo aggiornamento
SELECT 'RIEPILOGO AGGIORNAMENTO DATABASE' as report_type;

SELECT 'TABELLA PRATICHE' as tabella, COUNT(*) as record_totali FROM pratiche
UNION ALL
SELECT 'TABELLA NOTIFICHE' as tabella, COUNT(*) as record_totali FROM notifiche
UNION ALL
SELECT 'TABELLA PREFERENZE' as tabella, COUNT(*) as record_totali FROM notifiche_preferenze;

-- 6.2 Stati pratiche dopo aggiornamento
SELECT 'STATI PRATICHE DOPO AGGIORNAMENTO' as check_type;
SELECT 
    stato,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM pratiche), 2) as percentuale
FROM pratiche 
GROUP BY stato
ORDER BY count DESC;

-- 6.3 Preferenze utenti configurate
SELECT 'PREFERENZE UTENTI CONFIGURATE' as check_type;
SELECT 
    tipo_notifica,
    COUNT(*) as utenti_configurati,
    SUM(CASE WHEN email_enabled = TRUE THEN 1 ELSE 0 END) as email_abilitata,
    SUM(CASE WHEN push_enabled = TRUE THEN 1 ELSE 0 END) as push_abilitata
FROM notifiche_preferenze
GROUP BY tipo_notifica
ORDER BY tipo_notifica;

-- 6.4 Messaggio finale
SELECT 
    'AGGIORNAMENTO COMPLETATO CON SUCCESSO!' as messaggio,
    NOW() as timestamp_completamento,
    'Workflow pratiche e sistema notifiche attivati' as stato;

-- =====================================================
-- COMMIT FINALE
-- =====================================================
COMMIT;

-- =====================================================
-- ISTRUZIONI POST-AGGIORNAMENTO
-- =====================================================

/*
ISTRUZIONI PER L'ESECUZIONE:

1. BACKUP COMPLETO:
   mysqldump -u username -p database_name > backup_pre_update.sql

2. ESECUZIONE SCRIPT:
   mysql -u username -p database_name < update_database_complete.sql

3. VERIFICA RISULTATI:
   - Controllare output del report finale
   - Eseguire test_pratiche.php per verificare funzionalità
   - Eseguire test_notifiche.php per verificare notifiche

4. ROLLBACK (se necessario):
   mysql -u username -p database_name < backup_pre_update.sql

FUNZIONALITÀ ABILITATE DOPO L'AGGIORNAMENTO:
✅ Workflow pratiche con 6 stati
✅ Transizioni validate automaticamente
✅ Sistema notifiche completo
✅ Controllo scadenze automatico
✅ Dashboard statistiche avanzate
✅ Preferenze utente personalizzabili
✅ Pulizia automatica notifiche
✅ Performance ottimizzate con indici

COMPATIBILITÀ:
✅ Dati esistenti preservati
✅ Nessuna perdita di informazioni
✅ Backward compatibility mantenuta
*/
