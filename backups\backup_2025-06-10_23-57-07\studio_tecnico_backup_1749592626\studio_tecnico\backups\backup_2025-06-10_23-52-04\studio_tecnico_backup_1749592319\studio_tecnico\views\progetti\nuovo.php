<?php
$pageTitle = 'Nuovo Progetto';
include VIEWS_DIR . '/layouts/header.php';
?>
<link rel="stylesheet" href="<?= BASE_URL ?>assets/css/management.css">

<?php
require_once dirname(dirname(__DIR__)) . '/config/config.php';
use App\Core\Security;
?>

<div class="container-fluid py-4">
    <div class="page-header">
        <h2 class="page-title">
            <i class="fas fa-project-diagram me-2"></i>
            Nuovo Progetto
        </h2>
        <div class="page-actions">
            <a href="<?= BASE_URL ?>progetti" class="btn btn-outline-neutral">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <?php if (!empty($errori)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i>
            <ul class="mb-0">
                <?php foreach ($errori as $errore): ?>
                    <li><?php echo htmlspecialchars($errore); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="content-card">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i> Dettagli Progetto
                </h5>
            </div>
            <div class="card-body">
                <form action="<?php echo BASE_URL; ?>progetti/nuovo" method="POST" id="progettoForm">
                    <?php echo Security::csrfField(); ?>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cliente_id" class="form-label">Cliente *</label>
                            <select class="form-select" id="cliente_id" name="cliente_id" required>
                                <option value="">Seleziona cliente</option>
                                <?php foreach ($clienti as $cliente): ?>
                                    <option value="<?php echo $cliente['id']; ?>" 
                                        <?php echo (isset($_POST['cliente_id']) && $_POST['cliente_id'] == $cliente['id']) ? 'selected' : ''; ?>>
                                        <?php 
                                        if ($cliente['tipo_cliente'] === 'privato') {
                                            echo htmlspecialchars($cliente['nome'] . ' ' . $cliente['cognome']);
                                        } else {
                                            echo htmlspecialchars($cliente['ragione_sociale']);
                                        }
                                        ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="nome_progetto" class="form-label">Nome Progetto *</label>
                            <input type="text" class="form-control" id="nome_progetto" name="nome_progetto" 
                                   value="<?php echo isset($_POST['nome_progetto']) ? htmlspecialchars($_POST['nome_progetto']) : ''; ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="data_inizio" class="form-label">Data Inizio *</label>
                            <input type="date" class="form-control" id="data_inizio" name="data_inizio" 
                                   value="<?php echo isset($_POST['data_inizio']) ? $_POST['data_inizio'] : date('Y-m-d'); ?>" required>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="data_fine_prevista" class="form-label">Data Fine Prevista *</label>
                            <input type="date" class="form-control" id="data_fine_prevista" name="data_fine_prevista" 
                                   value="<?php echo isset($_POST['data_fine_prevista']) ? $_POST['data_fine_prevista'] : ''; ?>" required>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="data_fine" class="form-label">Data Fine Effettiva</label>
                            <input type="date" class="form-control" id="data_fine" name="data_fine" 
                                   value="<?php echo isset($_POST['data_fine']) ? $_POST['data_fine'] : ''; ?>">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="stato" class="form-label">Stato *</label>
                            <select class="form-select" id="stato" name="stato" required>
                                <option value="">Seleziona stato</option>
                                <option value="in_corso" <?php echo (isset($_POST['stato']) && $_POST['stato'] === 'in_corso') ? 'selected' : ''; ?>>In Corso</option>
                                <option value="completato" <?php echo (isset($_POST['stato']) && $_POST['stato'] === 'completato') ? 'selected' : ''; ?>>Completato</option>
                                <option value="sospeso" <?php echo (isset($_POST['stato']) && $_POST['stato'] === 'sospeso') ? 'selected' : ''; ?>>Sospeso</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="budget" class="form-label">Budget (€)</label>
                            <input type="number" step="0.01" min="0" class="form-control" id="budget" name="budget" 
                                   value="<?php echo isset($_POST['budget']) ? htmlspecialchars($_POST['budget']) : ''; ?>">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="descrizione" class="form-label">Descrizione</label>
                        <textarea class="form-control" id="descrizione" name="descrizione" rows="4"><?php echo isset($_POST['descrizione']) ? htmlspecialchars($_POST['descrizione']) : ''; ?></textarea>
                    </div>

                    <div class="text-end mt-4">
                        <button type="submit" class="btn btn-neutral">
                            <i class="fas fa-save"></i> Salva Progetto
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('progettoForm');
    const dataInizio = document.getElementById('data_inizio');
    const dataFinePrevista = document.getElementById('data_fine_prevista');
    const dataFine = document.getElementById('data_fine');
    
    form.addEventListener('submit', function(e) {
        if (dataFinePrevista.value && dataFinePrevista.value < dataInizio.value) {
            e.preventDefault();
            alert('La data di fine prevista non può essere precedente alla data di inizio');
        }
        if (dataFine.value && dataFine.value < dataInizio.value) {
            e.preventDefault();
            alert('La data di fine effettiva non può essere precedente alla data di inizio');
        }
    });
});
</script>

<?php require_once ROOT_PATH . '/includes/footer.php'; ?>