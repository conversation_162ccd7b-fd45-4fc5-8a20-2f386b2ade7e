-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.4.32-MariaD<PERSON>, for Win64 (AMD64)
--
-- Host: localhost    Database: studio_tecnico
-- ------------------------------------------------------
-- Server version	10.4.32-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `studio_tecnico`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `studio_tecnico` /*!40100 DEFAULT CHARACTER SET latin1 COLLATE latin1_swedish_ci */;

USE `studio_tecnico`;

--
-- Table structure for table `admin_profile`
--

DROP TABLE IF EXISTS `admin_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_profile` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `nome` varchar(100) NOT NULL,
  `cognome` varchar(100) NOT NULL,
  `codice_fiscale` varchar(16) NOT NULL,
  `partita_iva` varchar(11) NOT NULL,
  `indirizzo` varchar(255) NOT NULL,
  `citta` varchar(100) NOT NULL,
  `cap` varchar(5) NOT NULL,
  `provincia` varchar(2) NOT NULL,
  `telefono` varchar(20) NOT NULL,
  `email` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  CONSTRAINT `admin_profile_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_profile`
--

LOCK TABLES `admin_profile` WRITE;
/*!40000 ALTER TABLE `admin_profile` DISABLE KEYS */;
/*!40000 ALTER TABLE `admin_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `clienti`
--

DROP TABLE IF EXISTS `clienti`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clienti` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(100) NOT NULL,
  `cognome` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `telefono` varchar(20) DEFAULT NULL,
  `indirizzo` text DEFAULT NULL,
  `cap_cliente` varchar(10) DEFAULT NULL,
  `data_registrazione` timestamp NOT NULL DEFAULT current_timestamp(),
  `tipo_cliente` enum('privato','societa') NOT NULL DEFAULT 'privato',
  `ragione_sociale` varchar(200) DEFAULT NULL,
  `partita_iva` varchar(20) DEFAULT NULL,
  `codice_fiscale` varchar(16) DEFAULT NULL,
  `pec` varchar(100) DEFAULT NULL,
  `citta` varchar(100) DEFAULT NULL,
  `provincia` varchar(2) DEFAULT NULL,
  `note` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `clienti`
--

LOCK TABLES `clienti` WRITE;
/*!40000 ALTER TABLE `clienti` DISABLE KEYS */;
INSERT INTO `clienti` VALUES (1,'Mauro','Mazzarelli','<EMAIL>','3392720049','via della Stazione di Ciampino 151','00118','2024-12-08 07:04:27','privato','','','****************','<EMAIL>','Roma','RM','sono io, ciao'),(2,'Roberto','Di Nunzio','<EMAIL>','3357421632','via Ajani 33','00044','2024-12-08 15:45:59','privato','','','****************','<EMAIL>','Roma','RM','');
/*!40000 ALTER TABLE `clienti` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pratiche`
--

DROP TABLE IF EXISTS `pratiche`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pratiche` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `progetto_id` int(11) DEFAULT NULL,
  `tipo_pratica` varchar(100) DEFAULT NULL,
  `stato` enum('in_attesa','in_revisione','approvata') DEFAULT 'in_attesa',
  `data_apertura` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_scadenza` date DEFAULT NULL,
  `note` text DEFAULT NULL,
  `numero_pratica` varchar(50) DEFAULT NULL,
  `tipo_documento` enum('cila','scia','permesso_costruire','altro') DEFAULT 'altro',
  `ente_riferimento` varchar(100) DEFAULT NULL,
  `protocollo` varchar(50) DEFAULT NULL,
  `data_protocollo` date DEFAULT NULL,
  `data_scadenza_integrazione` date DEFAULT NULL,
  `importo_diritti` decimal(10,2) DEFAULT NULL,
  `note_interne` text DEFAULT NULL,
  `documenti_richiesti` text DEFAULT NULL,
  `responsabile` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `progetto_id` (`progetto_id`),
  CONSTRAINT `pratiche_ibfk_1` FOREIGN KEY (`progetto_id`) REFERENCES `progetti` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pratiche`
--

LOCK TABLES `pratiche` WRITE;
/*!40000 ALTER TABLE `pratiche` DISABLE KEYS */;
INSERT INTO `pratiche` VALUES (1,1,NULL,'in_attesa','2024-12-07 23:00:00','2025-02-28','','1/2025','cila','','34566','2025-01-17',NULL,NULL,'','doc id','Roberto');
/*!40000 ALTER TABLE `pratiche` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `progetti`
--

DROP TABLE IF EXISTS `progetti`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `progetti` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cliente_id` int(11) DEFAULT NULL,
  `nome_progetto` varchar(200) NOT NULL,
  `descrizione` text DEFAULT NULL,
  `data_inizio` date DEFAULT NULL,
  `data_fine_prevista` date DEFAULT NULL,
  `stato` enum('in_corso','completato','sospeso') DEFAULT 'in_corso',
  `importo` decimal(10,2) DEFAULT NULL,
  `comune` varchar(100) DEFAULT NULL,
  `indirizzo_progetto` text DEFAULT NULL,
  `tipo_progetto` enum('ristrutturazione','nuova_costruzione','consulenza','altro') DEFAULT 'altro',
  `priorita` enum('bassa','media','alta') DEFAULT 'media',
  PRIMARY KEY (`id`),
  KEY `cliente_id` (`cliente_id`),
  CONSTRAINT `progetti_ibfk_1` FOREIGN KEY (`cliente_id`) REFERENCES `clienti` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `progetti`
--

LOCK TABLES `progetti` WRITE;
/*!40000 ALTER TABLE `progetti` DISABLE KEYS */;
INSERT INTO `progetti` VALUES (1,1,'Casa','','2024-12-08','2025-04-08','in_corso',50000.00,'Roma','via della Stazione di Ciampino 151','ristrutturazione','media');
/*!40000 ALTER TABLE `progetti` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `scadenze`
--

DROP TABLE IF EXISTS `scadenze`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `scadenze` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `progetto_id` int(11) DEFAULT NULL,
  `titolo` varchar(255) NOT NULL,
  `descrizione` text DEFAULT NULL,
  `data_scadenza` date NOT NULL,
  `stato` enum('in_corso','completata','scaduta') NOT NULL DEFAULT 'in_corso',
  `priorita` enum('bassa','media','alta') NOT NULL DEFAULT 'media',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `progetto_id` (`progetto_id`),
  CONSTRAINT `scadenze_ibfk_1` FOREIGN KEY (`progetto_id`) REFERENCES `progetti` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `scadenze`
--

LOCK TABLES `scadenze` WRITE;
/*!40000 ALTER TABLE `scadenze` DISABLE KEYS */;
/*!40000 ALTER TABLE `scadenze` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `ruolo` enum('admin','user') NOT NULL DEFAULT 'user',
  `active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (5,'admin','<EMAIL>','$2y$10$VT/tvsPz72/pHDdpDdGCieS6CqO/ouQhwAcuMHsoDZxUwR2YXHsdC','admin',1),(6,'Mauro',NULL,'$2y$10$YourHashedPasswordHere','admin',1);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-12-11  0:23:15
