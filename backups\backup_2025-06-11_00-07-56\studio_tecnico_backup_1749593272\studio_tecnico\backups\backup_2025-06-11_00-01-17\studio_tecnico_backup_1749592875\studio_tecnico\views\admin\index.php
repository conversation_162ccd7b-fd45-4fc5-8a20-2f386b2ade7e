<?php
/**
 * Dashboard principale dell'area amministrativa
 */

// Inclusione configurazione database
require_once __DIR__ . '/../../config/database.php';

ob_start();

function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    return round($bytes / pow(1024, $pow), $precision) . ' ' . $units[$pow];
}

// Verifica connessione database basata sulla presenza di statistiche
$dbConnection = isset($stats) && $stats['users'] > 0;

$pageTitle = 'Dashboard';
?>

<style>
    .btn-outline-primary {
        border-color: #adb5bd;
        color: #495057;
    }
    .btn-outline-primary:hover {
        background-color: #f8f9fa;
        color: #212529;
        border-color: #6c757d;
    }
    .card-title i {
        font-size: 1.2rem;
        margin-right: 5px;
    }

    .dashboard-card {
        background: #ffffff;
        border-radius: 15px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        height: 100%;
        position: relative;
        border: 1px solid #e9ecef;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.15);
    }

    .card-header {
        background: transparent;
        border: none;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .card-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        color: white;
    }

    .users .card-icon {
        background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    }
    .projects .card-icon {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    }
    .clients .card-icon {
        background: linear-gradient(135deg, #868e96 0%, #6c757d 100%);
    }
    .practices .card-icon {
        background: linear-gradient(135deg, #adb5bd 0%, #868e96 100%);
    }
    .logs .card-icon {
        background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    }
    .users-management .card-icon {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    }
    .config .card-icon {
        background: linear-gradient(135deg, #868e96 0%, #6c757d 100%);
    }
    .backup .card-icon {
        background: linear-gradient(135deg, #adb5bd 0%, #868e96 100%);
    }
    .debug-info .card-icon {
        background: linear-gradient(135deg, #adb5bd 0%, #868e96 100%);
    }

    .card-title {
        font-size: 1rem;
        font-weight: 600;
        color: #495057;
        margin: 0;
    }

    .card-stats {
        font-size: 2rem;
        font-weight: 700;
        color: #212529;
        margin: 1rem 0;
    }

    .card-link {
        color: #6c757d;
        text-decoration: none;
        font-size: 0.9rem;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
    }

    .card-link:hover {
        color: #495057;
        transform: translateX(5px);
    }

    .card-link i {
        margin-left: 5px;
    }
</style>

<div class="row">
    <div class="col-12">
        <h1 class="mb-4">Dashboard Amministrativa</h1>
        
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <?= htmlspecialchars($_SESSION['success']) ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <?= htmlspecialchars($_SESSION['error']) ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<div class="row g-4">
    <!-- Card Statistiche -->
    <div class="col-md-6 col-lg-3">
        <div class="card h-100 dashboard-card users">
            <div class="card-body text-center">
                <h5 class="card-title mb-3"><i class="fas fa-users"></i> Utenti Totali</h5>
                <h2 class="card-text"><?= $stats['users'] ?? 0 ?></h2>
            </div>
        </div>
    </div>

<!-- Card Clienti -->
<div class="col-md-6 col-lg-3">
        <div class="card h-100 dashboard-card clients">
            <div class="card-body text-center">
                <h5 class="card-title mb-3"><i class="fas fa-user-tie"></i> Clienti</h5>
                <h2 class="card-text"><?= $stats['clients'] ?? 0 ?></h2>
            </div>
        </div>
    </div>

    <!-- Card Progetti -->
    <div class="col-md-6 col-lg-3">
        <div class="card h-100 dashboard-card projects">
            <div class="card-body text-center">
                <h5 class="card-title mb-3"><i class="fas fa-project-diagram"></i> Progetti</h5>
                <h2 class="card-text"><?= $stats['projects'] ?? 0 ?></h2>
            </div>
        </div>
    </div>

        <!-- Card Pratiche -->
    <div class="col-md-6 col-lg-3">
        <div class="card h-100 dashboard-card practices">
            <div class="card-body text-center">
                <h5 class="card-title mb-3"><i class="fas fa-folder-open"></i> Pratiche</h5>
                <h2 class="card-text"><?= $stats['practices'] ?? 0 ?></h2>
            </div>
        </div>
    </div>
    
<!-- Sezione Azioni Rapide -->
<div class="row mt-4">
    <div class="col-12">
        <h4 class="mb-3">Azioni Rapide</h4>
    </div>
    <div class="col-md-3">
        <div class="card h-100 dashboard-card logs">
            <div class="card-body text-center">
                <h5 class="card-title mb-3"><i class="fas fa-file-alt"></i> Log di Sistema</h5>
                <button onclick="openLogViewer()" class="btn btn-outline-primary btn-sm">
                    Visualizza Log
                </button>
            </div>
        </div>
    </div>
    
    <!-- Card Gestione Utenti -->
    <div class="col-md-3">
        <div class="card h-100 dashboard-card users-management">
            <div class="card-body text-center">
                <h5 class="card-title mb-3"><i class="fas fa-users-cog"></i> Gestione Utenti</h5>
                <a href="<?= BASE_URL ?>admin/users" class="btn btn-outline-primary btn-sm">
                    Gestisci Utenti
                </a>
            </div>
        </div>
    </div>

    <!-- Card Configurazione -->
    <div class="col-md-3">
        <div class="card h-100 dashboard-card config">
            <div class="card-body text-center">
                <h5 class="card-title mb-3"><i class="fas fa-cogs"></i> Configurazione</h5>
                <a href="<?= BASE_URL ?>admin/config" class="btn btn-outline-primary btn-sm">
                    Configura
                </a>
            </div>
        </div>
    </div>

    <!-- Card Backup -->
    <div class="col-md-3">
        <div class="card h-100 dashboard-card backup">
            <div class="card-body text-center">
                <h5 class="card-title mb-3"><i class="fas fa-database"></i> Backup Completo</h5>
                <button onclick="openBackupWindow()" class="btn btn-outline-primary btn-sm">
                    Esegui Backup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Card Debug Info -->
<div class="col-md-12 col-lg-6">
        <div class="card h-100 dashboard-card debug-info">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="fas fa-bug"></i> Debug Information
                    <button class="btn btn-sm btn-outline-secondary float-end" onclick="refreshDebugInfo()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </h5>
                <div class="debug-content" style="font-family: monospace; font-size: 0.9rem;">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>PHP Version:</strong> <?= PHP_VERSION ?></p>
                            <p><strong>Server Software:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?></p>
                            <p><strong>Database Status:</strong> 
                                <span class="badge bg-<?= $dbConnection ? 'success' : 'danger' ?>">
                                    <?= $dbConnection ? 'Connected' : 'Disconnected' ?>
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Memory Usage:</strong> <?= formatBytes(memory_get_usage()) ?></p>
                            <p><strong>Peak Memory:</strong> <?= formatBytes(memory_get_peak_usage()) ?></p>
                            <p><strong>Last Error:</strong> 
                                <span id="lastError" class="text-danger">
                                    <?= error_get_last() ? error_get_last()['message'] : 'None' ?>
                                </span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <p><strong>Session Info:</strong></p>
                        <pre class="bg-light p-2" style="max-height: 100px; overflow-y: auto;">
<?php print_r($_SESSION); ?>
                        </pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openBackupWindow() {
    // Calcola le dimensioni del popup
    const width = 1000;
    const height = 800;
    const left = (window.screen.width - width) / 2;
    const top = (window.screen.height - height) / 2;

    // Apri il popup centrato
    window.open(
        '<?= BASE_URL ?>BackupCompleto.php',
        'Backup Completo',
        `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes,status=yes`
    );
}
</script>

<script>
function openLogViewer() {
    const width = Math.min(1200, window.innerWidth * 0.9);
    const height = Math.min(800, window.innerHeight * 0.9);
    const left = (window.innerWidth - width) / 2;
    const top = (window.innerHeight - height) / 2;
    
    window.open(
        '<?= BASE_URL ?>logs/log_viewer.php',
        'LogViewer',
        `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes,status=no,location=no`
    );
}
</script>

<script>
function refreshDebugInfo() {
    const debugContent = document.querySelector('.debug-content');
    debugContent.innerHTML = '';
    debugContent.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <p><strong>PHP Version:</strong> <?= PHP_VERSION ?></p>
                <p><strong>Server Software:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?></p>
                <p><strong>Database Status:</strong> 
                    <span class="badge bg-<?= $dbConnection ? 'success' : 'danger' ?>">
                        <?= $dbConnection ? 'Connected' : 'Disconnected' ?>
                    </span>
                </p>
            </div>
            <div class="col-md-6">
                <p><strong>Memory Usage:</strong> <?= formatBytes(memory_get_usage()) ?></p>
                <p><strong>Peak Memory:</strong> <?= formatBytes(memory_get_peak_usage()) ?></p>
                <p><strong>Last Error:</strong> 
                    <span id="lastError" class="text-danger">
                        <?= error_get_last() ? error_get_last()['message'] : 'None' ?>
                    </span>
                </p>
            </div>
        </div>
        <div class="mt-3">
            <p><strong>Session Info:</strong></p>
            <pre class="bg-light p-2" style="max-height: 100px; overflow-y: auto;">
<?php print_r($_SESSION); ?>
            </pre>
        </div>
    `;
}
</script>

<?php
$content = ob_get_clean();
include VIEWS_DIR . '/layouts/admin.php';
?>
