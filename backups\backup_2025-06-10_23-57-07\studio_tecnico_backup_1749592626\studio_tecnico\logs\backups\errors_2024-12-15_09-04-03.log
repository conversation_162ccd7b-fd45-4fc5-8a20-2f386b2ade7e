[14-Dec-2024 18:10:09 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:10:09 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:10:09 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:10:09 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:10:09 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:10:09 Europe/Rome] Router inizializzato
[14-Dec-2024 18:10:09 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:10:09 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:10:09 Europe/Rome] Route home definita
[14-Dec-2024 18:10:09 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:10:09 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:10:09 Europe/Rome] Route progetti definite
[14-Dec-2024 18:10:09 Europe/Rome] Route clienti definite
[14-Dec-2024 18:10:09 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:10:09 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:10:09 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:10:09 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cap' in 'field list'
[14-Dec-2024 18:10:09 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:10:09 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:13:00 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:13:00 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:13:00 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:13:00 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:13:00 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:13:00 Europe/Rome] Router inizializzato
[14-Dec-2024 18:13:00 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:13:00 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:13:00 Europe/Rome] Route home definita
[14-Dec-2024 18:13:00 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:13:00 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:13:00 Europe/Rome] Route progetti definite
[14-Dec-2024 18:13:00 Europe/Rome] Route clienti definite
[14-Dec-2024 18:13:00 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:13:00 Europe/Rome] URL richiesto: 
[14-Dec-2024 18:13:00 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:13:00 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:13:00 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:13:00 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:13:00 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:13:00 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:13:00 Europe/Rome] Router inizializzato
[14-Dec-2024 18:13:00 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:13:00 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:13:00 Europe/Rome] Route home definita
[14-Dec-2024 18:13:00 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:13:00 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:13:00 Europe/Rome] Route progetti definite
[14-Dec-2024 18:13:00 Europe/Rome] Route clienti definite
[14-Dec-2024 18:13:00 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:13:00 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 18:13:00 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:13:00 Europe/Rome] 
==================================================
[14-Dec-2024 18:13:00 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 18:13:00 Europe/Rome] ==================================================

[14-Dec-2024 18:13:00 Europe/Rome] Clienti totali: 2
[14-Dec-2024 18:13:00 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 18:13:00 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 18:13:00 Europe/Rome] Progetti totali: 1
[14-Dec-2024 18:13:00 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 18:13:00 Europe/Rome] 
==================================================
[14-Dec-2024 18:13:00 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 18:13:00 Europe/Rome] ==================================================

[14-Dec-2024 18:13:00 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:13:00 Europe/Rome] Route dispatched
[14-Dec-2024 18:13:05 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:13:05 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:13:06 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:13:06 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:13:06 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:13:06 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:13:06 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:13:06 Europe/Rome] Router inizializzato
[14-Dec-2024 18:13:06 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:13:06 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:13:06 Europe/Rome] Route home definita
[14-Dec-2024 18:13:06 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:13:06 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:13:06 Europe/Rome] Route progetti definite
[14-Dec-2024 18:13:06 Europe/Rome] Route clienti definite
[14-Dec-2024 18:13:06 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:13:06 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:13:06 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:13:06 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.data_modifica' in 'field list'
[14-Dec-2024 18:13:06 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:13:06 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:14:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:14:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:14:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:14:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:14:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:14:30 Europe/Rome] Router inizializzato
[14-Dec-2024 18:14:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:14:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:14:30 Europe/Rome] Route home definita
[14-Dec-2024 18:14:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:14:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:14:30 Europe/Rome] Route progetti definite
[14-Dec-2024 18:14:30 Europe/Rome] Route clienti definite
[14-Dec-2024 18:14:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:14:30 Europe/Rome] URL richiesto: 
[14-Dec-2024 18:14:30 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:14:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:14:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:14:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:14:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:14:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:14:30 Europe/Rome] Router inizializzato
[14-Dec-2024 18:14:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:14:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:14:30 Europe/Rome] Route home definita
[14-Dec-2024 18:14:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:14:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:14:30 Europe/Rome] Route progetti definite
[14-Dec-2024 18:14:30 Europe/Rome] Route clienti definite
[14-Dec-2024 18:14:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:14:30 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 18:14:30 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:14:30 Europe/Rome] 
==================================================
[14-Dec-2024 18:14:30 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 18:14:30 Europe/Rome] ==================================================

[14-Dec-2024 18:14:30 Europe/Rome] Clienti totali: 2
[14-Dec-2024 18:14:30 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 18:14:30 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 18:14:30 Europe/Rome] Progetti totali: 1
[14-Dec-2024 18:14:30 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 18:14:30 Europe/Rome] 
==================================================
[14-Dec-2024 18:14:30 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 18:14:30 Europe/Rome] ==================================================

[14-Dec-2024 18:14:30 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:14:30 Europe/Rome] Route dispatched
[14-Dec-2024 18:14:33 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:14:33 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:14:35 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:14:35 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:14:35 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:14:35 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:14:35 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:14:35 Europe/Rome] Router inizializzato
[14-Dec-2024 18:14:35 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:14:35 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:14:35 Europe/Rome] Route home definita
[14-Dec-2024 18:14:35 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:14:35 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:14:35 Europe/Rome] Route progetti definite
[14-Dec-2024 18:14:35 Europe/Rome] Route clienti definite
[14-Dec-2024 18:14:35 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:14:35 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:14:35 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:14:35 Europe/Rome] Errore nel recupero dei dettagli del cliente: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pr.nome' in 'field list'
[14-Dec-2024 18:14:35 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:14:35 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:16:09 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:16:09 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:16:09 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:16:09 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:16:09 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:16:09 Europe/Rome] Router inizializzato
[14-Dec-2024 18:16:09 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:16:09 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:16:09 Europe/Rome] Route home definita
[14-Dec-2024 18:16:09 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:16:09 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:16:09 Europe/Rome] Route progetti definite
[14-Dec-2024 18:16:09 Europe/Rome] Route clienti definite
[14-Dec-2024 18:16:09 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:16:09 Europe/Rome] URL richiesto: admin
[14-Dec-2024 18:16:09 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 18:16:09 Europe/Rome] Route dispatched
[14-Dec-2024 18:16:14 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:16:14 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:16:14 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:16:14 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:16:14 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:16:14 Europe/Rome] Router inizializzato
[14-Dec-2024 18:16:14 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:16:14 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:16:14 Europe/Rome] Route home definita
[14-Dec-2024 18:16:14 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:16:14 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:16:14 Europe/Rome] Route progetti definite
[14-Dec-2024 18:16:14 Europe/Rome] Route clienti definite
[14-Dec-2024 18:16:14 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:16:14 Europe/Rome] URL richiesto: 
[14-Dec-2024 18:16:14 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:16:14 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:16:14 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:16:14 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:16:14 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:16:14 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:16:14 Europe/Rome] Router inizializzato
[14-Dec-2024 18:16:14 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:16:14 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:16:14 Europe/Rome] Route home definita
[14-Dec-2024 18:16:14 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:16:14 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:16:14 Europe/Rome] Route progetti definite
[14-Dec-2024 18:16:14 Europe/Rome] Route clienti definite
[14-Dec-2024 18:16:14 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:16:14 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 18:16:14 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:16:14 Europe/Rome] 
==================================================
[14-Dec-2024 18:16:14 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 18:16:14 Europe/Rome] ==================================================

[14-Dec-2024 18:16:14 Europe/Rome] Clienti totali: 2
[14-Dec-2024 18:16:14 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 18:16:14 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 18:16:14 Europe/Rome] Progetti totali: 1
[14-Dec-2024 18:16:14 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 18:16:14 Europe/Rome] 
==================================================
[14-Dec-2024 18:16:14 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 18:16:14 Europe/Rome] ==================================================

[14-Dec-2024 18:16:14 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:16:14 Europe/Rome] Route dispatched
[14-Dec-2024 18:16:17 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:16:17 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:16:21 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:16:21 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:16:21 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:16:21 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:16:21 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:16:21 Europe/Rome] Router inizializzato
[14-Dec-2024 18:16:21 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:16:21 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:16:21 Europe/Rome] Route home definita
[14-Dec-2024 18:16:21 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:16:21 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:16:21 Europe/Rome] Route progetti definite
[14-Dec-2024 18:16:21 Europe/Rome] Route clienti definite
[14-Dec-2024 18:16:21 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:16:21 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:16:21 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:16:21 Europe/Rome] Dati cliente: Array
(
    [id] => 1
    [tipo_cliente] => privato
    [nome] => Mauro
    [cognome] => Mazzarelli
    [ragione_sociale] => 
    [partita_iva] => 
    [codice_fiscale] => ****************
    [email] => <EMAIL>
    [telefono] => 3392720049
    [indirizzo] => via della Stazione di Ciampino 151
    [citta] => Roma
    [provincia] => RM
    [note] => sono io, ciao
)

[14-Dec-2024 18:16:21 Europe/Rome] Progetti trovati: Array
(
    [0] => Array
        (
            [id] => 1
            [nome] => Casa
            [data_creazione] => 2024-12-08
            [ultima_modifica] => 2025-04-08
            [stato] => in_corso
        )

)

[14-Dec-2024 18:16:21 Europe/Rome] Pratiche trovate: Array
(
    [0] => Array
        (
            [id] => 1
            [nome_pratica] => 1/2025
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [stato] => in_attesa
            [nome_progetto] => Casa
        )

)

[14-Dec-2024 18:16:21 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:16:21 Europe/Rome] Route dispatched
[14-Dec-2024 18:16:33 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:16:33 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:16:33 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:16:33 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:16:33 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:16:33 Europe/Rome] Router inizializzato
[14-Dec-2024 18:16:33 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:16:33 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:16:33 Europe/Rome] Route home definita
[14-Dec-2024 18:16:33 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:16:33 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:16:33 Europe/Rome] Route progetti definite
[14-Dec-2024 18:16:33 Europe/Rome] Route clienti definite
[14-Dec-2024 18:16:33 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:16:33 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 18:16:33 Europe/Rome] Route dispatched
[14-Dec-2024 18:16:36 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:16:36 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:16:36 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:16:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:16:36 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:16:36 Europe/Rome] Router inizializzato
[14-Dec-2024 18:16:36 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:16:36 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:16:36 Europe/Rome] Route home definita
[14-Dec-2024 18:16:36 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:16:36 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:16:36 Europe/Rome] Route progetti definite
[14-Dec-2024 18:16:36 Europe/Rome] Route clienti definite
[14-Dec-2024 18:16:36 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:16:36 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 18:16:36 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:16:36 Europe/Rome] Dati cliente: Array
(
    [id] => 1
    [tipo_cliente] => privato
    [nome] => Mauro
    [cognome] => Mazzarelli
    [ragione_sociale] => 
    [partita_iva] => 
    [codice_fiscale] => ****************
    [email] => <EMAIL>
    [telefono] => 3392720049
    [indirizzo] => via della Stazione di Ciampino 151
    [citta] => Roma
    [provincia] => RM
    [note] => sono io, ciao
)

[14-Dec-2024 18:16:36 Europe/Rome] Progetti trovati: Array
(
    [0] => Array
        (
            [id] => 1
            [nome] => Casa
            [data_creazione] => 2024-12-08
            [ultima_modifica] => 2025-04-08
            [stato] => in_corso
        )

)

[14-Dec-2024 18:16:36 Europe/Rome] Pratiche trovate: Array
(
    [0] => Array
        (
            [id] => 1
            [nome_pratica] => 1/2025
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [stato] => in_attesa
            [nome_progetto] => Casa
        )

)

[14-Dec-2024 18:16:36 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:16:36 Europe/Rome] Route dispatched
[14-Dec-2024 18:16:40 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:16:40 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:16:40 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:16:40 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:16:40 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:16:40 Europe/Rome] Router inizializzato
[14-Dec-2024 18:16:40 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:16:40 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:16:40 Europe/Rome] Route home definita
[14-Dec-2024 18:16:40 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:16:40 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:16:40 Europe/Rome] Route progetti definite
[14-Dec-2024 18:16:40 Europe/Rome] Route clienti definite
[14-Dec-2024 18:16:40 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:16:40 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 18:16:40 Europe/Rome] Route dispatched
[14-Dec-2024 18:16:47 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:16:47 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:16:47 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:16:47 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:16:47 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:16:47 Europe/Rome] Router inizializzato
[14-Dec-2024 18:16:47 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:16:47 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:16:47 Europe/Rome] Route home definita
[14-Dec-2024 18:16:47 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:16:47 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:16:47 Europe/Rome] Route progetti definite
[14-Dec-2024 18:16:47 Europe/Rome] Route clienti definite
[14-Dec-2024 18:16:47 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:16:47 Europe/Rome] URL richiesto: 
[14-Dec-2024 18:16:47 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:16:47 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 18:16:47 Europe/Rome] Autoloader registrato
[14-Dec-2024 18:16:47 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 18:16:47 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 18:16:47 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 18:16:47 Europe/Rome] Router inizializzato
[14-Dec-2024 18:16:47 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 18:16:47 Europe/Rome] Route profilo admin definite
[14-Dec-2024 18:16:47 Europe/Rome] Route home definita
[14-Dec-2024 18:16:47 Europe/Rome] Route dashboard definita
[14-Dec-2024 18:16:47 Europe/Rome] Route pratiche definite
[14-Dec-2024 18:16:47 Europe/Rome] Route progetti definite
[14-Dec-2024 18:16:47 Europe/Rome] Route clienti definite
[14-Dec-2024 18:16:47 Europe/Rome] Route scadenze definite
[14-Dec-2024 18:16:47 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 18:16:47 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 18:16:47 Europe/Rome] 
==================================================
[14-Dec-2024 18:16:47 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 18:16:47 Europe/Rome] ==================================================

[14-Dec-2024 18:16:47 Europe/Rome] Clienti totali: 2
[14-Dec-2024 18:16:47 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 18:16:47 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 18:16:47 Europe/Rome] Progetti totali: 1
[14-Dec-2024 18:16:47 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 18:16:47 Europe/Rome] 
==================================================
[14-Dec-2024 18:16:47 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 18:16:47 Europe/Rome] ==================================================

[14-Dec-2024 18:16:47 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 18:16:47 Europe/Rome] Route dispatched
[14-Dec-2024 20:29:57 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:29:58 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:30:00 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:30:00 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:30:00 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:30:00 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:30:00 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:30:00 Europe/Rome] Router inizializzato
[14-Dec-2024 20:30:00 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:30:00 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:30:00 Europe/Rome] Route home definita
[14-Dec-2024 20:30:00 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:30:00 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:30:00 Europe/Rome] Route progetti definite
[14-Dec-2024 20:30:00 Europe/Rome] Route clienti definite
[14-Dec-2024 20:30:00 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:30:00 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:30:00 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:30:00 Europe/Rome] Dati cliente: Array
(
    [id] => 1
    [tipo_cliente] => privato
    [nome] => Mauro
    [cognome] => Mazzarelli
    [ragione_sociale] => 
    [partita_iva] => 
    [codice_fiscale] => ****************
    [email] => <EMAIL>
    [telefono] => 3392720049
    [indirizzo] => via della Stazione di Ciampino 151
    [citta] => Roma
    [provincia] => RM
    [note] => sono io, ciao
)

[14-Dec-2024 20:30:00 Europe/Rome] Progetti trovati: Array
(
    [0] => Array
        (
            [id] => 1
            [nome] => Casa
            [data_creazione] => 2024-12-08
            [ultima_modifica] => 2025-04-08
            [stato] => in_corso
        )

)

[14-Dec-2024 20:30:00 Europe/Rome] Pratiche trovate: Array
(
    [0] => Array
        (
            [id] => 1
            [nome_pratica] => 1/2025
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [stato] => in_attesa
            [nome_progetto] => Casa
        )

)

[14-Dec-2024 20:30:00 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:30:00 Europe/Rome] Route dispatched
[14-Dec-2024 20:32:16 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:32:16 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:32:16 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:32:16 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:32:16 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:32:16 Europe/Rome] Router inizializzato
[14-Dec-2024 20:32:16 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:32:16 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:32:16 Europe/Rome] Route home definita
[14-Dec-2024 20:32:16 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:32:16 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:32:16 Europe/Rome] Route progetti definite
[14-Dec-2024 20:32:16 Europe/Rome] Route clienti definite
[14-Dec-2024 20:32:16 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:32:16 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 20:32:16 Europe/Rome] Route dispatched
[14-Dec-2024 20:32:22 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:32:22 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:32:22 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:32:22 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:32:22 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:32:22 Europe/Rome] Router inizializzato
[14-Dec-2024 20:32:22 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:32:22 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:32:22 Europe/Rome] Route home definita
[14-Dec-2024 20:32:22 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:32:22 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:32:22 Europe/Rome] Route progetti definite
[14-Dec-2024 20:32:22 Europe/Rome] Route clienti definite
[14-Dec-2024 20:32:22 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:32:22 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:32:22 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:32:22 Europe/Rome] Dati cliente: Array
(
    [id] => 1
    [tipo_cliente] => privato
    [nome] => Mauro
    [cognome] => Mazzarelli
    [ragione_sociale] => 
    [partita_iva] => 
    [codice_fiscale] => ****************
    [email] => <EMAIL>
    [telefono] => 3392720049
    [indirizzo] => via della Stazione di Ciampino 151
    [citta] => Roma
    [provincia] => RM
    [note] => sono io, ciao
)

[14-Dec-2024 20:32:22 Europe/Rome] Progetti trovati: Array
(
    [0] => Array
        (
            [id] => 1
            [nome] => Casa
            [data_creazione] => 2024-12-08
            [ultima_modifica] => 2025-04-08
            [stato] => in_corso
        )

)

[14-Dec-2024 20:32:22 Europe/Rome] Pratiche trovate: Array
(
    [0] => Array
        (
            [id] => 1
            [nome_pratica] => 1/2025
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [stato] => in_attesa
            [nome_progetto] => Casa
        )

)

[14-Dec-2024 20:32:22 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:32:22 Europe/Rome] Route dispatched
[14-Dec-2024 20:32:40 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:32:40 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:32:40 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:32:40 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:32:40 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:32:40 Europe/Rome] Router inizializzato
[14-Dec-2024 20:32:40 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:32:40 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:32:40 Europe/Rome] Route home definita
[14-Dec-2024 20:32:40 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:32:40 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:32:40 Europe/Rome] Route progetti definite
[14-Dec-2024 20:32:40 Europe/Rome] Route clienti definite
[14-Dec-2024 20:32:40 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:32:40 Europe/Rome] URL richiesto: progetti/nuovo/1
[14-Dec-2024 20:32:40 Europe/Rome] Route dispatched
[14-Dec-2024 20:32:42 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:32:42 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:32:42 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:32:42 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:32:42 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:32:42 Europe/Rome] Router inizializzato
[14-Dec-2024 20:32:42 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:32:42 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:32:42 Europe/Rome] Route home definita
[14-Dec-2024 20:32:42 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:32:42 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:32:42 Europe/Rome] Route progetti definite
[14-Dec-2024 20:32:42 Europe/Rome] Route clienti definite
[14-Dec-2024 20:32:42 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:32:42 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:32:42 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:32:42 Europe/Rome] Dati cliente: Array
(
    [id] => 1
    [tipo_cliente] => privato
    [nome] => Mauro
    [cognome] => Mazzarelli
    [ragione_sociale] => 
    [partita_iva] => 
    [codice_fiscale] => ****************
    [email] => <EMAIL>
    [telefono] => 3392720049
    [indirizzo] => via della Stazione di Ciampino 151
    [citta] => Roma
    [provincia] => RM
    [note] => sono io, ciao
)

[14-Dec-2024 20:32:42 Europe/Rome] Progetti trovati: Array
(
    [0] => Array
        (
            [id] => 1
            [nome] => Casa
            [data_creazione] => 2024-12-08
            [ultima_modifica] => 2025-04-08
            [stato] => in_corso
        )

)

[14-Dec-2024 20:32:42 Europe/Rome] Pratiche trovate: Array
(
    [0] => Array
        (
            [id] => 1
            [nome_pratica] => 1/2025
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [stato] => in_attesa
            [nome_progetto] => Casa
        )

)

[14-Dec-2024 20:32:42 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:32:42 Europe/Rome] Route dispatched
[14-Dec-2024 20:37:58 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:37:58 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:37:58 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:37:58 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:37:58 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:37:58 Europe/Rome] Router inizializzato
[14-Dec-2024 20:37:58 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:37:58 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:37:58 Europe/Rome] Route home definita
[14-Dec-2024 20:37:58 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:37:58 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:37:58 Europe/Rome] Route progetti definite
[14-Dec-2024 20:37:58 Europe/Rome] Route clienti definite
[14-Dec-2024 20:37:58 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:37:58 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:37:58 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:37:58 Europe/Rome] Dati cliente: Array
(
    [id] => 1
    [tipo_cliente] => privato
    [nome] => Mauro
    [cognome] => Mazzarelli
    [ragione_sociale] => 
    [partita_iva] => 
    [codice_fiscale] => ****************
    [email] => <EMAIL>
    [telefono] => 3392720049
    [indirizzo] => via della Stazione di Ciampino 151
    [citta] => Roma
    [provincia] => RM
    [note] => sono io, ciao
)

[14-Dec-2024 20:37:58 Europe/Rome] Progetti trovati: Array
(
    [0] => Array
        (
            [id] => 1
            [nome] => Casa
            [data_creazione] => 2024-12-08
            [ultima_modifica] => 2025-04-08
            [stato] => in_corso
        )

)

[14-Dec-2024 20:37:58 Europe/Rome] Pratiche trovate: Array
(
    [0] => Array
        (
            [id] => 1
            [nome_pratica] => 1/2025
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [stato] => in_attesa
            [nome_progetto] => Casa
        )

)

[14-Dec-2024 20:37:58 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:37:58 Europe/Rome] Route dispatched
[14-Dec-2024 20:38:00 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:38:00 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:38:00 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:38:00 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:00 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:38:00 Europe/Rome] Router inizializzato
[14-Dec-2024 20:38:00 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:38:00 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:38:00 Europe/Rome] Route home definita
[14-Dec-2024 20:38:00 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:38:00 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:38:00 Europe/Rome] Route progetti definite
[14-Dec-2024 20:38:00 Europe/Rome] Route clienti definite
[14-Dec-2024 20:38:00 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:38:00 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:38:00 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:38:00 Europe/Rome] Dati cliente: Array
(
    [id] => 1
    [tipo_cliente] => privato
    [nome] => Mauro
    [cognome] => Mazzarelli
    [ragione_sociale] => 
    [partita_iva] => 
    [codice_fiscale] => ****************
    [email] => <EMAIL>
    [telefono] => 3392720049
    [indirizzo] => via della Stazione di Ciampino 151
    [citta] => Roma
    [provincia] => RM
    [note] => sono io, ciao
)

[14-Dec-2024 20:38:00 Europe/Rome] Progetti trovati: Array
(
    [0] => Array
        (
            [id] => 1
            [nome] => Casa
            [data_creazione] => 2024-12-08
            [ultima_modifica] => 2025-04-08
            [stato] => in_corso
        )

)

[14-Dec-2024 20:38:00 Europe/Rome] Pratiche trovate: Array
(
    [0] => Array
        (
            [id] => 1
            [nome_pratica] => 1/2025
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [stato] => in_attesa
            [nome_progetto] => Casa
        )

)

[14-Dec-2024 20:38:00 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:38:00 Europe/Rome] Route dispatched
[14-Dec-2024 20:38:17 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:38:17 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:38:17 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:38:17 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:17 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:38:17 Europe/Rome] Router inizializzato
[14-Dec-2024 20:38:17 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:38:17 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:38:17 Europe/Rome] Route home definita
[14-Dec-2024 20:38:17 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:38:17 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:38:17 Europe/Rome] Route progetti definite
[14-Dec-2024 20:38:17 Europe/Rome] Route clienti definite
[14-Dec-2024 20:38:17 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:38:17 Europe/Rome] URL richiesto: logout
[14-Dec-2024 20:38:17 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:38:17 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:38:17 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:38:17 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:17 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:38:17 Europe/Rome] Router inizializzato
[14-Dec-2024 20:38:17 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:38:17 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:38:17 Europe/Rome] Route home definita
[14-Dec-2024 20:38:17 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:38:17 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:38:17 Europe/Rome] Route progetti definite
[14-Dec-2024 20:38:17 Europe/Rome] Route clienti definite
[14-Dec-2024 20:38:17 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:38:17 Europe/Rome] URL richiesto: login
[14-Dec-2024 20:38:17 Europe/Rome] Route dispatched
[14-Dec-2024 20:38:20 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:38:20 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:38:20 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:38:20 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:20 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:38:20 Europe/Rome] Router inizializzato
[14-Dec-2024 20:38:20 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:38:20 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:38:20 Europe/Rome] Route home definita
[14-Dec-2024 20:38:20 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:38:20 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:38:20 Europe/Rome] Route progetti definite
[14-Dec-2024 20:38:20 Europe/Rome] Route clienti definite
[14-Dec-2024 20:38:20 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:38:20 Europe/Rome] URL richiesto: login
[14-Dec-2024 20:38:20 Europe/Rome] Route dispatched
[14-Dec-2024 20:38:29 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:38:29 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:38:29 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:38:29 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:29 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:38:29 Europe/Rome] Router inizializzato
[14-Dec-2024 20:38:29 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:38:29 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:38:29 Europe/Rome] Route home definita
[14-Dec-2024 20:38:29 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:38:29 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:38:29 Europe/Rome] Route progetti definite
[14-Dec-2024 20:38:29 Europe/Rome] Route clienti definite
[14-Dec-2024 20:38:29 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:38:29 Europe/Rome] URL richiesto: 
[14-Dec-2024 20:38:29 Europe/Rome] Utente non autenticato, redirect al login
[14-Dec-2024 20:38:29 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:38:29 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:38:29 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:38:29 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:29 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:38:29 Europe/Rome] Router inizializzato
[14-Dec-2024 20:38:29 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:38:29 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:38:29 Europe/Rome] Route home definita
[14-Dec-2024 20:38:29 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:38:29 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:38:29 Europe/Rome] Route progetti definite
[14-Dec-2024 20:38:29 Europe/Rome] Route clienti definite
[14-Dec-2024 20:38:29 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:38:29 Europe/Rome] URL richiesto: login
[14-Dec-2024 20:38:29 Europe/Rome] Route dispatched
[14-Dec-2024 20:38:34 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:38:34 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:38:34 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:38:34 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:34 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:38:34 Europe/Rome] Router inizializzato
[14-Dec-2024 20:38:34 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:38:34 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:38:34 Europe/Rome] Route home definita
[14-Dec-2024 20:38:34 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:38:34 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:38:34 Europe/Rome] Route progetti definite
[14-Dec-2024 20:38:34 Europe/Rome] Route clienti definite
[14-Dec-2024 20:38:34 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:38:34 Europe/Rome] URL richiesto: login
[14-Dec-2024 20:38:34 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 20:38:34 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 20:38:34 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 20:38:35 Europe/Rome] Password verificata con successo
[14-Dec-2024 20:38:35 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[14-Dec-2024 20:38:35 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:38:35 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:38:35 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:38:35 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:35 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:38:35 Europe/Rome] Router inizializzato
[14-Dec-2024 20:38:35 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:38:35 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:38:35 Europe/Rome] Route home definita
[14-Dec-2024 20:38:35 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:38:35 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:38:35 Europe/Rome] Route progetti definite
[14-Dec-2024 20:38:35 Europe/Rome] Route clienti definite
[14-Dec-2024 20:38:35 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:38:35 Europe/Rome] URL richiesto: admin
[14-Dec-2024 20:38:35 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 20:38:35 Europe/Rome] Route dispatched
[14-Dec-2024 20:38:42 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:38:42 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:38:42 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:38:42 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:42 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:38:42 Europe/Rome] Router inizializzato
[14-Dec-2024 20:38:42 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:38:42 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:38:42 Europe/Rome] Route home definita
[14-Dec-2024 20:38:42 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:38:42 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:38:42 Europe/Rome] Route progetti definite
[14-Dec-2024 20:38:42 Europe/Rome] Route clienti definite
[14-Dec-2024 20:38:42 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:38:42 Europe/Rome] URL richiesto: 
[14-Dec-2024 20:38:42 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:38:42 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:38:42 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:38:42 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:38:42 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:42 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:38:42 Europe/Rome] Router inizializzato
[14-Dec-2024 20:38:42 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:38:42 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:38:42 Europe/Rome] Route home definita
[14-Dec-2024 20:38:42 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:38:42 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:38:42 Europe/Rome] Route progetti definite
[14-Dec-2024 20:38:42 Europe/Rome] Route clienti definite
[14-Dec-2024 20:38:42 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:38:42 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 20:38:42 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:38:42 Europe/Rome] 
==================================================
[14-Dec-2024 20:38:42 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 20:38:42 Europe/Rome] ==================================================

[14-Dec-2024 20:38:42 Europe/Rome] Clienti totali: 2
[14-Dec-2024 20:38:42 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 20:38:42 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 20:38:42 Europe/Rome] Progetti totali: 1
[14-Dec-2024 20:38:42 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 20:38:42 Europe/Rome] 
==================================================
[14-Dec-2024 20:38:42 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 20:38:42 Europe/Rome] ==================================================

[14-Dec-2024 20:38:42 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:38:42 Europe/Rome] Route dispatched
[14-Dec-2024 20:38:46 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:46 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:38:51 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:38:51 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:38:51 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:38:51 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:38:51 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:38:51 Europe/Rome] Router inizializzato
[14-Dec-2024 20:38:51 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:38:51 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:38:51 Europe/Rome] Route home definita
[14-Dec-2024 20:38:51 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:38:51 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:38:51 Europe/Rome] Route progetti definite
[14-Dec-2024 20:38:51 Europe/Rome] Route clienti definite
[14-Dec-2024 20:38:51 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:38:51 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:38:51 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:38:51 Europe/Rome] Dati cliente: Array
(
    [id] => 1
    [tipo_cliente] => privato
    [nome] => Mauro
    [cognome] => Mazzarelli
    [ragione_sociale] => 
    [partita_iva] => 
    [codice_fiscale] => ****************
    [email] => <EMAIL>
    [telefono] => 3392720049
    [indirizzo] => via della Stazione di Ciampino 151
    [citta] => Roma
    [provincia] => RM
    [note] => sono io, ciao
)

[14-Dec-2024 20:38:51 Europe/Rome] Progetti trovati: Array
(
    [0] => Array
        (
            [id] => 1
            [nome] => Casa
            [data_creazione] => 2024-12-08
            [ultima_modifica] => 2025-04-08
            [stato] => in_corso
        )

)

[14-Dec-2024 20:38:51 Europe/Rome] Pratiche trovate: Array
(
    [0] => Array
        (
            [id] => 1
            [nome_pratica] => 1/2025
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [stato] => in_attesa
            [nome_progetto] => Casa
        )

)

[14-Dec-2024 20:38:51 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:38:51 Europe/Rome] Route dispatched
[14-Dec-2024 20:41:26 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:41:26 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:41:26 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:41:26 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:41:26 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:41:26 Europe/Rome] Router inizializzato
[14-Dec-2024 20:41:26 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:41:26 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:41:26 Europe/Rome] Route home definita
[14-Dec-2024 20:41:26 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:41:26 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:41:26 Europe/Rome] Route progetti definite
[14-Dec-2024 20:41:26 Europe/Rome] Route clienti definite
[14-Dec-2024 20:41:26 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:41:26 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:41:26 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:41:26 Europe/Rome] Dati cliente: Array
(
    [id] => 1
    [tipo_cliente] => privato
    [nome] => Mauro
    [cognome] => Mazzarelli
    [ragione_sociale] => 
    [partita_iva] => 
    [codice_fiscale] => ****************
    [email] => <EMAIL>
    [telefono] => 3392720049
    [indirizzo] => via della Stazione di Ciampino 151
    [citta] => Roma
    [provincia] => RM
    [note] => sono io, ciao
)

[14-Dec-2024 20:41:26 Europe/Rome] Progetti trovati: Array
(
    [0] => Array
        (
            [id] => 1
            [nome] => Casa
            [data_creazione] => 2024-12-08
            [ultima_modifica] => 2025-04-08
            [stato] => in_corso
        )

)

[14-Dec-2024 20:41:26 Europe/Rome] Pratiche trovate: Array
(
    [0] => Array
        (
            [id] => 1
            [nome_pratica] => 1/2025
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [stato] => in_attesa
            [nome_progetto] => Casa
        )

)

[14-Dec-2024 20:41:26 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:41:26 Europe/Rome] Route dispatched
[14-Dec-2024 20:44:27 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:44:27 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:44:27 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:44:27 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:44:27 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:44:27 Europe/Rome] Router inizializzato
[14-Dec-2024 20:44:27 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:44:27 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:44:27 Europe/Rome] Route home definita
[14-Dec-2024 20:44:27 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:44:27 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:44:27 Europe/Rome] Route progetti definite
[14-Dec-2024 20:44:27 Europe/Rome] Route clienti definite
[14-Dec-2024 20:44:27 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:44:27 Europe/Rome] URL richiesto: 
[14-Dec-2024 20:44:27 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:44:27 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:44:27 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:44:27 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:44:27 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:44:27 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:44:27 Europe/Rome] Router inizializzato
[14-Dec-2024 20:44:27 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:44:27 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:44:27 Europe/Rome] Route home definita
[14-Dec-2024 20:44:27 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:44:27 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:44:27 Europe/Rome] Route progetti definite
[14-Dec-2024 20:44:27 Europe/Rome] Route clienti definite
[14-Dec-2024 20:44:27 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:44:27 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 20:44:27 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:44:27 Europe/Rome] 
==================================================
[14-Dec-2024 20:44:27 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 20:44:27 Europe/Rome] ==================================================

[14-Dec-2024 20:44:27 Europe/Rome] Clienti totali: 2
[14-Dec-2024 20:44:27 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 20:44:27 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 20:44:27 Europe/Rome] Progetti totali: 1
[14-Dec-2024 20:44:27 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 20:44:27 Europe/Rome] 
==================================================
[14-Dec-2024 20:44:27 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 20:44:27 Europe/Rome] ==================================================

[14-Dec-2024 20:44:27 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:44:27 Europe/Rome] Route dispatched
[14-Dec-2024 20:44:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:44:30 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:44:34 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:44:34 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:44:34 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:44:34 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:44:34 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:44:34 Europe/Rome] Router inizializzato
[14-Dec-2024 20:44:34 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:44:34 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:44:34 Europe/Rome] Route home definita
[14-Dec-2024 20:44:34 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:44:34 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:44:34 Europe/Rome] Route progetti definite
[14-Dec-2024 20:44:34 Europe/Rome] Route clienti definite
[14-Dec-2024 20:44:34 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:44:34 Europe/Rome] URL richiesto: clienti/dettagli/2
[14-Dec-2024 20:44:34 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:44:34 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:44:34 Europe/Rome] Route dispatched
[14-Dec-2024 20:44:38 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:44:38 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:44:39 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:44:39 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:44:39 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:44:39 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:44:39 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:44:39 Europe/Rome] Router inizializzato
[14-Dec-2024 20:44:39 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:44:39 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:44:39 Europe/Rome] Route home definita
[14-Dec-2024 20:44:39 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:44:39 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:44:39 Europe/Rome] Route progetti definite
[14-Dec-2024 20:44:39 Europe/Rome] Route clienti definite
[14-Dec-2024 20:44:39 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:44:39 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:44:39 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:44:39 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:44:39 Europe/Rome] Route dispatched
[14-Dec-2024 20:48:09 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:48:09 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:48:11 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:48:11 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:48:11 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:48:11 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:48:11 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:48:11 Europe/Rome] Router inizializzato
[14-Dec-2024 20:48:11 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:48:11 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:48:11 Europe/Rome] Route home definita
[14-Dec-2024 20:48:11 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:48:11 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:48:11 Europe/Rome] Route progetti definite
[14-Dec-2024 20:48:11 Europe/Rome] Route clienti definite
[14-Dec-2024 20:48:11 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:48:11 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:48:11 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:48:11 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:48:11 Europe/Rome] Route dispatched
[14-Dec-2024 20:49:38 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:49:38 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:49:41 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:49:41 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:49:41 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:49:41 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:49:41 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:49:41 Europe/Rome] Router inizializzato
[14-Dec-2024 20:49:41 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:49:41 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:49:41 Europe/Rome] Route home definita
[14-Dec-2024 20:49:41 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:49:41 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:49:41 Europe/Rome] Route progetti definite
[14-Dec-2024 20:49:41 Europe/Rome] Route clienti definite
[14-Dec-2024 20:49:41 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:49:41 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:49:41 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:49:41 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:49:41 Europe/Rome] Route dispatched
[14-Dec-2024 20:49:45 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:49:45 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:49:45 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:49:45 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:49:45 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:49:45 Europe/Rome] Router inizializzato
[14-Dec-2024 20:49:45 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:49:45 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:49:45 Europe/Rome] Route home definita
[14-Dec-2024 20:49:45 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:49:45 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:49:45 Europe/Rome] Route progetti definite
[14-Dec-2024 20:49:45 Europe/Rome] Route clienti definite
[14-Dec-2024 20:49:45 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:49:45 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 20:49:45 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:49:48 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:49:48 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:49:48 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:49:48 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:49:48 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:49:48 Europe/Rome] Router inizializzato
[14-Dec-2024 20:49:48 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:49:48 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:49:48 Europe/Rome] Route home definita
[14-Dec-2024 20:49:48 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:49:48 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:49:48 Europe/Rome] Route progetti definite
[14-Dec-2024 20:49:48 Europe/Rome] Route clienti definite
[14-Dec-2024 20:49:48 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:49:48 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 20:49:48 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:49:48 Europe/Rome] Errore nel caricamento della vista: Vista non trovata: C:\xampp\htdocs\studio_tecnico/views/progetti/dettagli.php
[14-Dec-2024 20:49:48 Europe/Rome] Vista non trovata: C:\xampp\htdocs\studio_tecnico/views/progetti/dettagli.php
[14-Dec-2024 20:49:48 Europe/Rome] #0 C:\xampp\htdocs\studio_tecnico\app\controllers\ProgettiController.php(277): App\Core\Controller->view('progetti/dettag...', Array)
#1 [internal function]: App\Controllers\ProgettiController->dettagli('1')
#2 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#3 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('progetti/dettag...')
#4 {main}
[14-Dec-2024 20:49:57 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:49:57 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:49:57 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:49:57 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:49:57 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:49:57 Europe/Rome] Router inizializzato
[14-Dec-2024 20:49:57 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:49:57 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:49:57 Europe/Rome] Route home definita
[14-Dec-2024 20:49:57 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:49:57 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:49:57 Europe/Rome] Route progetti definite
[14-Dec-2024 20:49:57 Europe/Rome] Route clienti definite
[14-Dec-2024 20:49:57 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:49:57 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 20:49:57 Europe/Rome] Utente autenticato: admin
[2024-12-14 20:49:57][🔴 FATAL][admin@::1][error_log.php:105] Eccezione non catturata: Call to undefined method App\Controllers\PraticheController::view()
Stack trace:
#0 [internal function]: App\Controllers\PraticheController->dettagli('1')
#1 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#2 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('pratiche/dettag...')
#3 {main}
[14-Dec-2024 20:50:09 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:50:09 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:50:09 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:50:09 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:50:09 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:50:09 Europe/Rome] Router inizializzato
[14-Dec-2024 20:50:09 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:50:09 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:50:09 Europe/Rome] Route home definita
[14-Dec-2024 20:50:09 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:50:09 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:50:09 Europe/Rome] Route progetti definite
[14-Dec-2024 20:50:09 Europe/Rome] Route clienti definite
[14-Dec-2024 20:50:09 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:50:09 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 20:50:09 Europe/Rome] Utente autenticato: admin
[2024-12-14 20:50:09][🔴 FATAL][admin@::1][error_log.php:105] Eccezione non catturata: Call to undefined method App\Controllers\PraticheController::view()
Stack trace:
#0 [internal function]: App\Controllers\PraticheController->dettagli('1')
#1 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#2 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('pratiche/dettag...')
#3 {main}
[14-Dec-2024 20:50:19 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:50:19 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:50:21 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:50:21 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:50:21 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:50:21 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:50:21 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:50:21 Europe/Rome] Router inizializzato
[14-Dec-2024 20:50:21 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:50:21 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:50:21 Europe/Rome] Route home definita
[14-Dec-2024 20:50:21 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:50:21 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:50:21 Europe/Rome] Route progetti definite
[14-Dec-2024 20:50:21 Europe/Rome] Route clienti definite
[14-Dec-2024 20:50:21 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:50:21 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:50:21 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:50:21 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:50:21 Europe/Rome] Route dispatched
[14-Dec-2024 20:50:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:50:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:50:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:50:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:50:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:50:24 Europe/Rome] Router inizializzato
[14-Dec-2024 20:50:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:50:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:50:24 Europe/Rome] Route home definita
[14-Dec-2024 20:50:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:50:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:50:24 Europe/Rome] Route progetti definite
[14-Dec-2024 20:50:24 Europe/Rome] Route clienti definite
[14-Dec-2024 20:50:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:50:24 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 20:50:24 Europe/Rome] Utente autenticato: admin
[2024-12-14 20:50:24][🔴 FATAL][admin@::1][error_log.php:105] Eccezione non catturata: Call to undefined method App\Controllers\PraticheController::view()
Stack trace:
#0 [internal function]: App\Controllers\PraticheController->dettagli('1')
#1 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#2 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('pratiche/dettag...')
#3 {main}
[14-Dec-2024 20:51:23 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:51:23 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:51:25 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:51:25 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:51:25 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:51:25 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:51:25 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:51:25 Europe/Rome] Router inizializzato
[14-Dec-2024 20:51:25 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:51:25 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:51:25 Europe/Rome] Route home definita
[14-Dec-2024 20:51:25 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:51:25 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:51:25 Europe/Rome] Route progetti definite
[14-Dec-2024 20:51:25 Europe/Rome] Route clienti definite
[14-Dec-2024 20:51:25 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:51:25 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:51:25 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:51:25 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:51:25 Europe/Rome] Route dispatched
[14-Dec-2024 20:51:27 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:51:27 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:51:27 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:51:27 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:51:27 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:51:27 Europe/Rome] Router inizializzato
[14-Dec-2024 20:51:27 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:51:27 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:51:27 Europe/Rome] Route home definita
[14-Dec-2024 20:51:27 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:51:27 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:51:27 Europe/Rome] Route progetti definite
[14-Dec-2024 20:51:27 Europe/Rome] Route clienti definite
[14-Dec-2024 20:51:27 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:51:27 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 20:51:27 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:51:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:51:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:51:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:51:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:51:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:51:30 Europe/Rome] Router inizializzato
[14-Dec-2024 20:51:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:51:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:51:30 Europe/Rome] Route home definita
[14-Dec-2024 20:51:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:51:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:51:30 Europe/Rome] Route progetti definite
[14-Dec-2024 20:51:30 Europe/Rome] Route clienti definite
[14-Dec-2024 20:51:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:51:30 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 20:51:30 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:51:35 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:51:35 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:51:35 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:51:35 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:51:35 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:51:35 Europe/Rome] Router inizializzato
[14-Dec-2024 20:51:35 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:51:35 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:51:35 Europe/Rome] Route home definita
[14-Dec-2024 20:51:35 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:51:35 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:51:35 Europe/Rome] Route progetti definite
[14-Dec-2024 20:51:35 Europe/Rome] Route clienti definite
[14-Dec-2024 20:51:35 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:51:35 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 20:51:35 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:51:35 Europe/Rome] Errore nel caricamento della vista: Vista non trovata: C:\xampp\htdocs\studio_tecnico/views/pratiche/dettagli.php
[14-Dec-2024 20:51:35 Europe/Rome] Vista non trovata: C:\xampp\htdocs\studio_tecnico/views/pratiche/dettagli.php
[14-Dec-2024 20:51:35 Europe/Rome] #0 C:\xampp\htdocs\studio_tecnico\app\controllers\PraticheController.php(185): App\Core\Controller->view('pratiche/dettag...', Array)
#1 [internal function]: App\Controllers\PraticheController->dettagli('1')
#2 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#3 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('pratiche/dettag...')
#4 {main}
[14-Dec-2024 20:52:01 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:52:01 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:52:01 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:52:01 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:52:01 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:52:01 Europe/Rome] Router inizializzato
[14-Dec-2024 20:52:01 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:52:01 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:52:01 Europe/Rome] Route home definita
[14-Dec-2024 20:52:01 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:52:01 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:52:01 Europe/Rome] Route progetti definite
[14-Dec-2024 20:52:01 Europe/Rome] Route clienti definite
[14-Dec-2024 20:52:01 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:52:01 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 20:52:01 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:52:01 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 20:52:01 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 20:52:01 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 20:52:01 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 20:52:01 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => cila
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 20:52:01 Europe/Rome] Route dispatched
[14-Dec-2024 20:52:02 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:52:02 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => cila
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 20:52:02 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 20:52:02 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 20:52:26 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:52:26 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:52:29 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:52:29 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:52:29 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:52:29 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:52:29 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:52:29 Europe/Rome] Router inizializzato
[14-Dec-2024 20:52:29 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:52:29 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:52:29 Europe/Rome] Route home definita
[14-Dec-2024 20:52:29 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:52:29 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:52:29 Europe/Rome] Route progetti definite
[14-Dec-2024 20:52:29 Europe/Rome] Route clienti definite
[14-Dec-2024 20:52:29 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:52:29 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:52:29 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:52:29 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:52:29 Europe/Rome] Route dispatched
[14-Dec-2024 20:52:34 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:52:34 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:52:34 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:52:34 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:52:34 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:52:34 Europe/Rome] Router inizializzato
[14-Dec-2024 20:52:34 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:52:34 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:52:34 Europe/Rome] Route home definita
[14-Dec-2024 20:52:34 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:52:34 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:52:34 Europe/Rome] Route progetti definite
[14-Dec-2024 20:52:34 Europe/Rome] Route clienti definite
[14-Dec-2024 20:52:34 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:52:34 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 20:52:34 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:53:23 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:53:23 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:53:23 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:53:23 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:53:23 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:53:23 Europe/Rome] Router inizializzato
[14-Dec-2024 20:53:23 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:53:23 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:53:23 Europe/Rome] Route home definita
[14-Dec-2024 20:53:23 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:53:23 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:53:23 Europe/Rome] Route progetti definite
[14-Dec-2024 20:53:23 Europe/Rome] Route clienti definite
[14-Dec-2024 20:53:23 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:53:23 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 20:53:23 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:56:26 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:56:26 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:56:28 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:56:28 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:56:28 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:56:28 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:56:28 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:56:28 Europe/Rome] Router inizializzato
[14-Dec-2024 20:56:28 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:56:28 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:56:28 Europe/Rome] Route home definita
[14-Dec-2024 20:56:28 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:56:28 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:56:28 Europe/Rome] Route progetti definite
[14-Dec-2024 20:56:28 Europe/Rome] Route clienti definite
[14-Dec-2024 20:56:28 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:56:28 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 20:56:28 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:56:28 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:56:28 Europe/Rome] Route dispatched
[14-Dec-2024 20:56:31 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:56:31 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:56:31 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:56:31 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:56:31 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:56:31 Europe/Rome] Router inizializzato
[14-Dec-2024 20:56:31 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:56:31 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:56:31 Europe/Rome] Route home definita
[14-Dec-2024 20:56:31 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:56:31 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:56:31 Europe/Rome] Route progetti definite
[14-Dec-2024 20:56:31 Europe/Rome] Route clienti definite
[14-Dec-2024 20:56:31 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:56:31 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 20:56:31 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:56:32 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:56:32 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:56:32 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:56:32 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:56:32 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:56:32 Europe/Rome] Router inizializzato
[14-Dec-2024 20:56:32 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:56:32 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:56:32 Europe/Rome] Route home definita
[14-Dec-2024 20:56:32 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:56:32 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:56:32 Europe/Rome] Route progetti definite
[14-Dec-2024 20:56:32 Europe/Rome] Route clienti definite
[14-Dec-2024 20:56:32 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:56:32 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 20:56:32 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:56:32 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:56:32 Europe/Rome] Route dispatched
[14-Dec-2024 20:56:57 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:56:57 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:56:57 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:56:57 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:56:57 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:56:57 Europe/Rome] Router inizializzato
[14-Dec-2024 20:56:57 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:56:57 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:56:57 Europe/Rome] Route home definita
[14-Dec-2024 20:56:57 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:56:57 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:56:57 Europe/Rome] Route progetti definite
[14-Dec-2024 20:56:57 Europe/Rome] Route clienti definite
[14-Dec-2024 20:56:57 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:56:57 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 20:56:57 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:56:59 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:56:59 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:56:59 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:56:59 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:56:59 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:56:59 Europe/Rome] Router inizializzato
[14-Dec-2024 20:56:59 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:56:59 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:56:59 Europe/Rome] Route home definita
[14-Dec-2024 20:56:59 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:56:59 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:56:59 Europe/Rome] Route progetti definite
[14-Dec-2024 20:56:59 Europe/Rome] Route clienti definite
[14-Dec-2024 20:56:59 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:56:59 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 20:56:59 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:56:59 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:56:59 Europe/Rome] Route dispatched
[14-Dec-2024 20:57:58 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:57:58 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:57:58 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:57:58 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:57:58 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:57:58 Europe/Rome] Router inizializzato
[14-Dec-2024 20:57:58 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:57:58 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:57:58 Europe/Rome] Route home definita
[14-Dec-2024 20:57:58 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:57:58 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:57:58 Europe/Rome] Route progetti definite
[14-Dec-2024 20:57:58 Europe/Rome] Route clienti definite
[14-Dec-2024 20:57:58 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:57:58 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 20:57:58 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:57:58 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:57:58 Europe/Rome] Route dispatched
[14-Dec-2024 20:58:21 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:58:21 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:58:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:58:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:58:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:58:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:58:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:58:30 Europe/Rome] Router inizializzato
[14-Dec-2024 20:58:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:58:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:58:30 Europe/Rome] Route home definita
[14-Dec-2024 20:58:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:58:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:58:30 Europe/Rome] Route progetti definite
[14-Dec-2024 20:58:30 Europe/Rome] Route clienti definite
[14-Dec-2024 20:58:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:58:30 Europe/Rome] URL richiesto: 
[14-Dec-2024 20:58:30 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:58:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:58:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:58:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:58:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:58:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:58:30 Europe/Rome] Router inizializzato
[14-Dec-2024 20:58:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:58:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:58:30 Europe/Rome] Route home definita
[14-Dec-2024 20:58:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:58:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:58:30 Europe/Rome] Route progetti definite
[14-Dec-2024 20:58:30 Europe/Rome] Route clienti definite
[14-Dec-2024 20:58:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:58:30 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 20:58:30 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:58:30 Europe/Rome] 
==================================================
[14-Dec-2024 20:58:30 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 20:58:30 Europe/Rome] ==================================================

[14-Dec-2024 20:58:30 Europe/Rome] Clienti totali: 2
[14-Dec-2024 20:58:30 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 20:58:30 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 20:58:30 Europe/Rome] Progetti totali: 1
[14-Dec-2024 20:58:30 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 20:58:30 Europe/Rome] 
==================================================
[14-Dec-2024 20:58:30 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 20:58:30 Europe/Rome] ==================================================

[14-Dec-2024 20:58:30 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:58:30 Europe/Rome] Route dispatched
[14-Dec-2024 20:58:32 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:58:32 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:58:44 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:58:44 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:58:44 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:58:44 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:58:44 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:58:44 Europe/Rome] Router inizializzato
[14-Dec-2024 20:58:44 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:58:44 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:58:44 Europe/Rome] Route home definita
[14-Dec-2024 20:58:44 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:58:44 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:58:44 Europe/Rome] Route progetti definite
[14-Dec-2024 20:58:44 Europe/Rome] Route clienti definite
[14-Dec-2024 20:58:44 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:58:44 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 20:58:44 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:58:44 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:58:44 Europe/Rome] Route dispatched
[14-Dec-2024 20:58:45 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:58:45 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:58:45 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:58:45 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:58:45 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:58:45 Europe/Rome] Router inizializzato
[14-Dec-2024 20:58:45 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:58:45 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:58:45 Europe/Rome] Route home definita
[14-Dec-2024 20:58:45 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:58:45 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:58:45 Europe/Rome] Route progetti definite
[14-Dec-2024 20:58:45 Europe/Rome] Route clienti definite
[14-Dec-2024 20:58:45 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:58:45 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 20:58:45 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:58:45 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 20:58:45 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 20:58:45 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 20:58:45 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 20:58:45 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => cila
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 20:58:45 Europe/Rome] Route dispatched
[14-Dec-2024 20:58:45 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:58:45 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => cila
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 20:58:45 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 20:58:45 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 20:58:45 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 20:58:45 Europe/Rome] Autoloader registrato
[14-Dec-2024 20:58:45 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 20:58:45 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:58:45 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 20:58:45 Europe/Rome] Router inizializzato
[14-Dec-2024 20:58:45 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 20:58:45 Europe/Rome] Route profilo admin definite
[14-Dec-2024 20:58:45 Europe/Rome] Route home definita
[14-Dec-2024 20:58:45 Europe/Rome] Route dashboard definita
[14-Dec-2024 20:58:45 Europe/Rome] Route pratiche definite
[14-Dec-2024 20:58:45 Europe/Rome] Route progetti definite
[14-Dec-2024 20:58:45 Europe/Rome] Route clienti definite
[14-Dec-2024 20:58:45 Europe/Rome] Route scadenze definite
[14-Dec-2024 20:58:45 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 20:58:45 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 20:58:45 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 20:58:45 Europe/Rome] Route dispatched
[14-Dec-2024 20:59:01 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 20:59:01 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:00:50 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:00:50 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:00:50 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:00:50 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:00:50 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:00:50 Europe/Rome] Router inizializzato
[14-Dec-2024 21:00:50 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:00:50 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:00:50 Europe/Rome] Route home definita
[14-Dec-2024 21:00:50 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:00:50 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:00:50 Europe/Rome] Route progetti definite
[14-Dec-2024 21:00:50 Europe/Rome] Route clienti definite
[14-Dec-2024 21:00:50 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:00:50 Europe/Rome] URL richiesto: 
[14-Dec-2024 21:00:50 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:00:50 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:00:50 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:00:50 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:00:50 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:00:50 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:00:50 Europe/Rome] Router inizializzato
[14-Dec-2024 21:00:50 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:00:50 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:00:50 Europe/Rome] Route home definita
[14-Dec-2024 21:00:50 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:00:50 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:00:50 Europe/Rome] Route progetti definite
[14-Dec-2024 21:00:50 Europe/Rome] Route clienti definite
[14-Dec-2024 21:00:50 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:00:50 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 21:00:50 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:00:50 Europe/Rome] 
==================================================
[14-Dec-2024 21:00:50 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 21:00:50 Europe/Rome] ==================================================

[14-Dec-2024 21:00:50 Europe/Rome] Clienti totali: 2
[14-Dec-2024 21:00:50 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 21:00:50 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 21:00:50 Europe/Rome] Progetti totali: 1
[14-Dec-2024 21:00:50 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 21:00:50 Europe/Rome] 
==================================================
[14-Dec-2024 21:00:50 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 21:00:50 Europe/Rome] ==================================================

[14-Dec-2024 21:00:50 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:00:50 Europe/Rome] Route dispatched
[14-Dec-2024 21:00:58 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:00:58 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:01:03 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:01:03 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:01:03 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:01:03 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:01:03 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:01:03 Europe/Rome] Router inizializzato
[14-Dec-2024 21:01:03 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:01:03 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:01:03 Europe/Rome] Route home definita
[14-Dec-2024 21:01:03 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:01:03 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:01:03 Europe/Rome] Route progetti definite
[14-Dec-2024 21:01:03 Europe/Rome] Route clienti definite
[14-Dec-2024 21:01:03 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:01:03 Europe/Rome] URL richiesto: clienti/dettagli/2
[14-Dec-2024 21:01:03 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:01:03 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:01:03 Europe/Rome] Route dispatched
[14-Dec-2024 21:01:09 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:01:09 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:01:29 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:01:29 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:01:31 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:01:31 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:01:31 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:01:31 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:01:31 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:01:31 Europe/Rome] Router inizializzato
[14-Dec-2024 21:01:31 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:01:31 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:01:31 Europe/Rome] Route home definita
[14-Dec-2024 21:01:31 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:01:31 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:01:31 Europe/Rome] Route progetti definite
[14-Dec-2024 21:01:31 Europe/Rome] Route clienti definite
[14-Dec-2024 21:01:31 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:01:31 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 21:01:31 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:01:31 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:01:31 Europe/Rome] Route dispatched
[14-Dec-2024 21:01:45 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:01:45 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:01:45 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:01:45 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:01:45 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:01:45 Europe/Rome] Router inizializzato
[14-Dec-2024 21:01:45 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:01:45 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:01:45 Europe/Rome] Route home definita
[14-Dec-2024 21:01:45 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:01:45 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:01:45 Europe/Rome] Route progetti definite
[14-Dec-2024 21:01:45 Europe/Rome] Route clienti definite
[14-Dec-2024 21:01:45 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:01:45 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 21:01:45 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:01:45 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 21:01:45 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 21:01:45 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 21:01:45 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 21:01:45 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => cila
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 21:01:45 Europe/Rome] Route dispatched
[14-Dec-2024 21:01:45 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:01:45 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => cila
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 21:01:45 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 21:01:45 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 21:01:46 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:01:46 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:01:46 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:01:46 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:01:46 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:01:46 Europe/Rome] Router inizializzato
[14-Dec-2024 21:01:46 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:01:46 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:01:46 Europe/Rome] Route home definita
[14-Dec-2024 21:01:46 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:01:46 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:01:46 Europe/Rome] Route progetti definite
[14-Dec-2024 21:01:46 Europe/Rome] Route clienti definite
[14-Dec-2024 21:01:46 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:01:46 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 21:01:46 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:01:51 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:01:51 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:01:51 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:01:51 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:01:51 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:01:51 Europe/Rome] Router inizializzato
[14-Dec-2024 21:01:51 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:01:51 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:01:51 Europe/Rome] Route home definita
[14-Dec-2024 21:01:51 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:01:51 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:01:51 Europe/Rome] Route progetti definite
[14-Dec-2024 21:01:51 Europe/Rome] Route clienti definite
[14-Dec-2024 21:01:51 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:01:51 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 21:01:51 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:01:51 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:01:51 Europe/Rome] Route dispatched
[14-Dec-2024 21:02:14 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:02:14 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:02:14 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:02:14 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:02:14 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:02:14 Europe/Rome] Router inizializzato
[14-Dec-2024 21:02:14 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:02:14 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:02:14 Europe/Rome] Route home definita
[14-Dec-2024 21:02:14 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:02:14 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:02:14 Europe/Rome] Route progetti definite
[14-Dec-2024 21:02:14 Europe/Rome] Route clienti definite
[14-Dec-2024 21:02:14 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:02:14 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 21:02:14 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:02:21 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:02:21 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:02:23 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:02:23 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:02:23 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:02:23 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:02:23 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:02:23 Europe/Rome] Router inizializzato
[14-Dec-2024 21:02:23 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:02:23 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:02:23 Europe/Rome] Route home definita
[14-Dec-2024 21:02:23 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:02:23 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:02:23 Europe/Rome] Route progetti definite
[14-Dec-2024 21:02:23 Europe/Rome] Route clienti definite
[14-Dec-2024 21:02:23 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:02:23 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 21:02:23 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:02:23 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:02:23 Europe/Rome] Route dispatched
[14-Dec-2024 21:02:28 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:02:28 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:02:28 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:02:28 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:02:28 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:02:28 Europe/Rome] Router inizializzato
[14-Dec-2024 21:02:28 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:02:28 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:02:28 Europe/Rome] Route home definita
[14-Dec-2024 21:02:28 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:02:28 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:02:28 Europe/Rome] Route progetti definite
[14-Dec-2024 21:02:28 Europe/Rome] Route clienti definite
[14-Dec-2024 21:02:28 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:02:28 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 21:02:28 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:02:28 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:02:28 Europe/Rome] Route dispatched
[14-Dec-2024 21:02:29 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:02:29 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:02:29 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:02:29 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:02:29 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:02:29 Europe/Rome] Router inizializzato
[14-Dec-2024 21:02:29 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:02:29 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:02:29 Europe/Rome] Route home definita
[14-Dec-2024 21:02:29 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:02:29 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:02:29 Europe/Rome] Route progetti definite
[14-Dec-2024 21:02:29 Europe/Rome] Route clienti definite
[14-Dec-2024 21:02:29 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:02:29 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 21:02:29 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:02:29 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 21:02:29 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 21:02:29 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 21:02:29 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 21:02:29 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => cila
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 21:02:29 Europe/Rome] Route dispatched
[14-Dec-2024 21:02:29 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:02:29 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => cila
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 21:02:29 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 21:02:29 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 21:02:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:02:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:02:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:02:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:02:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:02:30 Europe/Rome] Router inizializzato
[14-Dec-2024 21:02:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:02:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:02:30 Europe/Rome] Route home definita
[14-Dec-2024 21:02:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:02:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:02:30 Europe/Rome] Route progetti definite
[14-Dec-2024 21:02:30 Europe/Rome] Route clienti definite
[14-Dec-2024 21:02:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:02:30 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 21:02:30 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:02:40 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:02:40 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:02:41 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:02:41 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:02:41 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:02:41 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:02:41 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:02:41 Europe/Rome] Router inizializzato
[14-Dec-2024 21:02:41 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:02:41 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:02:41 Europe/Rome] Route home definita
[14-Dec-2024 21:02:41 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:02:41 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:02:41 Europe/Rome] Route progetti definite
[14-Dec-2024 21:02:41 Europe/Rome] Route clienti definite
[14-Dec-2024 21:02:41 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:02:41 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 21:02:41 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:02:41 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:02:41 Europe/Rome] Route dispatched
[14-Dec-2024 21:02:44 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:02:44 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:02:44 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:02:44 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:02:44 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:02:44 Europe/Rome] Router inizializzato
[14-Dec-2024 21:02:44 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:02:44 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:02:44 Europe/Rome] Route home definita
[14-Dec-2024 21:02:44 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:02:44 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:02:44 Europe/Rome] Route progetti definite
[14-Dec-2024 21:02:44 Europe/Rome] Route clienti definite
[14-Dec-2024 21:02:44 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:02:44 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 21:02:44 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:02:48 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:02:48 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:02:48 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:02:48 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:02:48 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:02:48 Europe/Rome] Router inizializzato
[14-Dec-2024 21:02:48 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:02:48 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:02:48 Europe/Rome] Route home definita
[14-Dec-2024 21:02:48 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:02:48 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:02:48 Europe/Rome] Route progetti definite
[14-Dec-2024 21:02:48 Europe/Rome] Route clienti definite
[14-Dec-2024 21:02:48 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:02:48 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 21:02:48 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:02:48 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:02:48 Europe/Rome] Route dispatched
[14-Dec-2024 21:05:28 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:05:28 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:05:28 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:05:28 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:05:28 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:05:28 Europe/Rome] Router inizializzato
[14-Dec-2024 21:05:28 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:05:28 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:05:28 Europe/Rome] Route home definita
[14-Dec-2024 21:05:28 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:05:28 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:05:28 Europe/Rome] Route progetti definite
[14-Dec-2024 21:05:28 Europe/Rome] Route clienti definite
[14-Dec-2024 21:05:28 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:05:28 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 21:05:28 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:05:32 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:05:32 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:05:32 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:05:32 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:05:32 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:05:32 Europe/Rome] Router inizializzato
[14-Dec-2024 21:05:32 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:05:32 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:05:32 Europe/Rome] Route home definita
[14-Dec-2024 21:05:32 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:05:32 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:05:32 Europe/Rome] Route progetti definite
[14-Dec-2024 21:05:32 Europe/Rome] Route clienti definite
[14-Dec-2024 21:05:32 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:05:32 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 21:05:32 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:05:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:05:37 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:05:38 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:05:38 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:05:38 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:05:38 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:05:38 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:05:38 Europe/Rome] Router inizializzato
[14-Dec-2024 21:05:38 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:05:38 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:05:38 Europe/Rome] Route home definita
[14-Dec-2024 21:05:38 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:05:38 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:05:38 Europe/Rome] Route progetti definite
[14-Dec-2024 21:05:38 Europe/Rome] Route clienti definite
[14-Dec-2024 21:05:38 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:05:38 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 21:05:38 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:05:38 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:05:38 Europe/Rome] Route dispatched
[14-Dec-2024 21:05:43 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:05:43 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:05:43 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:05:43 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:05:43 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:05:43 Europe/Rome] Router inizializzato
[14-Dec-2024 21:05:43 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:05:43 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:05:43 Europe/Rome] Route home definita
[14-Dec-2024 21:05:43 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:05:43 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:05:43 Europe/Rome] Route progetti definite
[14-Dec-2024 21:05:43 Europe/Rome] Route clienti definite
[14-Dec-2024 21:05:43 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:05:43 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 21:05:43 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:19 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:06:19 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:06:19 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:06:19 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:19 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:06:19 Europe/Rome] Router inizializzato
[14-Dec-2024 21:06:19 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:06:19 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:06:19 Europe/Rome] Route home definita
[14-Dec-2024 21:06:19 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:06:19 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:06:19 Europe/Rome] Route progetti definite
[14-Dec-2024 21:06:19 Europe/Rome] Route clienti definite
[14-Dec-2024 21:06:19 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:06:19 Europe/Rome] URL richiesto: 
[14-Dec-2024 21:06:19 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:19 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:06:19 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:06:19 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:06:19 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:19 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:06:19 Europe/Rome] Router inizializzato
[14-Dec-2024 21:06:19 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:06:19 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:06:19 Europe/Rome] Route home definita
[14-Dec-2024 21:06:19 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:06:19 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:06:19 Europe/Rome] Route progetti definite
[14-Dec-2024 21:06:19 Europe/Rome] Route clienti definite
[14-Dec-2024 21:06:19 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:06:19 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 21:06:19 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:19 Europe/Rome] 
==================================================
[14-Dec-2024 21:06:19 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 21:06:19 Europe/Rome] ==================================================

[14-Dec-2024 21:06:19 Europe/Rome] Clienti totali: 2
[14-Dec-2024 21:06:19 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 21:06:19 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 21:06:19 Europe/Rome] Progetti totali: 1
[14-Dec-2024 21:06:19 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 21:06:19 Europe/Rome] 
==================================================
[14-Dec-2024 21:06:19 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 21:06:19 Europe/Rome] ==================================================

[14-Dec-2024 21:06:19 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:19 Europe/Rome] Route dispatched
[14-Dec-2024 21:06:23 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:23 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:26 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:06:26 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:06:26 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:06:26 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:26 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:06:26 Europe/Rome] Router inizializzato
[14-Dec-2024 21:06:26 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:06:26 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:06:26 Europe/Rome] Route home definita
[14-Dec-2024 21:06:26 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:06:26 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:06:26 Europe/Rome] Route progetti definite
[14-Dec-2024 21:06:26 Europe/Rome] Route clienti definite
[14-Dec-2024 21:06:26 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:06:26 Europe/Rome] URL richiesto: clienti/dettagli/2
[14-Dec-2024 21:06:26 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:26 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:26 Europe/Rome] Route dispatched
[14-Dec-2024 21:06:28 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:28 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:06:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:06:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:06:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:06:30 Europe/Rome] Router inizializzato
[14-Dec-2024 21:06:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:06:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:06:30 Europe/Rome] Route home definita
[14-Dec-2024 21:06:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:06:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:06:30 Europe/Rome] Route progetti definite
[14-Dec-2024 21:06:30 Europe/Rome] Route clienti definite
[14-Dec-2024 21:06:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:06:30 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 21:06:30 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:30 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:30 Europe/Rome] Route dispatched
[14-Dec-2024 21:06:32 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:06:32 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:06:32 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:06:32 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:32 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:06:32 Europe/Rome] Router inizializzato
[14-Dec-2024 21:06:32 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:06:32 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:06:32 Europe/Rome] Route home definita
[14-Dec-2024 21:06:32 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:06:32 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:06:32 Europe/Rome] Route progetti definite
[14-Dec-2024 21:06:32 Europe/Rome] Route clienti definite
[14-Dec-2024 21:06:32 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:06:32 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 21:06:32 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:32 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:32 Europe/Rome] Route dispatched
[14-Dec-2024 21:06:39 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:06:39 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:06:39 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:06:39 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:39 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:06:39 Europe/Rome] Router inizializzato
[14-Dec-2024 21:06:39 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:06:39 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:06:39 Europe/Rome] Route home definita
[14-Dec-2024 21:06:39 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:06:39 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:06:39 Europe/Rome] Route progetti definite
[14-Dec-2024 21:06:39 Europe/Rome] Route clienti definite
[14-Dec-2024 21:06:39 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:06:39 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 21:06:39 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:39 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:39 Europe/Rome] Route dispatched
[14-Dec-2024 21:06:42 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:06:42 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:06:42 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:06:42 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:42 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:06:42 Europe/Rome] Router inizializzato
[14-Dec-2024 21:06:42 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:06:42 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:06:42 Europe/Rome] Route home definita
[14-Dec-2024 21:06:42 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:06:42 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:06:42 Europe/Rome] Route progetti definite
[14-Dec-2024 21:06:42 Europe/Rome] Route clienti definite
[14-Dec-2024 21:06:42 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:06:42 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 21:06:42 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:42 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:42 Europe/Rome] Route dispatched
[14-Dec-2024 21:06:46 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:06:46 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:06:46 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:06:46 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:46 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:06:46 Europe/Rome] Router inizializzato
[14-Dec-2024 21:06:46 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:06:46 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:06:46 Europe/Rome] Route home definita
[14-Dec-2024 21:06:46 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:06:46 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:06:46 Europe/Rome] Route progetti definite
[14-Dec-2024 21:06:46 Europe/Rome] Route clienti definite
[14-Dec-2024 21:06:46 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:06:46 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 21:06:46 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:46 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:46 Europe/Rome] Route dispatched
[14-Dec-2024 21:06:49 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:06:49 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:06:49 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:06:49 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:49 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:06:49 Europe/Rome] Router inizializzato
[14-Dec-2024 21:06:49 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:06:49 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:06:49 Europe/Rome] Route home definita
[14-Dec-2024 21:06:49 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:06:49 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:06:49 Europe/Rome] Route progetti definite
[14-Dec-2024 21:06:49 Europe/Rome] Route clienti definite
[14-Dec-2024 21:06:49 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:06:49 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 21:06:49 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:49 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 21:06:49 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 21:06:49 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 21:06:49 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 21:06:49 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => cila
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 21:06:49 Europe/Rome] Route dispatched
[14-Dec-2024 21:06:49 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:49 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => cila
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 21:06:49 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 21:06:49 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 21:06:51 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:06:51 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:06:51 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:06:51 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:51 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:06:51 Europe/Rome] Router inizializzato
[14-Dec-2024 21:06:51 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:06:51 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:06:51 Europe/Rome] Route home definita
[14-Dec-2024 21:06:51 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:06:51 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:06:51 Europe/Rome] Route progetti definite
[14-Dec-2024 21:06:51 Europe/Rome] Route clienti definite
[14-Dec-2024 21:06:51 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:06:51 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 21:06:51 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:51 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:51 Europe/Rome] Route dispatched
[14-Dec-2024 21:06:54 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:06:54 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:06:54 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:06:54 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:06:54 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:06:54 Europe/Rome] Router inizializzato
[14-Dec-2024 21:06:54 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:06:54 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:06:54 Europe/Rome] Route home definita
[14-Dec-2024 21:06:54 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:06:54 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:06:54 Europe/Rome] Route progetti definite
[14-Dec-2024 21:06:54 Europe/Rome] Route clienti definite
[14-Dec-2024 21:06:54 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:06:54 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 21:06:54 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 21:06:54 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 21:06:54 Europe/Rome] Route dispatched
[14-Dec-2024 21:07:00 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:07:00 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:07:00 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:07:00 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:07:00 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:07:00 Europe/Rome] Router inizializzato
[14-Dec-2024 21:07:00 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:07:00 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:07:00 Europe/Rome] Route home definita
[14-Dec-2024 21:07:00 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:07:00 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:07:00 Europe/Rome] Route progetti definite
[14-Dec-2024 21:07:00 Europe/Rome] Route clienti definite
[14-Dec-2024 21:07:00 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:07:00 Europe/Rome] URL richiesto: logout
[14-Dec-2024 21:07:00 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:07:00 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:07:00 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:07:00 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:07:00 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:07:00 Europe/Rome] Router inizializzato
[14-Dec-2024 21:07:00 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:07:00 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:07:00 Europe/Rome] Route home definita
[14-Dec-2024 21:07:00 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:07:00 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:07:00 Europe/Rome] Route progetti definite
[14-Dec-2024 21:07:00 Europe/Rome] Route clienti definite
[14-Dec-2024 21:07:00 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:07:00 Europe/Rome] URL richiesto: login
[14-Dec-2024 21:07:00 Europe/Rome] Route dispatched
[14-Dec-2024 21:07:04 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:07:04 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:07:04 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:07:04 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:07:04 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:07:04 Europe/Rome] Router inizializzato
[14-Dec-2024 21:07:04 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:07:04 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:07:04 Europe/Rome] Route home definita
[14-Dec-2024 21:07:04 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:07:04 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:07:04 Europe/Rome] Route progetti definite
[14-Dec-2024 21:07:04 Europe/Rome] Route clienti definite
[14-Dec-2024 21:07:04 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:07:04 Europe/Rome] URL richiesto: login
[14-Dec-2024 21:07:04 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 21:07:04 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 21:07:04 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 21:07:04 Europe/Rome] Password verificata con successo
[14-Dec-2024 21:07:04 Europe/Rome] Login riuscito per l'utente: Mauro con ruolo: user
[14-Dec-2024 21:07:04 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:07:04 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:07:04 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:07:04 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:07:04 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:07:04 Europe/Rome] Router inizializzato
[14-Dec-2024 21:07:04 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:07:04 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:07:04 Europe/Rome] Route home definita
[14-Dec-2024 21:07:04 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:07:04 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:07:04 Europe/Rome] Route progetti definite
[14-Dec-2024 21:07:04 Europe/Rome] Route clienti definite
[14-Dec-2024 21:07:04 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:07:04 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 21:07:04 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 21:07:04 Europe/Rome] 
==================================================
[14-Dec-2024 21:07:04 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 21:07:04 Europe/Rome] ==================================================

[14-Dec-2024 21:07:04 Europe/Rome] Clienti totali: 2
[14-Dec-2024 21:07:04 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 21:07:04 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 21:07:04 Europe/Rome] Progetti totali: 1
[14-Dec-2024 21:07:04 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 21:07:04 Europe/Rome] 
==================================================
[14-Dec-2024 21:07:04 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 21:07:04 Europe/Rome] ==================================================

[14-Dec-2024 21:07:04 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:07:04 Europe/Rome] Route dispatched
[14-Dec-2024 21:07:12 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:07:12 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:07:14 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:07:14 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:07:14 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:07:14 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:07:14 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:07:14 Europe/Rome] Router inizializzato
[14-Dec-2024 21:07:14 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:07:14 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:07:14 Europe/Rome] Route home definita
[14-Dec-2024 21:07:14 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:07:14 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:07:14 Europe/Rome] Route progetti definite
[14-Dec-2024 21:07:14 Europe/Rome] Route clienti definite
[14-Dec-2024 21:07:14 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:07:14 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 21:07:14 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 21:07:14 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:07:14 Europe/Rome] Route dispatched
[14-Dec-2024 21:07:17 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:07:17 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:07:17 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:07:17 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:07:17 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:07:17 Europe/Rome] Router inizializzato
[14-Dec-2024 21:07:17 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:07:17 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:07:17 Europe/Rome] Route home definita
[14-Dec-2024 21:07:17 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:07:17 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:07:17 Europe/Rome] Route progetti definite
[14-Dec-2024 21:07:17 Europe/Rome] Route clienti definite
[14-Dec-2024 21:07:17 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:07:17 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 21:07:17 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 21:07:17 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:07:17 Europe/Rome] Route dispatched
[14-Dec-2024 21:10:55 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:10:55 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:10:55 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:10:55 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:10:55 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:10:55 Europe/Rome] Router inizializzato
[14-Dec-2024 21:10:55 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:10:55 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:10:55 Europe/Rome] Route home definita
[14-Dec-2024 21:10:55 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:10:55 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:10:55 Europe/Rome] Route progetti definite
[14-Dec-2024 21:10:55 Europe/Rome] Route clienti definite
[14-Dec-2024 21:10:55 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:10:55 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 21:10:55 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 21:10:55 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 21:10:55 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 21:10:55 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 21:10:55 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 21:10:55 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => cila
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 21:10:55 Europe/Rome] Route dispatched
[14-Dec-2024 21:10:55 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:10:55 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => cila
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 21:10:55 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 21:10:55 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 21:10:57 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:10:57 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:10:57 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:10:57 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:10:57 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:10:57 Europe/Rome] Router inizializzato
[14-Dec-2024 21:10:57 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:10:57 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:10:57 Europe/Rome] Route home definita
[14-Dec-2024 21:10:57 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:10:57 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:10:57 Europe/Rome] Route progetti definite
[14-Dec-2024 21:10:57 Europe/Rome] Route clienti definite
[14-Dec-2024 21:10:57 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:10:57 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 21:10:57 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 21:10:57 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:10:57 Europe/Rome] Route dispatched
[14-Dec-2024 21:11:00 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:11:00 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:11:08 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:11:08 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:11:08 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:11:08 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:11:08 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:11:08 Europe/Rome] Router inizializzato
[14-Dec-2024 21:11:08 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:11:08 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:11:08 Europe/Rome] Route home definita
[14-Dec-2024 21:11:08 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:11:08 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:11:08 Europe/Rome] Route progetti definite
[14-Dec-2024 21:11:08 Europe/Rome] Route clienti definite
[14-Dec-2024 21:11:08 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:11:08 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 21:11:08 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 21:11:08 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:11:08 Europe/Rome] Route dispatched
[14-Dec-2024 21:11:23 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:11:23 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:11:25 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:11:25 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:13:13 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:13:13 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:13:13 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:13:13 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:13:13 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:13:13 Europe/Rome] Router inizializzato
[14-Dec-2024 21:13:13 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:13:13 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:13:13 Europe/Rome] Route home definita
[14-Dec-2024 21:13:13 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:13:13 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:13:13 Europe/Rome] Route progetti definite
[14-Dec-2024 21:13:13 Europe/Rome] Route clienti definite
[14-Dec-2024 21:13:13 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:13:13 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 21:13:13 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 21:13:13 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:13:13 Europe/Rome] Route dispatched
[14-Dec-2024 21:15:53 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:15:53 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:15:53 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:15:53 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:15:53 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:15:53 Europe/Rome] Router inizializzato
[14-Dec-2024 21:15:53 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:15:53 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:15:53 Europe/Rome] Route home definita
[14-Dec-2024 21:15:53 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:15:53 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:15:53 Europe/Rome] Route progetti definite
[14-Dec-2024 21:15:53 Europe/Rome] Route clienti definite
[14-Dec-2024 21:15:53 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:15:53 Europe/Rome] URL richiesto: logout
[14-Dec-2024 21:15:53 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:15:53 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:15:53 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:15:53 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:15:53 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:15:53 Europe/Rome] Router inizializzato
[14-Dec-2024 21:15:53 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:15:53 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:15:53 Europe/Rome] Route home definita
[14-Dec-2024 21:15:53 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:15:53 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:15:53 Europe/Rome] Route progetti definite
[14-Dec-2024 21:15:53 Europe/Rome] Route clienti definite
[14-Dec-2024 21:15:53 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:15:53 Europe/Rome] URL richiesto: login
[14-Dec-2024 21:15:53 Europe/Rome] Route dispatched
[14-Dec-2024 21:20:55 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:20:55 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:20:55 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:20:55 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:20:55 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:20:55 Europe/Rome] Router inizializzato
[14-Dec-2024 21:20:55 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:20:55 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:20:55 Europe/Rome] Route home definita
[14-Dec-2024 21:20:55 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:20:55 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:20:55 Europe/Rome] Route progetti definite
[14-Dec-2024 21:20:55 Europe/Rome] Route clienti definite
[14-Dec-2024 21:20:55 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:20:55 Europe/Rome] URL richiesto: 
[14-Dec-2024 21:20:55 Europe/Rome] Utente non autenticato, redirect al login
[14-Dec-2024 21:20:55 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:20:55 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:20:55 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:20:55 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:20:55 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:20:55 Europe/Rome] Router inizializzato
[14-Dec-2024 21:20:55 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:20:55 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:20:55 Europe/Rome] Route home definita
[14-Dec-2024 21:20:55 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:20:55 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:20:55 Europe/Rome] Route progetti definite
[14-Dec-2024 21:20:55 Europe/Rome] Route clienti definite
[14-Dec-2024 21:20:55 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:20:55 Europe/Rome] URL richiesto: login
[14-Dec-2024 21:20:55 Europe/Rome] Route dispatched
[14-Dec-2024 21:20:58 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:20:58 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:20:58 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:20:58 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:20:58 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:20:58 Europe/Rome] Router inizializzato
[14-Dec-2024 21:20:58 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:20:58 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:20:58 Europe/Rome] Route home definita
[14-Dec-2024 21:20:58 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:20:58 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:20:58 Europe/Rome] Route progetti definite
[14-Dec-2024 21:20:58 Europe/Rome] Route clienti definite
[14-Dec-2024 21:20:58 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:20:58 Europe/Rome] URL richiesto: login
[14-Dec-2024 21:20:58 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 21:20:58 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 21:20:58 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 21:20:58 Europe/Rome] Password verificata con successo
[14-Dec-2024 21:20:58 Europe/Rome] Login riuscito per l'utente: Mauro con ruolo: user
[14-Dec-2024 21:20:58 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:20:58 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:20:58 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:20:58 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:20:58 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:20:58 Europe/Rome] Router inizializzato
[14-Dec-2024 21:20:58 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:20:58 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:20:58 Europe/Rome] Route home definita
[14-Dec-2024 21:20:58 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:20:58 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:20:58 Europe/Rome] Route progetti definite
[14-Dec-2024 21:20:58 Europe/Rome] Route clienti definite
[14-Dec-2024 21:20:58 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:20:58 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 21:20:58 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 21:20:58 Europe/Rome] 
==================================================
[14-Dec-2024 21:20:58 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 21:20:58 Europe/Rome] ==================================================

[14-Dec-2024 21:20:58 Europe/Rome] Clienti totali: 2
[14-Dec-2024 21:20:58 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 21:20:58 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 21:20:58 Europe/Rome] Progetti totali: 1
[14-Dec-2024 21:20:58 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 21:20:58 Europe/Rome] 
==================================================
[14-Dec-2024 21:20:58 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 21:20:58 Europe/Rome] ==================================================

[14-Dec-2024 21:20:58 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:20:58 Europe/Rome] Route dispatched
[14-Dec-2024 21:21:00 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:21:00 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:21:11 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:21:11 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:21:11 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:21:11 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:21:11 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:21:11 Europe/Rome] Router inizializzato
[14-Dec-2024 21:21:11 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:21:11 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:21:11 Europe/Rome] Route home definita
[14-Dec-2024 21:21:11 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:21:11 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:21:11 Europe/Rome] Route progetti definite
[14-Dec-2024 21:21:11 Europe/Rome] Route clienti definite
[14-Dec-2024 21:21:11 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:21:11 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 21:21:11 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 21:21:11 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 21:21:11 Europe/Rome] Route dispatched
[14-Dec-2024 21:21:23 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:21:23 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:21:23 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:21:23 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:21:23 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:21:23 Europe/Rome] Router inizializzato
[14-Dec-2024 21:21:23 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:21:23 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:21:23 Europe/Rome] Route home definita
[14-Dec-2024 21:21:23 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:21:23 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:21:23 Europe/Rome] Route progetti definite
[14-Dec-2024 21:21:23 Europe/Rome] Route clienti definite
[14-Dec-2024 21:21:23 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:21:23 Europe/Rome] URL richiesto: logout
[14-Dec-2024 21:21:23 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 21:21:23 Europe/Rome] Autoloader registrato
[14-Dec-2024 21:21:23 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 21:21:23 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 21:21:23 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 21:21:23 Europe/Rome] Router inizializzato
[14-Dec-2024 21:21:23 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 21:21:23 Europe/Rome] Route profilo admin definite
[14-Dec-2024 21:21:23 Europe/Rome] Route home definita
[14-Dec-2024 21:21:23 Europe/Rome] Route dashboard definita
[14-Dec-2024 21:21:23 Europe/Rome] Route pratiche definite
[14-Dec-2024 21:21:23 Europe/Rome] Route progetti definite
[14-Dec-2024 21:21:23 Europe/Rome] Route clienti definite
[14-Dec-2024 21:21:23 Europe/Rome] Route scadenze definite
[14-Dec-2024 21:21:23 Europe/Rome] URL richiesto: login
[14-Dec-2024 21:21:23 Europe/Rome] Route dispatched
[14-Dec-2024 22:52:12 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:52:12 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:52:12 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:52:12 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:52:12 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:52:12 Europe/Rome] Router inizializzato
[14-Dec-2024 22:52:12 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:52:12 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:52:12 Europe/Rome] Route home definita
[14-Dec-2024 22:52:12 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:52:12 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:52:12 Europe/Rome] Route progetti definite
[14-Dec-2024 22:52:12 Europe/Rome] Route clienti definite
[14-Dec-2024 22:52:12 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:52:12 Europe/Rome] URL richiesto: login
[14-Dec-2024 22:52:12 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 22:52:12 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 22:52:12 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 22:52:12 Europe/Rome] Password verificata con successo
[14-Dec-2024 22:52:12 Europe/Rome] Login riuscito per l'utente: Mauro con ruolo: user
[14-Dec-2024 22:52:12 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:52:12 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:52:12 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:52:12 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:52:12 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:52:12 Europe/Rome] Router inizializzato
[14-Dec-2024 22:52:12 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:52:12 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:52:12 Europe/Rome] Route home definita
[14-Dec-2024 22:52:12 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:52:12 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:52:12 Europe/Rome] Route progetti definite
[14-Dec-2024 22:52:12 Europe/Rome] Route clienti definite
[14-Dec-2024 22:52:12 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:52:12 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 22:52:12 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:52:12 Europe/Rome] 
==================================================
[14-Dec-2024 22:52:12 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 22:52:12 Europe/Rome] ==================================================

[14-Dec-2024 22:52:12 Europe/Rome] Clienti totali: 2
[14-Dec-2024 22:52:12 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 22:52:12 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 22:52:12 Europe/Rome] Progetti totali: 1
[14-Dec-2024 22:52:12 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 22:52:12 Europe/Rome] 
==================================================
[14-Dec-2024 22:52:12 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 22:52:12 Europe/Rome] ==================================================

[14-Dec-2024 22:52:12 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:52:12 Europe/Rome] Route dispatched
[14-Dec-2024 22:52:16 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:52:16 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:52:18 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:52:18 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:52:18 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:52:18 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:52:18 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:52:18 Europe/Rome] Router inizializzato
[14-Dec-2024 22:52:18 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:52:18 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:52:18 Europe/Rome] Route home definita
[14-Dec-2024 22:52:18 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:52:18 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:52:18 Europe/Rome] Route progetti definite
[14-Dec-2024 22:52:18 Europe/Rome] Route clienti definite
[14-Dec-2024 22:52:18 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:52:18 Europe/Rome] URL richiesto: clienti/dettagli/2
[14-Dec-2024 22:52:18 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:52:18 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:52:18 Europe/Rome] Route dispatched
[14-Dec-2024 22:52:21 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:52:21 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:52:22 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:52:22 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:52:22 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:52:22 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:52:22 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:52:22 Europe/Rome] Router inizializzato
[14-Dec-2024 22:52:22 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:52:22 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:52:22 Europe/Rome] Route home definita
[14-Dec-2024 22:52:22 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:52:22 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:52:22 Europe/Rome] Route progetti definite
[14-Dec-2024 22:52:22 Europe/Rome] Route clienti definite
[14-Dec-2024 22:52:22 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:52:22 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 22:52:22 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:52:22 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:52:22 Europe/Rome] Route dispatched
[14-Dec-2024 22:52:26 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:52:26 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:52:26 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:52:26 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:52:26 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:52:26 Europe/Rome] Router inizializzato
[14-Dec-2024 22:52:26 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:52:26 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:52:26 Europe/Rome] Route home definita
[14-Dec-2024 22:52:26 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:52:26 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:52:26 Europe/Rome] Route progetti definite
[14-Dec-2024 22:52:26 Europe/Rome] Route clienti definite
[14-Dec-2024 22:52:26 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:52:26 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 22:52:26 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:52:26 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:52:26 Europe/Rome] Route dispatched
[14-Dec-2024 22:52:36 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:52:36 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:52:36 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:52:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:52:36 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:52:36 Europe/Rome] Router inizializzato
[14-Dec-2024 22:52:36 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:52:36 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:52:36 Europe/Rome] Route home definita
[14-Dec-2024 22:52:36 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:52:36 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:52:36 Europe/Rome] Route progetti definite
[14-Dec-2024 22:52:36 Europe/Rome] Route clienti definite
[14-Dec-2024 22:52:36 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:52:36 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 22:52:36 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:52:36 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:52:36 Europe/Rome] Route dispatched
[14-Dec-2024 22:52:40 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:52:40 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:52:40 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:52:40 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:52:40 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:52:40 Europe/Rome] Router inizializzato
[14-Dec-2024 22:52:40 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:52:40 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:52:40 Europe/Rome] Route home definita
[14-Dec-2024 22:52:40 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:52:40 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:52:40 Europe/Rome] Route progetti definite
[14-Dec-2024 22:52:40 Europe/Rome] Route clienti definite
[14-Dec-2024 22:52:40 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:52:40 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 22:52:40 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:52:40 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:52:40 Europe/Rome] Route dispatched
[14-Dec-2024 22:53:33 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:53:33 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:53:33 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:53:33 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:53:33 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:53:33 Europe/Rome] Router inizializzato
[14-Dec-2024 22:53:33 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:53:33 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:53:33 Europe/Rome] Route home definita
[14-Dec-2024 22:53:33 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:53:33 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:53:33 Europe/Rome] Route progetti definite
[14-Dec-2024 22:53:33 Europe/Rome] Route clienti definite
[14-Dec-2024 22:53:33 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:53:33 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 22:53:33 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:53:33 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:53:33 Europe/Rome] Route dispatched
[14-Dec-2024 22:53:36 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:53:36 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:53:36 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:53:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:53:36 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:53:36 Europe/Rome] Router inizializzato
[14-Dec-2024 22:53:36 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:53:36 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:53:36 Europe/Rome] Route home definita
[14-Dec-2024 22:53:36 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:53:36 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:53:36 Europe/Rome] Route progetti definite
[14-Dec-2024 22:53:36 Europe/Rome] Route clienti definite
[14-Dec-2024 22:53:36 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:53:36 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 22:53:36 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:53:36 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:53:36 Europe/Rome] Route dispatched
[14-Dec-2024 22:57:13 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:57:13 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:57:13 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:57:13 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:57:13 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:57:13 Europe/Rome] Router inizializzato
[14-Dec-2024 22:57:13 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:57:13 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:57:13 Europe/Rome] Route home definita
[14-Dec-2024 22:57:13 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:57:13 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:57:13 Europe/Rome] Route progetti definite
[14-Dec-2024 22:57:13 Europe/Rome] Route clienti definite
[14-Dec-2024 22:57:13 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:57:13 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 22:57:13 Europe/Rome] Utente autenticato: Mauro
[2024-12-14 22:57:13][🔴 FATAL][Mauro@::1][error_log.php:105] Eccezione non catturata: syntax error, unexpected token "<", expecting end of file
Stack trace:
#0 C:\xampp\htdocs\studio_tecnico\app\core\Controller.php(21): require_once()
#1 C:\xampp\htdocs\studio_tecnico\app\controllers\ProgettiController.php(44): App\Core\Controller->view('progetti/index', Array)
#2 [internal function]: App\Controllers\ProgettiController->index()
#3 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#4 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('progetti')
#5 {main}
[14-Dec-2024 22:57:18 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:57:18 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:57:18 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:57:18 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:57:18 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:57:18 Europe/Rome] Router inizializzato
[14-Dec-2024 22:57:18 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:57:18 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:57:18 Europe/Rome] Route home definita
[14-Dec-2024 22:57:18 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:57:18 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:57:18 Europe/Rome] Route progetti definite
[14-Dec-2024 22:57:18 Europe/Rome] Route clienti definite
[14-Dec-2024 22:57:18 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:57:18 Europe/Rome] URL richiesto: 
[14-Dec-2024 22:57:18 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:57:18 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:57:18 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:57:18 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:57:18 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:57:18 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:57:18 Europe/Rome] Router inizializzato
[14-Dec-2024 22:57:18 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:57:18 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:57:18 Europe/Rome] Route home definita
[14-Dec-2024 22:57:18 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:57:18 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:57:18 Europe/Rome] Route progetti definite
[14-Dec-2024 22:57:18 Europe/Rome] Route clienti definite
[14-Dec-2024 22:57:18 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:57:18 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 22:57:18 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:57:18 Europe/Rome] 
==================================================
[14-Dec-2024 22:57:18 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 22:57:18 Europe/Rome] ==================================================

[14-Dec-2024 22:57:18 Europe/Rome] Clienti totali: 2
[14-Dec-2024 22:57:18 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 22:57:18 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 22:57:18 Europe/Rome] Progetti totali: 1
[14-Dec-2024 22:57:18 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 22:57:18 Europe/Rome] 
==================================================
[14-Dec-2024 22:57:18 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 22:57:18 Europe/Rome] ==================================================

[2024-12-14 22:57:18][🔴 FATAL][Mauro@::1][error_log.php:105] Eccezione non catturata: syntax error, unexpected token "<", expecting end of file
Stack trace:
#0 C:\xampp\htdocs\studio_tecnico\app\controllers\DashboardController.php(55): include()
#1 [internal function]: App\Controllers\DashboardController->index()
#2 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#3 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('dashboard')
#4 {main}
[14-Dec-2024 22:57:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:57:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:57:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:57:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:57:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:57:30 Europe/Rome] Router inizializzato
[14-Dec-2024 22:57:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:57:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:57:30 Europe/Rome] Route home definita
[14-Dec-2024 22:57:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:57:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:57:30 Europe/Rome] Route progetti definite
[14-Dec-2024 22:57:30 Europe/Rome] Route clienti definite
[14-Dec-2024 22:57:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:57:30 Europe/Rome] URL richiesto: 
[14-Dec-2024 22:57:30 Europe/Rome] Utente non autenticato, redirect al login
[14-Dec-2024 22:57:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:57:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:57:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:57:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:57:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:57:30 Europe/Rome] Router inizializzato
[14-Dec-2024 22:57:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:57:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:57:30 Europe/Rome] Route home definita
[14-Dec-2024 22:57:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:57:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:57:30 Europe/Rome] Route progetti definite
[14-Dec-2024 22:57:30 Europe/Rome] Route clienti definite
[14-Dec-2024 22:57:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:57:30 Europe/Rome] URL richiesto: login
[2024-12-14 22:57:30][🔴 FATAL][Guest@::1][error_log.php:105] Eccezione non catturata: syntax error, unexpected token "<", expecting end of file
Stack trace:
#0 C:\xampp\htdocs\studio_tecnico\app\controllers\AuthController.php(21): require_once()
#1 [internal function]: App\Controllers\AuthController->showLoginForm()
#2 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#3 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('login')
#4 {main}
[14-Dec-2024 22:58:48 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:58:48 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:58:48 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:58:48 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:58:48 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:58:48 Europe/Rome] Router inizializzato
[14-Dec-2024 22:58:48 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:58:48 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:58:48 Europe/Rome] Route home definita
[14-Dec-2024 22:58:48 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:58:48 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:58:48 Europe/Rome] Route progetti definite
[14-Dec-2024 22:58:48 Europe/Rome] Route clienti definite
[14-Dec-2024 22:58:48 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:58:48 Europe/Rome] URL richiesto: 
[14-Dec-2024 22:58:48 Europe/Rome] Utente non autenticato, redirect al login
[14-Dec-2024 22:58:48 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:58:48 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:58:48 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:58:48 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:58:48 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:58:48 Europe/Rome] Router inizializzato
[14-Dec-2024 22:58:48 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:58:48 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:58:48 Europe/Rome] Route home definita
[14-Dec-2024 22:58:48 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:58:48 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:58:48 Europe/Rome] Route progetti definite
[14-Dec-2024 22:58:48 Europe/Rome] Route clienti definite
[14-Dec-2024 22:58:48 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:58:48 Europe/Rome] URL richiesto: login
[14-Dec-2024 22:58:48 Europe/Rome] Route dispatched
[14-Dec-2024 22:58:52 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:58:52 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:58:52 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:58:52 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:58:52 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:58:52 Europe/Rome] Router inizializzato
[14-Dec-2024 22:58:52 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:58:52 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:58:52 Europe/Rome] Route home definita
[14-Dec-2024 22:58:52 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:58:52 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:58:52 Europe/Rome] Route progetti definite
[14-Dec-2024 22:58:52 Europe/Rome] Route clienti definite
[14-Dec-2024 22:58:52 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:58:52 Europe/Rome] URL richiesto: login
[14-Dec-2024 22:58:52 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 22:58:52 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 22:58:52 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 22:58:52 Europe/Rome] Password verificata con successo
[14-Dec-2024 22:58:52 Europe/Rome] Login riuscito per l'utente: Mauro con ruolo: user
[14-Dec-2024 22:58:52 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:58:52 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:58:52 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:58:52 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:58:52 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:58:52 Europe/Rome] Router inizializzato
[14-Dec-2024 22:58:52 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:58:52 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:58:52 Europe/Rome] Route home definita
[14-Dec-2024 22:58:52 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:58:52 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:58:52 Europe/Rome] Route progetti definite
[14-Dec-2024 22:58:52 Europe/Rome] Route clienti definite
[14-Dec-2024 22:58:52 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:58:52 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 22:58:52 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:58:52 Europe/Rome] 
==================================================
[14-Dec-2024 22:58:52 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 22:58:52 Europe/Rome] ==================================================

[14-Dec-2024 22:58:52 Europe/Rome] Clienti totali: 2
[14-Dec-2024 22:58:52 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 22:58:52 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 22:58:52 Europe/Rome] Progetti totali: 1
[14-Dec-2024 22:58:52 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 22:58:52 Europe/Rome] 
==================================================
[14-Dec-2024 22:58:52 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 22:58:52 Europe/Rome] ==================================================

[14-Dec-2024 22:58:52 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:58:52 Europe/Rome] Route dispatched
[14-Dec-2024 22:58:55 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:58:55 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:58:58 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:58:58 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:58:58 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:58:58 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:58:58 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:58:58 Europe/Rome] Router inizializzato
[14-Dec-2024 22:58:58 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:58:58 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:58:58 Europe/Rome] Route home definita
[14-Dec-2024 22:58:58 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:58:58 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:58:58 Europe/Rome] Route progetti definite
[14-Dec-2024 22:58:58 Europe/Rome] Route clienti definite
[14-Dec-2024 22:58:58 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:58:58 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 22:58:58 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:58:58 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 22:58:58 Europe/Rome] Route dispatched
[14-Dec-2024 22:59:03 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 22:59:03 Europe/Rome] Autoloader registrato
[14-Dec-2024 22:59:03 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 22:59:03 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 22:59:03 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 22:59:03 Europe/Rome] Router inizializzato
[14-Dec-2024 22:59:03 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 22:59:03 Europe/Rome] Route profilo admin definite
[14-Dec-2024 22:59:03 Europe/Rome] Route home definita
[14-Dec-2024 22:59:03 Europe/Rome] Route dashboard definita
[14-Dec-2024 22:59:03 Europe/Rome] Route pratiche definite
[14-Dec-2024 22:59:03 Europe/Rome] Route progetti definite
[14-Dec-2024 22:59:03 Europe/Rome] Route clienti definite
[14-Dec-2024 22:59:03 Europe/Rome] Route scadenze definite
[14-Dec-2024 22:59:03 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 22:59:03 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 22:59:03 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[2024-12-14 22:59:03][🟠 ERROR][Mauro@::1][error_log.php:94] htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated
[14-Dec-2024 22:59:03 Europe/Rome] PHP Deprecated:  htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated in C:\xampp\htdocs\studio_tecnico\views\progetti\dettagli.php on line 102
[14-Dec-2024 22:59:03 Europe/Rome] Route dispatched
[14-Dec-2024 23:00:11 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:00:11 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:00:11 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:00:11 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:00:11 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:00:11 Europe/Rome] Router inizializzato
[14-Dec-2024 23:00:11 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:00:11 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:00:11 Europe/Rome] Route home definita
[14-Dec-2024 23:00:11 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:00:11 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:00:11 Europe/Rome] Route progetti definite
[14-Dec-2024 23:00:11 Europe/Rome] Route clienti definite
[14-Dec-2024 23:00:11 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:00:11 Europe/Rome] URL richiesto: 
[14-Dec-2024 23:00:11 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:00:11 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:00:11 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:00:11 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:00:11 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:00:11 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:00:11 Europe/Rome] Router inizializzato
[14-Dec-2024 23:00:11 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:00:11 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:00:11 Europe/Rome] Route home definita
[14-Dec-2024 23:00:11 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:00:11 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:00:11 Europe/Rome] Route progetti definite
[14-Dec-2024 23:00:11 Europe/Rome] Route clienti definite
[14-Dec-2024 23:00:11 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:00:11 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:00:11 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:00:11 Europe/Rome] 
==================================================
[14-Dec-2024 23:00:11 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:00:11 Europe/Rome] ==================================================

[14-Dec-2024 23:00:11 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:00:11 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:00:11 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:00:11 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:00:11 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:00:11 Europe/Rome] 
==================================================
[14-Dec-2024 23:00:11 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:00:11 Europe/Rome] ==================================================

[14-Dec-2024 23:00:11 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:00:11 Europe/Rome] Route dispatched
[14-Dec-2024 23:00:13 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:00:13 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:00:14 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:00:14 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:00:14 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:00:14 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:00:14 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:00:14 Europe/Rome] Router inizializzato
[14-Dec-2024 23:00:14 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:00:14 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:00:14 Europe/Rome] Route home definita
[14-Dec-2024 23:00:14 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:00:14 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:00:14 Europe/Rome] Route progetti definite
[14-Dec-2024 23:00:14 Europe/Rome] Route clienti definite
[14-Dec-2024 23:00:14 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:00:14 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 23:00:14 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:00:14 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:00:14 Europe/Rome] Route dispatched
[14-Dec-2024 23:00:18 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:00:18 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:00:18 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:00:18 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:00:18 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:00:18 Europe/Rome] Router inizializzato
[14-Dec-2024 23:00:18 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:00:18 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:00:18 Europe/Rome] Route home definita
[14-Dec-2024 23:00:18 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:00:18 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:00:18 Europe/Rome] Route progetti definite
[14-Dec-2024 23:00:18 Europe/Rome] Route clienti definite
[14-Dec-2024 23:00:18 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:00:18 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 23:00:18 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:00:18 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:00:18 Europe/Rome] Route dispatched
[14-Dec-2024 23:00:36 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:00:36 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:00:36 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:00:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:00:36 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:00:36 Europe/Rome] Router inizializzato
[14-Dec-2024 23:00:36 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:00:36 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:00:36 Europe/Rome] Route home definita
[14-Dec-2024 23:00:36 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:00:36 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:00:36 Europe/Rome] Route progetti definite
[14-Dec-2024 23:00:36 Europe/Rome] Route clienti definite
[14-Dec-2024 23:00:36 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:00:36 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 23:00:36 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:00:36 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:00:36 Europe/Rome] Route dispatched
[14-Dec-2024 23:00:38 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:00:38 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:00:38 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:00:38 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:00:38 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:00:38 Europe/Rome] Router inizializzato
[14-Dec-2024 23:00:38 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:00:38 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:00:38 Europe/Rome] Route home definita
[14-Dec-2024 23:00:38 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:00:38 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:00:38 Europe/Rome] Route progetti definite
[14-Dec-2024 23:00:38 Europe/Rome] Route clienti definite
[14-Dec-2024 23:00:38 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:00:38 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 23:00:38 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:00:38 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:00:38 Europe/Rome] Route dispatched
[14-Dec-2024 23:00:39 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:00:39 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:00:39 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:00:39 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:00:39 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:00:39 Europe/Rome] Router inizializzato
[14-Dec-2024 23:00:39 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:00:39 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:00:39 Europe/Rome] Route home definita
[14-Dec-2024 23:00:39 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:00:39 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:00:39 Europe/Rome] Route progetti definite
[14-Dec-2024 23:00:39 Europe/Rome] Route clienti definite
[14-Dec-2024 23:00:39 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:00:39 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 23:00:39 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:00:39 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:00:39 Europe/Rome] Route dispatched
[14-Dec-2024 23:00:43 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:00:43 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:00:43 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:00:43 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:00:43 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:00:43 Europe/Rome] Router inizializzato
[14-Dec-2024 23:00:43 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:00:43 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:00:43 Europe/Rome] Route home definita
[14-Dec-2024 23:00:43 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:00:43 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:00:43 Europe/Rome] Route progetti definite
[14-Dec-2024 23:00:43 Europe/Rome] Route clienti definite
[14-Dec-2024 23:00:43 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:00:43 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 23:00:43 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:00:43 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 23:00:43 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 23:00:43 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 23:00:43 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 23:00:43 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => cila
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 23:00:43 Europe/Rome] Route dispatched
[14-Dec-2024 23:00:43 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:00:43 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => cila
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 23:00:43 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 23:00:43 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 23:01:07 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:01:07 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:01:07 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:01:07 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:01:07 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:01:07 Europe/Rome] Router inizializzato
[14-Dec-2024 23:01:07 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:01:07 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:01:07 Europe/Rome] Route home definita
[14-Dec-2024 23:01:07 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:01:07 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:01:07 Europe/Rome] Route progetti definite
[14-Dec-2024 23:01:07 Europe/Rome] Route clienti definite
[14-Dec-2024 23:01:07 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:01:07 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 23:01:07 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:01:07 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:01:07 Europe/Rome] Route dispatched
[14-Dec-2024 23:01:11 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:01:11 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:01:12 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:01:12 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:01:12 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:01:12 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:01:12 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:01:12 Europe/Rome] Router inizializzato
[14-Dec-2024 23:01:12 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:01:12 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:01:12 Europe/Rome] Route home definita
[14-Dec-2024 23:01:12 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:01:12 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:01:12 Europe/Rome] Route progetti definite
[14-Dec-2024 23:01:12 Europe/Rome] Route clienti definite
[14-Dec-2024 23:01:12 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:01:12 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 23:01:12 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:01:12 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:01:12 Europe/Rome] Route dispatched
[14-Dec-2024 23:01:16 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:01:16 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:01:16 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:01:16 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:01:16 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:01:16 Europe/Rome] Router inizializzato
[14-Dec-2024 23:01:16 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:01:16 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:01:16 Europe/Rome] Route home definita
[14-Dec-2024 23:01:16 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:01:16 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:01:16 Europe/Rome] Route progetti definite
[14-Dec-2024 23:01:16 Europe/Rome] Route clienti definite
[14-Dec-2024 23:01:16 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:01:16 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 23:01:16 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:01:16 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:01:16 Europe/Rome] Route dispatched
[14-Dec-2024 23:02:28 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:02:28 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:02:28 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:02:28 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:02:28 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:02:28 Europe/Rome] Router inizializzato
[14-Dec-2024 23:02:28 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:02:28 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:02:28 Europe/Rome] Route home definita
[14-Dec-2024 23:02:28 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:02:28 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:02:28 Europe/Rome] Route progetti definite
[14-Dec-2024 23:02:28 Europe/Rome] Route clienti definite
[14-Dec-2024 23:02:28 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:02:28 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 23:02:28 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:02:28 Europe/Rome] Errore nel recupero dei dettagli del progetto: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'studio_tecnico.tipi_pratica' doesn't exist
[14-Dec-2024 23:02:28 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:02:28 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:02:28 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:02:28 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:02:28 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:02:28 Europe/Rome] Router inizializzato
[14-Dec-2024 23:02:28 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:02:28 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:02:28 Europe/Rome] Route home definita
[14-Dec-2024 23:02:28 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:02:28 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:02:28 Europe/Rome] Route progetti definite
[14-Dec-2024 23:02:28 Europe/Rome] Route clienti definite
[14-Dec-2024 23:02:28 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:02:28 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 23:02:28 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:02:28 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:02:28 Europe/Rome] Route dispatched
[14-Dec-2024 23:02:31 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:02:31 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:02:32 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:02:32 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:02:32 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:02:32 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:02:32 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:02:32 Europe/Rome] Router inizializzato
[14-Dec-2024 23:02:32 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:02:33 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:02:33 Europe/Rome] Route home definita
[14-Dec-2024 23:02:33 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:02:33 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:02:33 Europe/Rome] Route progetti definite
[14-Dec-2024 23:02:33 Europe/Rome] Route clienti definite
[14-Dec-2024 23:02:33 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:02:33 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 23:02:33 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:02:33 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:02:33 Europe/Rome] Route dispatched
[14-Dec-2024 23:02:34 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:02:34 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:02:34 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:02:34 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:02:34 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:02:34 Europe/Rome] Router inizializzato
[14-Dec-2024 23:02:34 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:02:34 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:02:34 Europe/Rome] Route home definita
[14-Dec-2024 23:02:34 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:02:34 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:02:34 Europe/Rome] Route progetti definite
[14-Dec-2024 23:02:34 Europe/Rome] Route clienti definite
[14-Dec-2024 23:02:34 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:02:34 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 23:02:34 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:02:34 Europe/Rome] Errore nel recupero dei dettagli del progetto: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'studio_tecnico.tipi_pratica' doesn't exist
[14-Dec-2024 23:02:34 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:02:34 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:02:34 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:02:34 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:02:34 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:02:34 Europe/Rome] Router inizializzato
[14-Dec-2024 23:02:34 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:02:34 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:02:34 Europe/Rome] Route home definita
[14-Dec-2024 23:02:34 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:02:34 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:02:34 Europe/Rome] Route progetti definite
[14-Dec-2024 23:02:34 Europe/Rome] Route clienti definite
[14-Dec-2024 23:02:34 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:02:34 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 23:02:34 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:02:34 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:02:34 Europe/Rome] Route dispatched
[14-Dec-2024 23:04:07 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:04:07 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:04:07 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:04:07 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:04:07 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:04:07 Europe/Rome] Router inizializzato
[14-Dec-2024 23:04:07 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:04:07 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:04:07 Europe/Rome] Route home definita
[14-Dec-2024 23:04:07 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:04:07 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:04:07 Europe/Rome] Route progetti definite
[14-Dec-2024 23:04:07 Europe/Rome] Route clienti definite
[14-Dec-2024 23:04:07 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:04:07 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 23:04:07 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:04:07 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:04:07 Europe/Rome] Route dispatched
[14-Dec-2024 23:04:08 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:04:08 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:04:08 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:04:08 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:04:08 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:04:08 Europe/Rome] Router inizializzato
[14-Dec-2024 23:04:08 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:04:08 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:04:08 Europe/Rome] Route home definita
[14-Dec-2024 23:04:08 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:04:08 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:04:08 Europe/Rome] Route progetti definite
[14-Dec-2024 23:04:08 Europe/Rome] Route clienti definite
[14-Dec-2024 23:04:08 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:04:08 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 23:04:08 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:04:08 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:04:08 Europe/Rome] Route dispatched
[14-Dec-2024 23:04:10 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:04:10 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:04:12 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:04:12 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:04:12 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:04:12 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:04:12 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:04:12 Europe/Rome] Router inizializzato
[14-Dec-2024 23:04:12 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:04:12 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:04:12 Europe/Rome] Route home definita
[14-Dec-2024 23:04:12 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:04:12 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:04:12 Europe/Rome] Route progetti definite
[14-Dec-2024 23:04:12 Europe/Rome] Route clienti definite
[14-Dec-2024 23:04:12 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:04:12 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 23:04:12 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:04:12 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:04:12 Europe/Rome] Route dispatched
[14-Dec-2024 23:04:15 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:04:15 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:04:15 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:04:15 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:04:15 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:04:15 Europe/Rome] Router inizializzato
[14-Dec-2024 23:04:15 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:04:15 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:04:15 Europe/Rome] Route home definita
[14-Dec-2024 23:04:15 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:04:15 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:04:15 Europe/Rome] Route progetti definite
[14-Dec-2024 23:04:15 Europe/Rome] Route clienti definite
[14-Dec-2024 23:04:15 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:04:15 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 23:04:15 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:04:15 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:04:15 Europe/Rome] Route dispatched
[14-Dec-2024 23:04:18 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:04:18 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:04:18 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:04:18 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:04:18 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:04:18 Europe/Rome] Router inizializzato
[14-Dec-2024 23:04:18 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:04:18 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:04:18 Europe/Rome] Route home definita
[14-Dec-2024 23:04:18 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:04:18 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:04:18 Europe/Rome] Route progetti definite
[14-Dec-2024 23:04:18 Europe/Rome] Route clienti definite
[14-Dec-2024 23:04:18 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:04:18 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 23:04:18 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:04:18 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:04:18 Europe/Rome] Route dispatched
[14-Dec-2024 23:04:19 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:04:19 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:04:19 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:04:19 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:04:19 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:04:19 Europe/Rome] Router inizializzato
[14-Dec-2024 23:04:19 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:04:19 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:04:19 Europe/Rome] Route home definita
[14-Dec-2024 23:04:19 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:04:19 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:04:19 Europe/Rome] Route progetti definite
[14-Dec-2024 23:04:19 Europe/Rome] Route clienti definite
[14-Dec-2024 23:04:19 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:04:19 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 23:04:19 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:04:19 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:04:19 Europe/Rome] Route dispatched
[14-Dec-2024 23:04:20 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:04:20 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:04:20 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:04:20 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:04:20 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:04:20 Europe/Rome] Router inizializzato
[14-Dec-2024 23:04:20 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:04:20 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:04:20 Europe/Rome] Route home definita
[14-Dec-2024 23:04:20 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:04:20 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:04:20 Europe/Rome] Route progetti definite
[14-Dec-2024 23:04:20 Europe/Rome] Route clienti definite
[14-Dec-2024 23:04:20 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:04:20 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 23:04:20 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:04:20 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:04:20 Europe/Rome] Route dispatched
[14-Dec-2024 23:04:28 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:04:28 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:04:29 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:04:29 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:04:29 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:04:29 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:04:29 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:04:29 Europe/Rome] Router inizializzato
[14-Dec-2024 23:04:29 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:04:29 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:04:29 Europe/Rome] Route home definita
[14-Dec-2024 23:04:29 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:04:29 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:04:29 Europe/Rome] Route progetti definite
[14-Dec-2024 23:04:29 Europe/Rome] Route clienti definite
[14-Dec-2024 23:04:29 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:04:29 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 23:04:29 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:04:29 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 23:04:29 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 23:04:29 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 23:04:29 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 23:04:29 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => cila
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 23:04:29 Europe/Rome] Route dispatched
[14-Dec-2024 23:04:29 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:04:29 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => cila
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 23:04:29 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 23:04:29 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 23:04:31 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:04:31 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:04:31 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:04:31 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:04:31 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:04:31 Europe/Rome] Router inizializzato
[14-Dec-2024 23:04:31 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:04:31 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:04:31 Europe/Rome] Route home definita
[14-Dec-2024 23:04:31 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:04:31 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:04:31 Europe/Rome] Route progetti definite
[14-Dec-2024 23:04:31 Europe/Rome] Route clienti definite
[14-Dec-2024 23:04:31 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:04:31 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 23:04:31 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:04:31 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:04:31 Europe/Rome] Route dispatched
[14-Dec-2024 23:06:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:06:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:06:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:06:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:06:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:06:24 Europe/Rome] Router inizializzato
[14-Dec-2024 23:06:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:06:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:06:24 Europe/Rome] Route home definita
[14-Dec-2024 23:06:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:06:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:06:24 Europe/Rome] Route progetti definite
[14-Dec-2024 23:06:24 Europe/Rome] Route clienti definite
[14-Dec-2024 23:06:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:06:24 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 23:06:24 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:06:24 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:06:24 Europe/Rome] Route dispatched
[14-Dec-2024 23:06:26 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:06:26 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:06:27 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:06:27 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:06:27 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:06:27 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:06:27 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:06:27 Europe/Rome] Router inizializzato
[14-Dec-2024 23:06:27 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:06:27 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:06:27 Europe/Rome] Route home definita
[14-Dec-2024 23:06:27 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:06:27 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:06:27 Europe/Rome] Route progetti definite
[14-Dec-2024 23:06:27 Europe/Rome] Route clienti definite
[14-Dec-2024 23:06:27 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:06:27 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 23:06:27 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:06:27 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:06:27 Europe/Rome] Route dispatched
[14-Dec-2024 23:06:29 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:06:29 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:06:29 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:06:29 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:06:29 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:06:29 Europe/Rome] Router inizializzato
[14-Dec-2024 23:06:29 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:06:29 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:06:29 Europe/Rome] Route home definita
[14-Dec-2024 23:06:29 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:06:29 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:06:29 Europe/Rome] Route progetti definite
[14-Dec-2024 23:06:29 Europe/Rome] Route clienti definite
[14-Dec-2024 23:06:29 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:06:29 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 23:06:29 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:06:29 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 23:06:29 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_pratica, p.tipo_documento) as tipo_pratica
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 23:06:29 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 23:06:29 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 23:06:29 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => cila
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 23:06:29 Europe/Rome] Route dispatched
[14-Dec-2024 23:06:29 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:06:29 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => cila
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 23:06:29 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 23:06:29 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 23:06:31 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:06:31 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:06:31 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:06:31 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:06:31 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:06:31 Europe/Rome] Router inizializzato
[14-Dec-2024 23:06:31 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:06:31 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:06:31 Europe/Rome] Route home definita
[14-Dec-2024 23:06:31 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:06:31 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:06:31 Europe/Rome] Route progetti definite
[14-Dec-2024 23:06:31 Europe/Rome] Route clienti definite
[14-Dec-2024 23:06:31 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:06:31 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 23:06:31 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:06:31 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:06:31 Europe/Rome] Route dispatched
[14-Dec-2024 23:06:35 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:06:35 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:06:38 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:06:38 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:06:38 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:06:38 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:06:38 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:06:38 Europe/Rome] Router inizializzato
[14-Dec-2024 23:06:38 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:06:38 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:06:38 Europe/Rome] Route home definita
[14-Dec-2024 23:06:38 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:06:38 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:06:38 Europe/Rome] Route progetti definite
[14-Dec-2024 23:06:38 Europe/Rome] Route clienti definite
[14-Dec-2024 23:06:38 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:06:38 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 23:06:38 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:06:38 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:06:38 Europe/Rome] Route dispatched
[14-Dec-2024 23:06:43 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:06:43 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:06:43 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:06:43 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:06:43 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:06:43 Europe/Rome] Router inizializzato
[14-Dec-2024 23:06:43 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:06:43 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:06:43 Europe/Rome] Route home definita
[14-Dec-2024 23:06:43 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:06:43 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:06:43 Europe/Rome] Route progetti definite
[14-Dec-2024 23:06:43 Europe/Rome] Route clienti definite
[14-Dec-2024 23:06:43 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:06:43 Europe/Rome] URL richiesto: progetti/dettagli/1
[14-Dec-2024 23:06:43 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:06:43 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:06:43 Europe/Rome] Route dispatched
[14-Dec-2024 23:06:47 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:06:47 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:06:47 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:06:47 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:06:47 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:06:47 Europe/Rome] Router inizializzato
[14-Dec-2024 23:06:47 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:06:47 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:06:47 Europe/Rome] Route home definita
[14-Dec-2024 23:06:47 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:06:47 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:06:47 Europe/Rome] Route progetti definite
[14-Dec-2024 23:06:47 Europe/Rome] Route clienti definite
[14-Dec-2024 23:06:47 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:06:47 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 23:06:47 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:06:47 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:06:47 Europe/Rome] Route dispatched
[14-Dec-2024 23:09:41 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:09:41 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:09:41 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:09:41 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:09:41 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:09:41 Europe/Rome] Router inizializzato
[14-Dec-2024 23:09:41 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:09:41 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:09:41 Europe/Rome] Route home definita
[14-Dec-2024 23:09:41 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:09:41 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:09:41 Europe/Rome] Route progetti definite
[14-Dec-2024 23:09:41 Europe/Rome] Route clienti definite
[14-Dec-2024 23:09:41 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:09:41 Europe/Rome] URL richiesto: 
[14-Dec-2024 23:09:41 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:09:41 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:09:41 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:09:41 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:09:41 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:09:41 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:09:41 Europe/Rome] Router inizializzato
[14-Dec-2024 23:09:41 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:09:41 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:09:41 Europe/Rome] Route home definita
[14-Dec-2024 23:09:41 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:09:41 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:09:41 Europe/Rome] Route progetti definite
[14-Dec-2024 23:09:41 Europe/Rome] Route clienti definite
[14-Dec-2024 23:09:41 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:09:41 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:09:41 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:09:41 Europe/Rome] 
==================================================
[14-Dec-2024 23:09:41 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:09:41 Europe/Rome] ==================================================

[14-Dec-2024 23:09:41 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:09:41 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:09:41 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:09:41 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:09:41 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:09:41 Europe/Rome] 
==================================================
[14-Dec-2024 23:09:41 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:09:41 Europe/Rome] ==================================================

[14-Dec-2024 23:09:41 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:09:41 Europe/Rome] Route dispatched
[14-Dec-2024 23:09:44 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:09:44 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:09:46 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:09:46 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:09:46 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:09:46 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:09:46 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:09:46 Europe/Rome] Router inizializzato
[14-Dec-2024 23:09:46 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:09:46 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:09:46 Europe/Rome] Route home definita
[14-Dec-2024 23:09:46 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:09:46 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:09:46 Europe/Rome] Route progetti definite
[14-Dec-2024 23:09:46 Europe/Rome] Route clienti definite
[14-Dec-2024 23:09:46 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:09:46 Europe/Rome] URL richiesto: clienti/dettagli/1
[14-Dec-2024 23:09:46 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:09:46 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:09:46 Europe/Rome] Route dispatched
[14-Dec-2024 23:09:56 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:09:56 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:09:56 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:09:56 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:09:56 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:09:56 Europe/Rome] Router inizializzato
[14-Dec-2024 23:09:56 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:09:56 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:09:56 Europe/Rome] Route home definita
[14-Dec-2024 23:09:56 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:09:56 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:09:56 Europe/Rome] Route progetti definite
[14-Dec-2024 23:09:56 Europe/Rome] Route clienti definite
[14-Dec-2024 23:09:56 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:09:56 Europe/Rome] URL richiesto: pratiche/dettagli/1
[14-Dec-2024 23:09:56 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:09:56 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:09:56 Europe/Rome] Route dispatched
[14-Dec-2024 23:13:39 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:13:39 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:13:39 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:13:39 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:13:39 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:13:39 Europe/Rome] Router inizializzato
[14-Dec-2024 23:13:39 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:13:39 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:13:39 Europe/Rome] Route home definita
[14-Dec-2024 23:13:39 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:13:39 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:13:39 Europe/Rome] Route progetti definite
[14-Dec-2024 23:13:39 Europe/Rome] Route clienti definite
[14-Dec-2024 23:13:39 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:13:39 Europe/Rome] URL richiesto: 
[14-Dec-2024 23:13:39 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:13:39 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:13:39 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:13:39 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:13:39 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:13:39 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:13:39 Europe/Rome] Router inizializzato
[14-Dec-2024 23:13:39 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:13:39 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:13:39 Europe/Rome] Route home definita
[14-Dec-2024 23:13:39 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:13:39 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:13:39 Europe/Rome] Route progetti definite
[14-Dec-2024 23:13:39 Europe/Rome] Route clienti definite
[14-Dec-2024 23:13:39 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:13:39 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:13:39 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:13:39 Europe/Rome] 
==================================================
[14-Dec-2024 23:13:39 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:13:39 Europe/Rome] ==================================================

[14-Dec-2024 23:13:39 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:13:39 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:13:39 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:13:39 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:13:39 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:13:39 Europe/Rome] 
==================================================
[14-Dec-2024 23:13:39 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:13:39 Europe/Rome] ==================================================

[14-Dec-2024 23:13:39 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:13:39 Europe/Rome] Route dispatched
[14-Dec-2024 23:14:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:14:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:14:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:14:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:14:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:14:30 Europe/Rome] Router inizializzato
[14-Dec-2024 23:14:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:14:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:14:30 Europe/Rome] Route home definita
[14-Dec-2024 23:14:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:14:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:14:30 Europe/Rome] Route progetti definite
[14-Dec-2024 23:14:30 Europe/Rome] Route clienti definite
[14-Dec-2024 23:14:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:14:30 Europe/Rome] URL richiesto: logout
[14-Dec-2024 23:14:30 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:14:30 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:14:30 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:14:30 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:14:30 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:14:30 Europe/Rome] Router inizializzato
[14-Dec-2024 23:14:30 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:14:30 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:14:30 Europe/Rome] Route home definita
[14-Dec-2024 23:14:30 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:14:30 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:14:30 Europe/Rome] Route progetti definite
[14-Dec-2024 23:14:30 Europe/Rome] Route clienti definite
[14-Dec-2024 23:14:30 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:14:30 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:14:30 Europe/Rome] Route dispatched
[14-Dec-2024 23:23:26 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:23:26 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:23:26 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:23:26 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:23:26 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:23:26 Europe/Rome] Router inizializzato
[14-Dec-2024 23:23:26 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:23:26 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:23:26 Europe/Rome] Route home definita
[14-Dec-2024 23:23:26 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:23:26 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:23:26 Europe/Rome] Route progetti definite
[14-Dec-2024 23:23:26 Europe/Rome] Route clienti definite
[14-Dec-2024 23:23:26 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:23:26 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:23:26 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 23:23:26 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 23:23:26 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 23:23:27 Europe/Rome] Password verificata con successo
[14-Dec-2024 23:23:27 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[14-Dec-2024 23:23:27 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:23:27 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:23:27 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:23:27 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:23:27 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:23:27 Europe/Rome] Router inizializzato
[14-Dec-2024 23:23:27 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:23:27 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:23:27 Europe/Rome] Route home definita
[14-Dec-2024 23:23:27 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:23:27 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:23:27 Europe/Rome] Route progetti definite
[14-Dec-2024 23:23:27 Europe/Rome] Route clienti definite
[14-Dec-2024 23:23:27 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:23:27 Europe/Rome] URL richiesto: admin
[14-Dec-2024 23:23:27 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 23:23:27 Europe/Rome] Route dispatched
[14-Dec-2024 23:23:48 Europe/Rome] Inizio creazione ZIP con estensione nativa
[14-Dec-2024 23:23:48 Europe/Rome] Source: C:\xampp\htdocs\studio_tecnico
[14-Dec-2024 23:23:48 Europe/Rome] Destination: C:\xampp\htdocs\studio_tecnico/backups/backup_completo_2024-12-14_23-23-48.zip
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: .cursorrules
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: .htaccess
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: api
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: api/clienti
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: api/clienti/elimina.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: api/clienti/lista.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: api/clienti/salva.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: api/config.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: api/log-theme.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: api/pratiche
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: api/pratiche/ultimo-numero.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: api/progetti
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: api/progetti/aggiorna-stato.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: api/progetti/elimina.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: api/progetti/lista.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: app
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: app/classes
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/classes/BackupManager.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: app/Config
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/Config/Database.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: app/controllers
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/AdminController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/AdminProfileController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/AllegatiController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/AuthController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/ClientiController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/ConfigController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/DashboardController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/HomeController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/NotificheController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/PraticheController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/ProgettiController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/controllers/ScadenzeController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: app/core
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/core/Autoloader.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/core/Controller.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/core/Database.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/core/Router.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/core/Security.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: app/helpers
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: app/models
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/models/Allegato.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/models/ClientiModel.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/models/Notifica.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: app/models/User.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: assets
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: assets/css
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: assets/css/login.css
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: assets/css/management.css
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: assets/css/navbar.css
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: assets/css/style.css
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: assets/css/theme.css
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: assets/css/themes.css
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: assets/js
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: assets/js/clienti.js
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: assets/js/main.js
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: assets/js/pratiche.js
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: assets/js/progetti.js
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: assets/libs
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: assets/libs/datatables
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: assets/libs/datatables/it-IT.json
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: assets/svg
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: BackupCompleto.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: backups
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: backups/backup_2024-12-10_15-52-42.sql
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: backups/database_backup_2024-12-12_16-50-53.sql
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: backups/pclzip.lib.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: backups/reset_admin_password.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: backups/test.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: backups/test_db.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: backups/test_login.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: BACKUP_REPORT.md
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: bootstrap.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: clienti
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: clienti/elimina.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: clienti/index.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: clienti/modifica.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: clienti/nuovo.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: config
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: config/app.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: config/config.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: config/database.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: config/routes.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: controllers
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: controllers/ClientiController.php
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunta directory al backup: css
[14-Dec-2024 23:23:48 Europe/Rome] Aggiunto file al backup: css/style.css
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: database
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: database/backup
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: database/backup/backup_2024-12-10_09-01-57.sql
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: database/backup/studio_tecnico_20241211.sql
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: database/create_admin.sql
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: database/migrations
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: database/migrations/create_admin_profile_table.sql
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: database/migrations/create_allegati_table.sql
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: database/migrations/create_notifiche_tables.sql
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: database/setup_admin.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: database/studio_tecnico.sql
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: database/sync_db.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: database/users.sql
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: docs
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: docs/manuale.html
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: docs/next_session.md
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: docs/report.txt
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: docs/webapp_structure.md
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: img
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: img/logo.png
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: includes
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: includes/footer.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: includes/header.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: index.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: logs
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: logs/backups
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: logs/backups/errors_2024-12-14_17-32-32.log
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: logs/backups/errors_2024-12-14_18-10-04.log
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: logs/errors.log
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: logs/error_log.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: logs/log_viewer.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: logs/queries.log
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: logs/test_logs.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: logs/theme.log
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: migrations
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: migrations/add_active_column_to_clients.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: public
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: public/css
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: public/css/dashboard.css
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: public/uploads
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: public/uploads/logo
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: public/uploads/logo/logo_1734047458.png
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: test_backup.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: test_backup_completo.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/admin
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/admin/config.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/admin/index.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/admin/log_viewer.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/admin/profile_setup.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/admin/users
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/admin/users/create.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/admin/users/index.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/auth
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/auth/login.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/auth/profile.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/clienti
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/clienti/dettagli.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/clienti/form.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/clienti/index.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/clienti/modifica.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/clienti/nuovo.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/components
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/components/admin-navbar.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/components/allegati.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/components/default-logo.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/components/navbar.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/components/notifiche.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/components/quick_guide.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/dashboard
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/dashboard/index.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/errors
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/errors/404.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/errors/500.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/errors/csrf.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/home
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/home/<USER>
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/layouts
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/layouts/admin.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/layouts/base.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/layouts/dashboard.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/layouts/footer.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/layouts/header.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/login
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/notifiche
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/notifiche/index.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/pratiche
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/pratiche/dettagli.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/pratiche/dettagli_modal.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/pratiche/index.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/pratiche/modifica.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/pratiche/nuovo.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/progetti
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/progetti/dettagli.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/progetti/dettagli_modal.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/progetti/elimina.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/progetti/index.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/progetti/modifica.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/progetti/nuovo.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunta directory al backup: views/scadenze
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file al backup: views/scadenze/index.php
[14-Dec-2024 23:23:49 Europe/Rome] Aggiunto file di informazioni al backup
[14-Dec-2024 23:23:49 Europe/Rome] File ZIP creato con successo: C:\xampp\htdocs\studio_tecnico/backups/backup_completo_2024-12-14_23-23-48.zip
[14-Dec-2024 23:29:44 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:29:44 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:29:44 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:29:44 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:29:44 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:29:44 Europe/Rome] Router inizializzato
[14-Dec-2024 23:29:44 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:29:44 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:29:44 Europe/Rome] Route home definita
[14-Dec-2024 23:29:44 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:29:44 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:29:44 Europe/Rome] Route progetti definite
[14-Dec-2024 23:29:44 Europe/Rome] Route clienti definite
[14-Dec-2024 23:29:44 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:29:44 Europe/Rome] URL richiesto: logout
[14-Dec-2024 23:29:44 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:29:44 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:29:44 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:29:44 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:29:44 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:29:44 Europe/Rome] Router inizializzato
[14-Dec-2024 23:29:44 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:29:44 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:29:44 Europe/Rome] Route home definita
[14-Dec-2024 23:29:44 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:29:44 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:29:44 Europe/Rome] Route progetti definite
[14-Dec-2024 23:29:44 Europe/Rome] Route clienti definite
[14-Dec-2024 23:29:44 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:29:44 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:29:44 Europe/Rome] Route dispatched
[14-Dec-2024 23:30:14 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:30:14 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:30:14 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:30:14 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:30:14 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:30:14 Europe/Rome] Router inizializzato
[14-Dec-2024 23:30:14 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:30:14 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:30:14 Europe/Rome] Route home definita
[14-Dec-2024 23:30:14 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:30:14 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:30:14 Europe/Rome] Route progetti definite
[14-Dec-2024 23:30:14 Europe/Rome] Route clienti definite
[14-Dec-2024 23:30:14 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:30:14 Europe/Rome] URL richiesto: 
[14-Dec-2024 23:30:14 Europe/Rome] Utente non autenticato, redirect al login
[14-Dec-2024 23:30:14 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:30:14 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:30:14 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:30:14 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:30:14 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:30:14 Europe/Rome] Router inizializzato
[14-Dec-2024 23:30:14 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:30:14 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:30:14 Europe/Rome] Route home definita
[14-Dec-2024 23:30:14 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:30:14 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:30:14 Europe/Rome] Route progetti definite
[14-Dec-2024 23:30:14 Europe/Rome] Route clienti definite
[14-Dec-2024 23:30:14 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:30:14 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:30:14 Europe/Rome] Route dispatched
[14-Dec-2024 23:30:17 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:30:17 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:30:17 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:30:17 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:30:17 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:30:17 Europe/Rome] Router inizializzato
[14-Dec-2024 23:30:17 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:30:17 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:30:17 Europe/Rome] Route home definita
[14-Dec-2024 23:30:17 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:30:17 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:30:17 Europe/Rome] Route progetti definite
[14-Dec-2024 23:30:17 Europe/Rome] Route clienti definite
[14-Dec-2024 23:30:17 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:30:17 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:30:17 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 23:30:17 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 23:30:17 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 23:30:17 Europe/Rome] Password verificata con successo
[14-Dec-2024 23:30:17 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[14-Dec-2024 23:30:17 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:30:17 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:30:17 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:30:17 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:30:17 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:30:17 Europe/Rome] Router inizializzato
[14-Dec-2024 23:30:17 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:30:17 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:30:17 Europe/Rome] Route home definita
[14-Dec-2024 23:30:17 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:30:17 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:30:17 Europe/Rome] Route progetti definite
[14-Dec-2024 23:30:17 Europe/Rome] Route clienti definite
[14-Dec-2024 23:30:17 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:30:17 Europe/Rome] URL richiesto: admin
[14-Dec-2024 23:30:17 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 23:30:17 Europe/Rome] Route dispatched
[14-Dec-2024 23:30:22 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:30:22 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:30:22 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:30:22 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:30:22 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:30:22 Europe/Rome] Router inizializzato
[14-Dec-2024 23:30:22 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:30:22 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:30:22 Europe/Rome] Route home definita
[14-Dec-2024 23:30:22 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:30:22 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:30:22 Europe/Rome] Route progetti definite
[14-Dec-2024 23:30:22 Europe/Rome] Route clienti definite
[14-Dec-2024 23:30:22 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:30:22 Europe/Rome] URL richiesto: admin/users
[14-Dec-2024 23:30:22 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 23:30:22 Europe/Rome] AdminController::users() called
[14-Dec-2024 23:30:22 Europe/Rome] Executing users query with page: 1, perPage: 10
[14-Dec-2024 23:30:22 Europe/Rome] Total users found: 2, Total pages: 1
[14-Dec-2024 23:30:22 Europe/Rome] Executing query: SELECT * FROM users  
                     ORDER BY id DESC LIMIT 10 OFFSET 0
[14-Dec-2024 23:30:22 Europe/Rome] Found 2 users
[14-Dec-2024 23:30:22 Europe/Rome] Users data: Array
(
    [0] => Array
        (
            [id] => 6
            [username] => Mauro
            [email] => 
            [password] => $2y$10$RQ704WI9LMMrkTkX4A8OjOMDm12m6ovhOVpkT1C2RRSwPwx3W/a7e
            [ruolo] => user
            [active] => 1
        )

    [1] => Array
        (
            [id] => 5
            [username] => admin
            [email] => <EMAIL>
            [password] => $2y$10$VT/tvsPz72/pHDdpDdGCieS6CqO/ouQhwAcuMHsoDZxUwR2YXHsdC
            [ruolo] => admin
            [active] => 1
        )

)

[14-Dec-2024 23:30:22 Europe/Rome] Route dispatched
[14-Dec-2024 23:30:31 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:30:31 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:30:31 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:30:31 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:30:31 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:30:31 Europe/Rome] Router inizializzato
[14-Dec-2024 23:30:31 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:30:31 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:30:31 Europe/Rome] Route home definita
[14-Dec-2024 23:30:31 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:30:31 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:30:31 Europe/Rome] Route progetti definite
[14-Dec-2024 23:30:31 Europe/Rome] Route clienti definite
[14-Dec-2024 23:30:31 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:30:31 Europe/Rome] URL richiesto: logout
[14-Dec-2024 23:30:31 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:30:31 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:30:31 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:30:31 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:30:31 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:30:31 Europe/Rome] Router inizializzato
[14-Dec-2024 23:30:31 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:30:31 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:30:31 Europe/Rome] Route home definita
[14-Dec-2024 23:30:31 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:30:31 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:30:31 Europe/Rome] Route progetti definite
[14-Dec-2024 23:30:31 Europe/Rome] Route clienti definite
[14-Dec-2024 23:30:31 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:30:31 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:30:31 Europe/Rome] Route dispatched
[14-Dec-2024 23:38:36 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:38:36 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:38:36 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:38:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:38:36 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:38:36 Europe/Rome] Router inizializzato
[14-Dec-2024 23:38:36 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:38:36 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:38:36 Europe/Rome] Route home definita
[14-Dec-2024 23:38:36 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:38:36 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:38:36 Europe/Rome] Route progetti definite
[14-Dec-2024 23:38:36 Europe/Rome] Route clienti definite
[14-Dec-2024 23:38:36 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:38:36 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:38:36 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 23:38:36 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 23:38:36 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 23:38:36 Europe/Rome] Password verificata con successo
[14-Dec-2024 23:38:36 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[14-Dec-2024 23:38:36 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:38:36 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:38:36 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:38:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:38:36 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:38:36 Europe/Rome] Router inizializzato
[14-Dec-2024 23:38:36 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:38:36 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:38:36 Europe/Rome] Route home definita
[14-Dec-2024 23:38:36 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:38:36 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:38:36 Europe/Rome] Route progetti definite
[14-Dec-2024 23:38:36 Europe/Rome] Route clienti definite
[14-Dec-2024 23:38:36 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:38:36 Europe/Rome] URL richiesto: admin
[14-Dec-2024 23:38:36 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 23:38:36 Europe/Rome] Route dispatched
[14-Dec-2024 23:38:39 Europe/Rome] Inizio creazione ZIP con estensione nativa
[14-Dec-2024 23:38:39 Europe/Rome] Source: C:\xampp\htdocs\studio_tecnico
[14-Dec-2024 23:38:39 Europe/Rome] Destination: C:\xampp\htdocs\studio_tecnico/backups/backup_completo_2024-12-14_23-38-39.zip
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: .cursorrules
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: .htaccess
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: api
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: api/clienti
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: api/clienti/elimina.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: api/clienti/lista.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: api/clienti/salva.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: api/config.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: api/log-theme.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: api/pratiche
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: api/pratiche/ultimo-numero.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: api/progetti
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: api/progetti/aggiorna-stato.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: api/progetti/elimina.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: api/progetti/lista.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: app
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: app/classes
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/classes/BackupManager.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: app/Config
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/Config/Database.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: app/controllers
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/AdminController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/AdminProfileController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/AllegatiController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/AuthController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/ClientiController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/ConfigController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/DashboardController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/HomeController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/NotificheController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/PraticheController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/ProgettiController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/controllers/ScadenzeController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: app/core
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/core/Autoloader.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/core/Controller.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/core/Database.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/core/Router.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/core/Security.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: app/helpers
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: app/models
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/models/Allegato.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/models/ClientiModel.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/models/Notifica.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: app/models/User.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: assets
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: assets/css
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: assets/css/login.css
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: assets/css/management.css
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: assets/css/navbar.css
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: assets/css/style.css
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: assets/css/theme.css
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: assets/css/themes.css
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: assets/js
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: assets/js/clienti.js
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: assets/js/main.js
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: assets/js/pratiche.js
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: assets/js/progetti.js
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: assets/libs
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: assets/libs/datatables
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: assets/libs/datatables/it-IT.json
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: assets/svg
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: BackupCompleto.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: backups
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: backups/backup_2024-12-10_15-52-42.sql
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: backups/backup_completo_2024-12-14_23-23-48.zip
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: backups/database_backup_2024-12-12_16-50-53.sql
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: backups/pclzip.lib.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: backups/reset_admin_password.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: backups/test.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: backups/test_db.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: backups/test_login.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: BACKUP_REPORT.md
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: bootstrap.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: clienti
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: clienti/elimina.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: clienti/index.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: clienti/modifica.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: clienti/nuovo.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: config
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: config/app.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: config/config.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: config/database.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: config/routes.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: controllers
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: controllers/ClientiController.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: css
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: css/style.css
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: database
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: database/backup
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: database/backup/backup_2024-12-10_09-01-57.sql
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: database/backup/studio_tecnico_20241211.sql
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: database/create_admin.sql
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: database/migrations
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: database/migrations/create_admin_profile_table.sql
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: database/migrations/create_allegati_table.sql
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: database/migrations/create_notifiche_tables.sql
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: database/setup_admin.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: database/studio_tecnico.sql
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: database/sync_db.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: database/users.sql
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: docs
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: docs/manuale.html
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: docs/next_session.md
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: docs/report.txt
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: docs/webapp_structure.md
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: img
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: img/logo.png
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: includes
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: includes/footer.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: includes/header.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: index.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: logs
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: logs/backups
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: logs/backups/errors_2024-12-14_17-32-32.log
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: logs/backups/errors_2024-12-14_18-10-04.log
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: logs/errors.log
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: logs/error_log.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: logs/log_viewer.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: logs/queries.log
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: logs/test_logs.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: logs/theme.log
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: migrations
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: migrations/add_active_column_to_clients.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: public
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: public/css
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: public/css/dashboard.css
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: public/uploads
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: public/uploads/logo
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: public/uploads/logo/logo_1734047458.png
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: test_backup.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: test_backup_completo.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/admin
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/admin/config.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/admin/index.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/admin/log_viewer.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/admin/profile_setup.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/admin/users
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/admin/users/create.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/admin/users/index.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/auth
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/auth/login.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/auth/profile.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/clienti
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/clienti/dettagli.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/clienti/form.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/clienti/index.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/clienti/modifica.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/clienti/nuovo.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/components
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/components/admin-navbar.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/components/allegati.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/components/default-logo.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/components/navbar.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/components/notifiche.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/components/quick_guide.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/dashboard
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/dashboard/index.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/errors
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/errors/404.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/errors/500.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/errors/csrf.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/home
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/home/<USER>
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/layouts
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/layouts/admin.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/layouts/base.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/layouts/dashboard.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/layouts/footer.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/layouts/header.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/login
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/notifiche
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/notifiche/index.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/pratiche
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/pratiche/dettagli.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/pratiche/dettagli_modal.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/pratiche/index.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/pratiche/modifica.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/pratiche/nuovo.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/progetti
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/progetti/dettagli.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/progetti/dettagli_modal.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/progetti/elimina.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/progetti/index.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/progetti/modifica.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/progetti/nuovo.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunta directory al backup: views/scadenze
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file al backup: views/scadenze/index.php
[14-Dec-2024 23:38:39 Europe/Rome] Aggiunto file di informazioni al backup
[14-Dec-2024 23:38:39 Europe/Rome] File ZIP creato con successo: C:\xampp\htdocs\studio_tecnico/backups/backup_completo_2024-12-14_23-38-39.zip
[14-Dec-2024 23:38:49 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:38:49 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:38:49 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:38:49 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:38:49 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:38:49 Europe/Rome] Router inizializzato
[14-Dec-2024 23:38:49 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:38:49 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:38:49 Europe/Rome] Route home definita
[14-Dec-2024 23:38:49 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:38:49 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:38:49 Europe/Rome] Route progetti definite
[14-Dec-2024 23:38:49 Europe/Rome] Route clienti definite
[14-Dec-2024 23:38:49 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:38:49 Europe/Rome] URL richiesto: logout
[14-Dec-2024 23:38:49 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:38:49 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:38:49 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:38:49 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:38:49 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:38:49 Europe/Rome] Router inizializzato
[14-Dec-2024 23:38:49 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:38:49 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:38:49 Europe/Rome] Route home definita
[14-Dec-2024 23:38:49 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:38:49 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:38:49 Europe/Rome] Route progetti definite
[14-Dec-2024 23:38:49 Europe/Rome] Route clienti definite
[14-Dec-2024 23:38:49 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:38:49 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:38:49 Europe/Rome] Route dispatched
[14-Dec-2024 23:47:19 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:47:19 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:47:19 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:47:19 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:47:19 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:47:19 Europe/Rome] Router inizializzato
[14-Dec-2024 23:47:19 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:47:19 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:47:19 Europe/Rome] Route home definita
[14-Dec-2024 23:47:19 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:47:19 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:47:19 Europe/Rome] Route progetti definite
[14-Dec-2024 23:47:19 Europe/Rome] Route clienti definite
[14-Dec-2024 23:47:19 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:47:19 Europe/Rome] URL richiesto: 
[14-Dec-2024 23:47:19 Europe/Rome] Utente non autenticato, redirect al login
[14-Dec-2024 23:47:19 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:47:19 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:47:19 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:47:19 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:47:19 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:47:19 Europe/Rome] Router inizializzato
[14-Dec-2024 23:47:19 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:47:19 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:47:19 Europe/Rome] Route home definita
[14-Dec-2024 23:47:19 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:47:19 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:47:19 Europe/Rome] Route progetti definite
[14-Dec-2024 23:47:19 Europe/Rome] Route clienti definite
[14-Dec-2024 23:47:19 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:47:19 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:47:19 Europe/Rome] Route dispatched
[14-Dec-2024 23:47:23 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:47:23 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:47:23 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:47:23 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:47:23 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:47:23 Europe/Rome] Router inizializzato
[14-Dec-2024 23:47:23 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:47:23 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:47:23 Europe/Rome] Route home definita
[14-Dec-2024 23:47:23 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:47:23 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:47:23 Europe/Rome] Route progetti definite
[14-Dec-2024 23:47:23 Europe/Rome] Route clienti definite
[14-Dec-2024 23:47:23 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:47:23 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:47:23 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 23:47:23 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 23:47:23 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 23:47:23 Europe/Rome] Password verificata con successo
[14-Dec-2024 23:47:23 Europe/Rome] Login riuscito per l'utente: Mauro con ruolo: user
[14-Dec-2024 23:47:23 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:47:23 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:47:23 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:47:23 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:47:23 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:47:23 Europe/Rome] Router inizializzato
[14-Dec-2024 23:47:23 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:47:23 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:47:23 Europe/Rome] Route home definita
[14-Dec-2024 23:47:23 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:47:23 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:47:23 Europe/Rome] Route progetti definite
[14-Dec-2024 23:47:23 Europe/Rome] Route clienti definite
[14-Dec-2024 23:47:23 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:47:23 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:47:23 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:47:23 Europe/Rome] 
==================================================
[14-Dec-2024 23:47:23 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:47:23 Europe/Rome] ==================================================

[14-Dec-2024 23:47:23 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:47:23 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:47:23 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:47:23 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:47:23 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:47:23 Europe/Rome] 
==================================================
[14-Dec-2024 23:47:23 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:47:23 Europe/Rome] ==================================================

[14-Dec-2024 23:47:23 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:47:23 Europe/Rome] Route dispatched
[14-Dec-2024 23:47:29 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:47:29 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:47:29 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:47:29 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:47:29 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:47:29 Europe/Rome] Router inizializzato
[14-Dec-2024 23:47:29 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:47:29 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:47:29 Europe/Rome] Route home definita
[14-Dec-2024 23:47:29 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:47:29 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:47:29 Europe/Rome] Route progetti definite
[14-Dec-2024 23:47:29 Europe/Rome] Route clienti definite
[14-Dec-2024 23:47:29 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:47:29 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 23:47:29 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:47:29 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:47:29 Europe/Rome] Route dispatched
[14-Dec-2024 23:47:36 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:47:36 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:47:36 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:47:36 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:47:36 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:47:36 Europe/Rome] Router inizializzato
[14-Dec-2024 23:47:36 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:47:36 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:47:36 Europe/Rome] Route home definita
[14-Dec-2024 23:47:36 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:47:36 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:47:36 Europe/Rome] Route progetti definite
[14-Dec-2024 23:47:36 Europe/Rome] Route clienti definite
[14-Dec-2024 23:47:36 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:47:36 Europe/Rome] URL richiesto: progetti/nuovo
[14-Dec-2024 23:47:36 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:47:36 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:47:36 Europe/Rome] Route dispatched
[14-Dec-2024 23:47:43 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:47:43 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:47:43 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:47:43 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:47:43 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:47:43 Europe/Rome] Router inizializzato
[14-Dec-2024 23:47:43 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:47:43 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:47:43 Europe/Rome] Route home definita
[14-Dec-2024 23:47:43 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:47:43 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:47:43 Europe/Rome] Route progetti definite
[14-Dec-2024 23:47:43 Europe/Rome] Route clienti definite
[14-Dec-2024 23:47:43 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:47:43 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 23:47:43 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:47:43 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:47:43 Europe/Rome] Route dispatched
[14-Dec-2024 23:47:44 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:47:44 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:47:44 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:47:45 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:47:45 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:47:45 Europe/Rome] Router inizializzato
[14-Dec-2024 23:47:45 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:47:45 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:47:45 Europe/Rome] Route home definita
[14-Dec-2024 23:47:45 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:47:45 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:47:45 Europe/Rome] Route progetti definite
[14-Dec-2024 23:47:45 Europe/Rome] Route clienti definite
[14-Dec-2024 23:47:45 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:47:45 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 23:47:45 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:47:45 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 23:47:45 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_documento, p.tipo_pratica) as tipo_documento
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 23:47:45 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 23:47:45 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 23:47:45 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => 
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 23:47:45 Europe/Rome] Route dispatched
[14-Dec-2024 23:47:45 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:47:45 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => 
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 23:47:45 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 23:47:45 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 23:47:59 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:47:59 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:47:59 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:47:59 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:47:59 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:47:59 Europe/Rome] Router inizializzato
[14-Dec-2024 23:47:59 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:47:59 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:47:59 Europe/Rome] Route home definita
[14-Dec-2024 23:47:59 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:47:59 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:47:59 Europe/Rome] Route progetti definite
[14-Dec-2024 23:47:59 Europe/Rome] Route clienti definite
[14-Dec-2024 23:47:59 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:47:59 Europe/Rome] URL richiesto: logout
[14-Dec-2024 23:47:59 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:47:59 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:47:59 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:47:59 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:47:59 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:47:59 Europe/Rome] Router inizializzato
[14-Dec-2024 23:47:59 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:47:59 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:47:59 Europe/Rome] Route home definita
[14-Dec-2024 23:47:59 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:47:59 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:47:59 Europe/Rome] Route progetti definite
[14-Dec-2024 23:47:59 Europe/Rome] Route clienti definite
[14-Dec-2024 23:47:59 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:47:59 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:47:59 Europe/Rome] Route dispatched
[14-Dec-2024 23:48:10 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:48:10 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:48:10 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:48:10 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:48:10 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:48:10 Europe/Rome] Router inizializzato
[14-Dec-2024 23:48:10 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:48:10 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:48:10 Europe/Rome] Route home definita
[14-Dec-2024 23:48:10 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:48:10 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:48:10 Europe/Rome] Route progetti definite
[14-Dec-2024 23:48:10 Europe/Rome] Route clienti definite
[14-Dec-2024 23:48:10 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:48:10 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:48:10 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 23:48:10 Europe/Rome] Tentativo di login per username: Mauro
[14-Dec-2024 23:48:10 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 23:48:10 Europe/Rome] Password verificata con successo
[14-Dec-2024 23:48:10 Europe/Rome] Login riuscito per l'utente: Mauro con ruolo: user
[14-Dec-2024 23:48:10 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:48:10 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:48:10 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:48:10 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:48:10 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:48:10 Europe/Rome] Router inizializzato
[14-Dec-2024 23:48:10 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:48:10 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:48:10 Europe/Rome] Route home definita
[14-Dec-2024 23:48:10 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:48:10 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:48:10 Europe/Rome] Route progetti definite
[14-Dec-2024 23:48:10 Europe/Rome] Route clienti definite
[14-Dec-2024 23:48:10 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:48:10 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:48:10 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:48:10 Europe/Rome] 
==================================================
[14-Dec-2024 23:48:10 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:48:10 Europe/Rome] ==================================================

[14-Dec-2024 23:48:10 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:48:10 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:48:10 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:48:10 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:48:10 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:48:10 Europe/Rome] 
==================================================
[14-Dec-2024 23:48:10 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:48:10 Europe/Rome] ==================================================

[14-Dec-2024 23:48:10 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:48:10 Europe/Rome] Route dispatched
[14-Dec-2024 23:48:14 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:48:14 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:48:14 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:48:14 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:48:14 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:48:14 Europe/Rome] Router inizializzato
[14-Dec-2024 23:48:14 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:48:14 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:48:14 Europe/Rome] Route home definita
[14-Dec-2024 23:48:14 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:48:14 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:48:14 Europe/Rome] Route progetti definite
[14-Dec-2024 23:48:14 Europe/Rome] Route clienti definite
[14-Dec-2024 23:48:14 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:48:14 Europe/Rome] URL richiesto: profilo
[14-Dec-2024 23:48:14 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:48:14 Europe/Rome] Route dispatched
[14-Dec-2024 23:48:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:48:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:48:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:48:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:48:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:48:24 Europe/Rome] Router inizializzato
[14-Dec-2024 23:48:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:48:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:48:24 Europe/Rome] Route home definita
[14-Dec-2024 23:48:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:48:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:48:24 Europe/Rome] Route progetti definite
[14-Dec-2024 23:48:24 Europe/Rome] Route clienti definite
[14-Dec-2024 23:48:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:48:24 Europe/Rome] URL richiesto: 
[14-Dec-2024 23:48:24 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:48:24 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:48:24 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:48:24 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:48:24 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:48:24 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:48:24 Europe/Rome] Router inizializzato
[14-Dec-2024 23:48:24 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:48:24 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:48:24 Europe/Rome] Route home definita
[14-Dec-2024 23:48:24 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:48:24 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:48:24 Europe/Rome] Route progetti definite
[14-Dec-2024 23:48:24 Europe/Rome] Route clienti definite
[14-Dec-2024 23:48:24 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:48:24 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:48:24 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:48:24 Europe/Rome] 
==================================================
[14-Dec-2024 23:48:24 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:48:24 Europe/Rome] ==================================================

[14-Dec-2024 23:48:24 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:48:24 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:48:24 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:48:24 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:48:24 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:48:24 Europe/Rome] 
==================================================
[14-Dec-2024 23:48:24 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:48:24 Europe/Rome] ==================================================

[14-Dec-2024 23:48:24 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:48:24 Europe/Rome] Route dispatched
[14-Dec-2024 23:48:46 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:48:46 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:48:46 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:48:46 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:48:46 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:48:46 Europe/Rome] Router inizializzato
[14-Dec-2024 23:48:46 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:48:46 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:48:46 Europe/Rome] Route home definita
[14-Dec-2024 23:48:46 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:48:46 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:48:46 Europe/Rome] Route progetti definite
[14-Dec-2024 23:48:46 Europe/Rome] Route clienti definite
[14-Dec-2024 23:48:46 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:48:46 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 23:48:46 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:48:46 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:48:46 Europe/Rome] Route dispatched
[14-Dec-2024 23:48:49 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:48:49 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:48:49 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:48:49 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:48:49 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:48:49 Europe/Rome] Router inizializzato
[14-Dec-2024 23:48:49 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:48:49 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:48:49 Europe/Rome] Route home definita
[14-Dec-2024 23:48:49 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:48:49 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:48:49 Europe/Rome] Route progetti definite
[14-Dec-2024 23:48:49 Europe/Rome] Route clienti definite
[14-Dec-2024 23:48:49 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:48:49 Europe/Rome] URL richiesto: clienti
[14-Dec-2024 23:48:49 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:48:50 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:48:50 Europe/Rome] Route dispatched
[14-Dec-2024 23:48:51 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:48:51 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:48:51 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:48:51 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:48:51 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:48:51 Europe/Rome] Router inizializzato
[14-Dec-2024 23:48:51 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:48:51 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:48:51 Europe/Rome] Route home definita
[14-Dec-2024 23:48:51 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:48:51 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:48:51 Europe/Rome] Route progetti definite
[14-Dec-2024 23:48:51 Europe/Rome] Route clienti definite
[14-Dec-2024 23:48:51 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:48:51 Europe/Rome] URL richiesto: 
[14-Dec-2024 23:48:51 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:48:51 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:48:51 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:48:51 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:48:51 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:48:51 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:48:51 Europe/Rome] Router inizializzato
[14-Dec-2024 23:48:51 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:48:51 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:48:51 Europe/Rome] Route home definita
[14-Dec-2024 23:48:51 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:48:51 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:48:51 Europe/Rome] Route progetti definite
[14-Dec-2024 23:48:51 Europe/Rome] Route clienti definite
[14-Dec-2024 23:48:51 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:48:51 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:48:51 Europe/Rome] Utente autenticato: Mauro
[14-Dec-2024 23:48:51 Europe/Rome] 
==================================================
[14-Dec-2024 23:48:51 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:48:51 Europe/Rome] ==================================================

[14-Dec-2024 23:48:51 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:48:51 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:48:51 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:48:51 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:48:51 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:48:51 Europe/Rome] 
==================================================
[14-Dec-2024 23:48:51 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:48:51 Europe/Rome] ==================================================

[14-Dec-2024 23:48:51 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[14-Dec-2024 23:48:51 Europe/Rome] Route dispatched
[14-Dec-2024 23:49:12 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:49:12 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:49:12 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:49:12 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:49:12 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:49:12 Europe/Rome] Router inizializzato
[14-Dec-2024 23:49:12 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:49:12 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:49:12 Europe/Rome] Route home definita
[14-Dec-2024 23:49:12 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:49:12 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:49:12 Europe/Rome] Route progetti definite
[14-Dec-2024 23:49:12 Europe/Rome] Route clienti definite
[14-Dec-2024 23:49:12 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:49:12 Europe/Rome] URL richiesto: logout
[14-Dec-2024 23:49:12 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:49:12 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:49:12 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:49:12 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:49:12 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:49:12 Europe/Rome] Router inizializzato
[14-Dec-2024 23:49:12 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:49:12 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:49:12 Europe/Rome] Route home definita
[14-Dec-2024 23:49:12 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:49:12 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:49:12 Europe/Rome] Route progetti definite
[14-Dec-2024 23:49:12 Europe/Rome] Route clienti definite
[14-Dec-2024 23:49:12 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:49:12 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:49:12 Europe/Rome] Route dispatched
[14-Dec-2024 23:49:15 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:49:15 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:49:15 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:49:15 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:49:15 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:49:15 Europe/Rome] Router inizializzato
[14-Dec-2024 23:49:15 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:49:15 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:49:15 Europe/Rome] Route home definita
[14-Dec-2024 23:49:15 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:49:15 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:49:15 Europe/Rome] Route progetti definite
[14-Dec-2024 23:49:15 Europe/Rome] Route clienti definite
[14-Dec-2024 23:49:15 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:49:15 Europe/Rome] URL richiesto: login
[14-Dec-2024 23:49:15 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 23:49:15 Europe/Rome] Tentativo di login per username: admin
[14-Dec-2024 23:49:15 Europe/Rome] Utente trovato, verifica della password...
[14-Dec-2024 23:49:15 Europe/Rome] Password verificata con successo
[14-Dec-2024 23:49:15 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[14-Dec-2024 23:49:15 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:49:15 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:49:15 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:49:15 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:49:15 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:49:15 Europe/Rome] Router inizializzato
[14-Dec-2024 23:49:15 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:49:15 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:49:15 Europe/Rome] Route home definita
[14-Dec-2024 23:49:15 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:49:15 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:49:15 Europe/Rome] Route progetti definite
[14-Dec-2024 23:49:15 Europe/Rome] Route clienti definite
[14-Dec-2024 23:49:15 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:49:15 Europe/Rome] URL richiesto: admin
[14-Dec-2024 23:49:15 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 23:49:15 Europe/Rome] Route dispatched
[14-Dec-2024 23:52:08 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:52:08 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:52:08 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:52:08 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:52:08 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:52:08 Europe/Rome] Router inizializzato
[14-Dec-2024 23:52:08 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:52:08 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:52:08 Europe/Rome] Route home definita
[14-Dec-2024 23:52:08 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:52:08 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:52:08 Europe/Rome] Route progetti definite
[14-Dec-2024 23:52:08 Europe/Rome] Route clienti definite
[14-Dec-2024 23:52:08 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:52:08 Europe/Rome] URL richiesto: admin
[14-Dec-2024 23:52:08 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 23:52:08 Europe/Rome] Route dispatched
[14-Dec-2024 23:52:14 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:52:14 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:52:14 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:52:14 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:52:14 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:52:14 Europe/Rome] Router inizializzato
[14-Dec-2024 23:52:14 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:52:14 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:52:14 Europe/Rome] Route home definita
[14-Dec-2024 23:52:14 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:52:14 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:52:14 Europe/Rome] Route progetti definite
[14-Dec-2024 23:52:14 Europe/Rome] Route clienti definite
[14-Dec-2024 23:52:14 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:52:14 Europe/Rome] URL richiesto: admin
[14-Dec-2024 23:52:14 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 23:52:14 Europe/Rome] Route dispatched
[14-Dec-2024 23:53:50 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:53:50 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:53:50 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:53:50 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:53:50 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:53:50 Europe/Rome] Router inizializzato
[14-Dec-2024 23:53:50 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:53:50 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:53:50 Europe/Rome] Route home definita
[14-Dec-2024 23:53:50 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:53:50 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:53:50 Europe/Rome] Route progetti definite
[14-Dec-2024 23:53:50 Europe/Rome] Route clienti definite
[14-Dec-2024 23:53:50 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:53:50 Europe/Rome] URL richiesto: admin
[14-Dec-2024 23:53:50 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 23:53:50 Europe/Rome] Route dispatched
[14-Dec-2024 23:53:52 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:53:52 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:53:52 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:53:52 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:53:52 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:53:52 Europe/Rome] Router inizializzato
[14-Dec-2024 23:53:52 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:53:52 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:53:52 Europe/Rome] Route home definita
[14-Dec-2024 23:53:52 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:53:52 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:53:52 Europe/Rome] Route progetti definite
[14-Dec-2024 23:53:52 Europe/Rome] Route clienti definite
[14-Dec-2024 23:53:52 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:53:52 Europe/Rome] URL richiesto: admin
[14-Dec-2024 23:53:52 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 23:53:52 Europe/Rome] Route dispatched
[14-Dec-2024 23:56:48 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:56:48 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:56:48 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:56:48 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:56:48 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:56:48 Europe/Rome] Router inizializzato
[14-Dec-2024 23:56:48 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:56:48 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:56:48 Europe/Rome] Route home definita
[14-Dec-2024 23:56:48 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:56:48 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:56:48 Europe/Rome] Route progetti definite
[14-Dec-2024 23:56:48 Europe/Rome] Route clienti definite
[14-Dec-2024 23:56:48 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:56:48 Europe/Rome] URL richiesto: admin
[14-Dec-2024 23:56:48 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 23:56:48 Europe/Rome] Route dispatched
[14-Dec-2024 23:56:50 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:56:50 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:56:50 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:56:50 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:56:50 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:56:50 Europe/Rome] Router inizializzato
[14-Dec-2024 23:56:50 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:56:50 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:56:50 Europe/Rome] Route home definita
[14-Dec-2024 23:56:50 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:56:50 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:56:50 Europe/Rome] Route progetti definite
[14-Dec-2024 23:56:50 Europe/Rome] Route clienti definite
[14-Dec-2024 23:56:50 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:56:50 Europe/Rome] URL richiesto: admin
[14-Dec-2024 23:56:50 Europe/Rome] Accesso admin verificato: admin
[14-Dec-2024 23:56:50 Europe/Rome] Route dispatched
[14-Dec-2024 23:56:53 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:56:53 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:56:53 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:56:53 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:56:53 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:56:53 Europe/Rome] Router inizializzato
[14-Dec-2024 23:56:53 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:56:53 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:56:53 Europe/Rome] Route home definita
[14-Dec-2024 23:56:53 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:56:53 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:56:53 Europe/Rome] Route progetti definite
[14-Dec-2024 23:56:53 Europe/Rome] Route clienti definite
[14-Dec-2024 23:56:53 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:56:53 Europe/Rome] URL richiesto: 
[14-Dec-2024 23:56:53 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 23:56:53 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:56:53 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:56:53 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:56:53 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:56:53 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:56:53 Europe/Rome] Router inizializzato
[14-Dec-2024 23:56:53 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:56:53 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:56:53 Europe/Rome] Route home definita
[14-Dec-2024 23:56:53 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:56:53 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:56:53 Europe/Rome] Route progetti definite
[14-Dec-2024 23:56:53 Europe/Rome] Route clienti definite
[14-Dec-2024 23:56:53 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:56:53 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:56:53 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 23:56:53 Europe/Rome] 
==================================================
[14-Dec-2024 23:56:53 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:56:53 Europe/Rome] ==================================================

[14-Dec-2024 23:56:53 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:56:53 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:56:53 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:56:53 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:56:53 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:56:53 Europe/Rome] 
==================================================
[14-Dec-2024 23:56:53 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:56:53 Europe/Rome] ==================================================

[14-Dec-2024 23:56:53 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[2024-12-14 23:56:53][🟡 WARN ][admin@::1][error_log.php:94] Undefined array key "total_users"
[14-Dec-2024 23:56:53 Europe/Rome] PHP Warning:  Undefined array key "total_users" in C:\xampp\htdocs\studio_tecnico\views\dashboard\index.php on line 169
[2024-12-14 23:56:53][🟠 ERROR][admin@::1][error_log.php:94] number_format(): Passing null to parameter #1 ($num) of type float is deprecated
[14-Dec-2024 23:56:53 Europe/Rome] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\xampp\htdocs\studio_tecnico\views\dashboard\index.php on line 169
[14-Dec-2024 23:56:53 Europe/Rome] Route dispatched
[14-Dec-2024 23:57:49 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:57:49 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:57:49 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:57:49 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:57:49 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:57:49 Europe/Rome] Router inizializzato
[14-Dec-2024 23:57:49 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:57:49 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:57:49 Europe/Rome] Route home definita
[14-Dec-2024 23:57:49 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:57:49 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:57:49 Europe/Rome] Route progetti definite
[14-Dec-2024 23:57:49 Europe/Rome] Route clienti definite
[14-Dec-2024 23:57:49 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:57:49 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:57:49 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 23:57:49 Europe/Rome] 
==================================================
[14-Dec-2024 23:57:49 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:57:49 Europe/Rome] ==================================================

[14-Dec-2024 23:57:49 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:57:49 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:57:49 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:57:49 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:57:49 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:57:49 Europe/Rome] 
==================================================
[14-Dec-2024 23:57:49 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:57:49 Europe/Rome] ==================================================

[14-Dec-2024 23:57:49 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 23:57:49 Europe/Rome] Route dispatched
[14-Dec-2024 23:59:00 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:59:00 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:59:00 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:59:00 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:59:00 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:59:00 Europe/Rome] Router inizializzato
[14-Dec-2024 23:59:00 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:59:00 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:59:00 Europe/Rome] Route home definita
[14-Dec-2024 23:59:00 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:59:00 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:59:00 Europe/Rome] Route progetti definite
[14-Dec-2024 23:59:00 Europe/Rome] Route clienti definite
[14-Dec-2024 23:59:00 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:59:00 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:59:00 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 23:59:00 Europe/Rome] 
==================================================
[14-Dec-2024 23:59:00 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:59:00 Europe/Rome] ==================================================

[14-Dec-2024 23:59:00 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:59:00 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:59:00 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:59:00 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:59:00 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:59:00 Europe/Rome] 
==================================================
[14-Dec-2024 23:59:00 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:59:00 Europe/Rome] ==================================================

[14-Dec-2024 23:59:00 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 23:59:00 Europe/Rome] Route dispatched
[14-Dec-2024 23:59:16 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:59:16 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:59:16 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:59:16 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:59:16 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:59:16 Europe/Rome] Router inizializzato
[14-Dec-2024 23:59:16 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:59:16 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:59:16 Europe/Rome] Route home definita
[14-Dec-2024 23:59:16 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:59:16 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:59:16 Europe/Rome] Route progetti definite
[14-Dec-2024 23:59:16 Europe/Rome] Route clienti definite
[14-Dec-2024 23:59:16 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:59:16 Europe/Rome] URL richiesto: 
[14-Dec-2024 23:59:16 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 23:59:16 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:59:16 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:59:16 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:59:16 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:59:16 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:59:16 Europe/Rome] Router inizializzato
[14-Dec-2024 23:59:16 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:59:16 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:59:16 Europe/Rome] Route home definita
[14-Dec-2024 23:59:16 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:59:16 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:59:16 Europe/Rome] Route progetti definite
[14-Dec-2024 23:59:16 Europe/Rome] Route clienti definite
[14-Dec-2024 23:59:16 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:59:16 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:59:16 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 23:59:16 Europe/Rome] 
==================================================
[14-Dec-2024 23:59:16 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:59:16 Europe/Rome] ==================================================

[14-Dec-2024 23:59:16 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:59:16 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:59:16 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:59:16 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:59:16 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:59:16 Europe/Rome] 
==================================================
[14-Dec-2024 23:59:16 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:59:16 Europe/Rome] ==================================================

[14-Dec-2024 23:59:16 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 23:59:16 Europe/Rome] Route dispatched
[14-Dec-2024 23:59:44 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:59:44 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:59:44 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:59:44 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:59:44 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:59:44 Europe/Rome] Router inizializzato
[14-Dec-2024 23:59:44 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:59:44 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:59:44 Europe/Rome] Route home definita
[14-Dec-2024 23:59:44 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:59:44 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:59:44 Europe/Rome] Route progetti definite
[14-Dec-2024 23:59:44 Europe/Rome] Route clienti definite
[14-Dec-2024 23:59:44 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:59:44 Europe/Rome] URL richiesto: clienti
[14-Dec-2024 23:59:44 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 23:59:44 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 23:59:44 Europe/Rome] Route dispatched
[14-Dec-2024 23:59:48 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:59:48 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:59:48 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:59:48 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:59:48 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:59:48 Europe/Rome] Router inizializzato
[14-Dec-2024 23:59:48 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:59:48 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:59:48 Europe/Rome] Route home definita
[14-Dec-2024 23:59:48 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:59:48 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:59:48 Europe/Rome] Route progetti definite
[14-Dec-2024 23:59:48 Europe/Rome] Route clienti definite
[14-Dec-2024 23:59:48 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:59:48 Europe/Rome] URL richiesto: progetti
[14-Dec-2024 23:59:48 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 23:59:48 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 23:59:48 Europe/Rome] Route dispatched
[14-Dec-2024 23:59:49 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:59:49 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:59:49 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:59:49 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:59:49 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:59:49 Europe/Rome] Router inizializzato
[14-Dec-2024 23:59:49 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:59:49 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:59:49 Europe/Rome] Route home definita
[14-Dec-2024 23:59:49 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:59:49 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:59:49 Europe/Rome] Route progetti definite
[14-Dec-2024 23:59:49 Europe/Rome] Route clienti definite
[14-Dec-2024 23:59:49 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:59:49 Europe/Rome] URL richiesto: pratiche
[14-Dec-2024 23:59:49 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 23:59:49 Europe/Rome] Inizio recupero pratiche
[14-Dec-2024 23:59:49 Europe/Rome] Query SQL: 
                SELECT p.*, pr.nome_progetto, 
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome,
                       COALESCE(p.tipo_documento, p.tipo_pratica) as tipo_documento
                FROM pratiche p 
                JOIN progetti pr ON p.progetto_id = pr.id 
                JOIN clienti c ON pr.cliente_id = c.id 
                ORDER BY p.data_apertura DESC
            
[14-Dec-2024 23:59:49 Europe/Rome] Numero di pratiche trovate: 1
[14-Dec-2024 23:59:49 Europe/Rome] Pratica trovata - ID: 1, Numero: 1/2025, Cliente: Mauro Mazzarelli, Progetto: Casa, Tipo: cila, Stato: in_attesa
[14-Dec-2024 23:59:49 Europe/Rome] Debug controller - Dati passati alla vista: Array
(
    [pratiche] => Array
        (
            [0] => Array
                (
                    [id] => 1
                    [progetto_id] => 1
                    [tipo_pratica] => 
                    [stato] => in_attesa
                    [data_apertura] => 2024-12-08 00:00:00
                    [data_scadenza] => 2025-02-28
                    [note] => 
                    [numero_pratica] => 1/2025
                    [tipo_documento] => cila
                    [ente_riferimento] => 
                    [protocollo] => 34566
                    [data_protocollo] => 2025-01-17
                    [data_scadenza_integrazione] => 
                    [importo_diritti] => 
                    [note_interne] => 
                    [documenti_richiesti] => doc id
                    [responsabile] => Roberto
                    [nome_progetto] => Casa
                    [cliente_nome] => Mauro Mazzarelli
                )

        )

)

[14-Dec-2024 23:59:49 Europe/Rome] Route dispatched
[14-Dec-2024 23:59:49 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 23:59:49 Europe/Rome] Debug vista - Contenuto di $pratiche: Array
(
    [0] => Array
        (
            [id] => 1
            [progetto_id] => 1
            [tipo_pratica] => 
            [stato] => in_attesa
            [data_apertura] => 2024-12-08 00:00:00
            [data_scadenza] => 2025-02-28
            [note] => 
            [numero_pratica] => 1/2025
            [tipo_documento] => cila
            [ente_riferimento] => 
            [protocollo] => 34566
            [data_protocollo] => 2025-01-17
            [data_scadenza_integrazione] => 
            [importo_diritti] => 
            [note_interne] => 
            [documenti_richiesti] => doc id
            [responsabile] => Roberto
            [nome_progetto] => Casa
            [cliente_nome] => Mauro Mazzarelli
        )

)

[14-Dec-2024 23:59:49 Europe/Rome] Debug vista - Numero di pratiche: 1
[14-Dec-2024 23:59:49 Europe/Rome] Debug vista - Elaborazione pratica ID: 1
[14-Dec-2024 23:59:50 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:59:50 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:59:50 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:59:50 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:59:50 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:59:50 Europe/Rome] Router inizializzato
[14-Dec-2024 23:59:50 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:59:50 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:59:50 Europe/Rome] Route home definita
[14-Dec-2024 23:59:50 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:59:50 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:59:50 Europe/Rome] Route progetti definite
[14-Dec-2024 23:59:50 Europe/Rome] Route clienti definite
[14-Dec-2024 23:59:50 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:59:50 Europe/Rome] URL richiesto: 
[14-Dec-2024 23:59:50 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 23:59:50 Europe/Rome] Bootstrap e Autoloader caricati
[14-Dec-2024 23:59:50 Europe/Rome] Autoloader registrato
[14-Dec-2024 23:59:50 Europe/Rome] Tentativo di connessione al database
[14-Dec-2024 23:59:50 Europe/Rome] Connessione al database stabilita con successo
[14-Dec-2024 23:59:50 Europe/Rome] Connessione al database ottenuta
[14-Dec-2024 23:59:50 Europe/Rome] Router inizializzato
[14-Dec-2024 23:59:50 Europe/Rome] Route di autenticazione definite
[14-Dec-2024 23:59:50 Europe/Rome] Route profilo admin definite
[14-Dec-2024 23:59:50 Europe/Rome] Route home definita
[14-Dec-2024 23:59:50 Europe/Rome] Route dashboard definita
[14-Dec-2024 23:59:50 Europe/Rome] Route pratiche definite
[14-Dec-2024 23:59:50 Europe/Rome] Route progetti definite
[14-Dec-2024 23:59:50 Europe/Rome] Route clienti definite
[14-Dec-2024 23:59:50 Europe/Rome] Route scadenze definite
[14-Dec-2024 23:59:50 Europe/Rome] URL richiesto: dashboard
[14-Dec-2024 23:59:50 Europe/Rome] Utente autenticato: admin
[14-Dec-2024 23:59:50 Europe/Rome] 
==================================================
[14-Dec-2024 23:59:50 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[14-Dec-2024 23:59:50 Europe/Rome] ==================================================

[14-Dec-2024 23:59:50 Europe/Rome] Clienti totali: 2
[14-Dec-2024 23:59:50 Europe/Rome] Pratiche totali: 1
[14-Dec-2024 23:59:50 Europe/Rome] Pratiche attive: 1
[14-Dec-2024 23:59:50 Europe/Rome] Progetti totali: 1
[14-Dec-2024 23:59:50 Europe/Rome] Scadenze pendenti: 0
[14-Dec-2024 23:59:50 Europe/Rome] 
==================================================
[14-Dec-2024 23:59:50 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[14-Dec-2024 23:59:50 Europe/Rome] ==================================================

[14-Dec-2024 23:59:50 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[14-Dec-2024 23:59:50 Europe/Rome] Route dispatched
[15-Dec-2024 00:00:06 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:00:06 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:00:06 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:00:06 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:00:06 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:00:06 Europe/Rome] Router inizializzato
[15-Dec-2024 00:00:06 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:00:06 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:00:06 Europe/Rome] Route home definita
[15-Dec-2024 00:00:06 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:00:06 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:00:06 Europe/Rome] Route progetti definite
[15-Dec-2024 00:00:06 Europe/Rome] Route clienti definite
[15-Dec-2024 00:00:06 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:00:06 Europe/Rome] URL richiesto: logout
[15-Dec-2024 00:00:06 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:00:06 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:00:06 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:00:06 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:00:06 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:00:06 Europe/Rome] Router inizializzato
[15-Dec-2024 00:00:06 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:00:06 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:00:06 Europe/Rome] Route home definita
[15-Dec-2024 00:00:06 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:00:06 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:00:06 Europe/Rome] Route progetti definite
[15-Dec-2024 00:00:06 Europe/Rome] Route clienti definite
[15-Dec-2024 00:00:06 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:00:06 Europe/Rome] URL richiesto: login
[15-Dec-2024 00:00:06 Europe/Rome] Route dispatched
[15-Dec-2024 00:00:10 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:00:10 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:00:10 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:00:10 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:00:10 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:00:10 Europe/Rome] Router inizializzato
[15-Dec-2024 00:00:10 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:00:10 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:00:10 Europe/Rome] Route home definita
[15-Dec-2024 00:00:10 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:00:10 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:00:10 Europe/Rome] Route progetti definite
[15-Dec-2024 00:00:10 Europe/Rome] Route clienti definite
[15-Dec-2024 00:00:10 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:00:10 Europe/Rome] URL richiesto: login
[15-Dec-2024 00:00:10 Europe/Rome] Tentativo di login per username: Mauro
[15-Dec-2024 00:00:10 Europe/Rome] Tentativo di login per username: Mauro
[15-Dec-2024 00:00:10 Europe/Rome] Utente trovato, verifica della password...
[15-Dec-2024 00:00:10 Europe/Rome] Password verificata con successo
[15-Dec-2024 00:00:10 Europe/Rome] Login riuscito per l'utente: Mauro con ruolo: user
[15-Dec-2024 00:00:10 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:00:10 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:00:10 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:00:10 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:00:10 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:00:10 Europe/Rome] Router inizializzato
[15-Dec-2024 00:00:10 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:00:10 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:00:10 Europe/Rome] Route home definita
[15-Dec-2024 00:00:10 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:00:10 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:00:10 Europe/Rome] Route progetti definite
[15-Dec-2024 00:00:10 Europe/Rome] Route clienti definite
[15-Dec-2024 00:00:10 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:00:10 Europe/Rome] URL richiesto: dashboard
[15-Dec-2024 00:00:10 Europe/Rome] Utente autenticato: Mauro
[15-Dec-2024 00:00:10 Europe/Rome] 
==================================================
[15-Dec-2024 00:00:10 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[15-Dec-2024 00:00:10 Europe/Rome] ==================================================

[15-Dec-2024 00:00:10 Europe/Rome] Clienti totali: 2
[15-Dec-2024 00:00:10 Europe/Rome] Pratiche totali: 1
[15-Dec-2024 00:00:10 Europe/Rome] Pratiche attive: 1
[15-Dec-2024 00:00:10 Europe/Rome] Progetti totali: 1
[15-Dec-2024 00:00:10 Europe/Rome] Scadenze pendenti: 0
[15-Dec-2024 00:00:10 Europe/Rome] 
==================================================
[15-Dec-2024 00:00:10 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[15-Dec-2024 00:00:10 Europe/Rome] ==================================================

[15-Dec-2024 00:00:10 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 6
    [username] => Mauro
    [role] => user
    [active] => 1
)

[15-Dec-2024 00:00:10 Europe/Rome] Route dispatched
[15-Dec-2024 00:00:15 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:00:15 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:00:15 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:00:15 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:00:15 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:00:15 Europe/Rome] Router inizializzato
[15-Dec-2024 00:00:15 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:00:15 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:00:15 Europe/Rome] Route home definita
[15-Dec-2024 00:00:15 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:00:15 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:00:15 Europe/Rome] Route progetti definite
[15-Dec-2024 00:00:15 Europe/Rome] Route clienti definite
[15-Dec-2024 00:00:15 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:00:15 Europe/Rome] URL richiesto: logout
[15-Dec-2024 00:00:15 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:00:15 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:00:15 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:00:15 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:00:15 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:00:15 Europe/Rome] Router inizializzato
[15-Dec-2024 00:00:15 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:00:15 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:00:15 Europe/Rome] Route home definita
[15-Dec-2024 00:00:15 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:00:15 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:00:15 Europe/Rome] Route progetti definite
[15-Dec-2024 00:00:15 Europe/Rome] Route clienti definite
[15-Dec-2024 00:00:15 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:00:15 Europe/Rome] URL richiesto: login
[15-Dec-2024 00:00:15 Europe/Rome] Route dispatched
[15-Dec-2024 00:00:20 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:00:20 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:00:20 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:00:20 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:00:20 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:00:20 Europe/Rome] Router inizializzato
[15-Dec-2024 00:00:20 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:00:20 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:00:20 Europe/Rome] Route home definita
[15-Dec-2024 00:00:20 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:00:20 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:00:20 Europe/Rome] Route progetti definite
[15-Dec-2024 00:00:20 Europe/Rome] Route clienti definite
[15-Dec-2024 00:00:20 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:00:20 Europe/Rome] URL richiesto: login
[15-Dec-2024 00:00:20 Europe/Rome] Tentativo di login per username: admin
[15-Dec-2024 00:00:20 Europe/Rome] Tentativo di login per username: admin
[15-Dec-2024 00:00:20 Europe/Rome] Utente trovato, verifica della password...
[15-Dec-2024 00:00:20 Europe/Rome] Password verificata con successo
[15-Dec-2024 00:00:20 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[15-Dec-2024 00:00:20 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:00:20 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:00:20 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:00:20 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:00:20 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:00:20 Europe/Rome] Router inizializzato
[15-Dec-2024 00:00:20 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:00:20 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:00:20 Europe/Rome] Route home definita
[15-Dec-2024 00:00:20 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:00:20 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:00:20 Europe/Rome] Route progetti definite
[15-Dec-2024 00:00:20 Europe/Rome] Route clienti definite
[15-Dec-2024 00:00:20 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:00:20 Europe/Rome] URL richiesto: admin
[15-Dec-2024 00:00:20 Europe/Rome] Accesso admin verificato: admin
[15-Dec-2024 00:00:20 Europe/Rome] Route dispatched
[15-Dec-2024 00:01:12 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:01:12 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:01:12 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:01:12 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:01:12 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:01:12 Europe/Rome] Router inizializzato
[15-Dec-2024 00:01:12 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:01:12 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:01:12 Europe/Rome] Route home definita
[15-Dec-2024 00:01:12 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:01:12 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:01:12 Europe/Rome] Route progetti definite
[15-Dec-2024 00:01:12 Europe/Rome] Route clienti definite
[15-Dec-2024 00:01:12 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:01:12 Europe/Rome] URL richiesto: admin
[15-Dec-2024 00:01:12 Europe/Rome] Accesso admin verificato: admin
[15-Dec-2024 00:01:12 Europe/Rome] Route dispatched
[15-Dec-2024 00:04:15 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:04:15 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:04:15 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:04:15 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:04:15 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:04:15 Europe/Rome] Router inizializzato
[15-Dec-2024 00:04:15 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:04:15 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:04:15 Europe/Rome] Route home definita
[15-Dec-2024 00:04:15 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:04:15 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:04:15 Europe/Rome] Route progetti definite
[15-Dec-2024 00:04:15 Europe/Rome] Route clienti definite
[15-Dec-2024 00:04:15 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:04:15 Europe/Rome] URL richiesto: admin
[15-Dec-2024 00:04:15 Europe/Rome] Accesso admin verificato: admin
[2024-12-15 00:04:15][🟡 WARN ][admin@::1][error_log.php:94] Undefined variable $dbConnection
[15-Dec-2024 00:04:15 Europe/Rome] PHP Warning:  Undefined variable $dbConnection in C:\xampp\htdocs\studio_tecnico\views\admin\index.php on line 208
[2024-12-15 00:04:15][🟡 WARN ][admin@::1][error_log.php:94] Undefined variable $dbConnection
[15-Dec-2024 00:04:15 Europe/Rome] PHP Warning:  Undefined variable $dbConnection in C:\xampp\htdocs\studio_tecnico\views\admin\index.php on line 209
[2024-12-15 00:04:15][🟡 WARN ][admin@::1][error_log.php:94] Undefined variable $dbConnection
[15-Dec-2024 00:04:15 Europe/Rome] PHP Warning:  Undefined variable $dbConnection in C:\xampp\htdocs\studio_tecnico\views\admin\index.php on line 329
[2024-12-15 00:04:15][🟡 WARN ][admin@::1][error_log.php:94] Undefined variable $dbConnection
[15-Dec-2024 00:04:15 Europe/Rome] PHP Warning:  Undefined variable $dbConnection in C:\xampp\htdocs\studio_tecnico\views\admin\index.php on line 330
[15-Dec-2024 00:04:15 Europe/Rome] Route dispatched
[15-Dec-2024 00:04:59 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:04:59 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:04:59 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:04:59 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:04:59 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:04:59 Europe/Rome] Router inizializzato
[15-Dec-2024 00:04:59 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:04:59 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:04:59 Europe/Rome] Route home definita
[15-Dec-2024 00:04:59 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:04:59 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:04:59 Europe/Rome] Route progetti definite
[15-Dec-2024 00:04:59 Europe/Rome] Route clienti definite
[15-Dec-2024 00:04:59 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:04:59 Europe/Rome] URL richiesto: admin
[15-Dec-2024 00:04:59 Europe/Rome] Accesso admin verificato: admin
[2024-12-15 00:04:59][🟡 WARN ][admin@::1][error_log.php:94] Undefined variable $dbConnection
[15-Dec-2024 00:04:59 Europe/Rome] PHP Warning:  Undefined variable $dbConnection in C:\xampp\htdocs\studio_tecnico\views\admin\index.php on line 261
[2024-12-15 00:04:59][🟡 WARN ][admin@::1][error_log.php:94] Undefined variable $dbConnection
[15-Dec-2024 00:04:59 Europe/Rome] PHP Warning:  Undefined variable $dbConnection in C:\xampp\htdocs\studio_tecnico\views\admin\index.php on line 262
[2024-12-15 00:04:59][🟡 WARN ][admin@::1][error_log.php:94] Undefined variable $dbConnection
[15-Dec-2024 00:04:59 Europe/Rome] PHP Warning:  Undefined variable $dbConnection in C:\xampp\htdocs\studio_tecnico\views\admin\index.php on line 330
[2024-12-15 00:04:59][🟡 WARN ][admin@::1][error_log.php:94] Undefined variable $dbConnection
[15-Dec-2024 00:04:59 Europe/Rome] PHP Warning:  Undefined variable $dbConnection in C:\xampp\htdocs\studio_tecnico\views\admin\index.php on line 331
[15-Dec-2024 00:04:59 Europe/Rome] Route dispatched
[15-Dec-2024 00:07:10 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:07:10 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:07:10 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:07:10 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:07:10 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:07:10 Europe/Rome] Router inizializzato
[15-Dec-2024 00:07:10 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:07:10 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:07:10 Europe/Rome] Route home definita
[15-Dec-2024 00:07:10 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:07:10 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:07:10 Europe/Rome] Route progetti definite
[15-Dec-2024 00:07:10 Europe/Rome] Route clienti definite
[15-Dec-2024 00:07:10 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:07:10 Europe/Rome] URL richiesto: admin
[15-Dec-2024 00:07:10 Europe/Rome] Accesso admin verificato: admin
[2024-12-15 00:07:10][🔴 FATAL][admin@::1][error_log.php:105] Eccezione non catturata: Undefined constant "DB_PASSWORD"
Stack trace:
#0 C:\xampp\htdocs\studio_tecnico\app\controllers\AdminController.php(38): include()
#1 [internal function]: App\Controllers\AdminController->index()
#2 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#3 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('admin')
#4 {main}
[15-Dec-2024 00:07:13 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:07:13 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:07:13 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:07:13 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:07:13 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:07:13 Europe/Rome] Router inizializzato
[15-Dec-2024 00:07:13 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:07:13 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:07:13 Europe/Rome] Route home definita
[15-Dec-2024 00:07:13 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:07:13 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:07:13 Europe/Rome] Route progetti definite
[15-Dec-2024 00:07:13 Europe/Rome] Route clienti definite
[15-Dec-2024 00:07:13 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:07:13 Europe/Rome] URL richiesto: admin
[15-Dec-2024 00:07:13 Europe/Rome] Accesso admin verificato: admin
[2024-12-15 00:07:13][🔴 FATAL][admin@::1][error_log.php:105] Eccezione non catturata: Undefined constant "DB_PASSWORD"
Stack trace:
#0 C:\xampp\htdocs\studio_tecnico\app\controllers\AdminController.php(38): include()
#1 [internal function]: App\Controllers\AdminController->index()
#2 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#3 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('admin')
#4 {main}
[15-Dec-2024 00:08:08 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:08:08 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:08:08 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:08:08 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:08:08 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:08:08 Europe/Rome] Router inizializzato
[15-Dec-2024 00:08:08 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:08:08 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:08:08 Europe/Rome] Route home definita
[15-Dec-2024 00:08:08 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:08:08 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:08:08 Europe/Rome] Route progetti definite
[15-Dec-2024 00:08:08 Europe/Rome] Route clienti definite
[15-Dec-2024 00:08:08 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:08:08 Europe/Rome] URL richiesto: 
[15-Dec-2024 00:08:08 Europe/Rome] Utente autenticato: admin
[15-Dec-2024 00:08:08 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:08:08 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:08:08 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:08:08 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:08:08 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:08:08 Europe/Rome] Router inizializzato
[15-Dec-2024 00:08:08 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:08:08 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:08:08 Europe/Rome] Route home definita
[15-Dec-2024 00:08:08 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:08:08 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:08:08 Europe/Rome] Route progetti definite
[15-Dec-2024 00:08:08 Europe/Rome] Route clienti definite
[15-Dec-2024 00:08:08 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:08:08 Europe/Rome] URL richiesto: dashboard
[15-Dec-2024 00:08:08 Europe/Rome] Utente autenticato: admin
[15-Dec-2024 00:08:08 Europe/Rome] 
==================================================
[15-Dec-2024 00:08:08 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[15-Dec-2024 00:08:08 Europe/Rome] ==================================================

[15-Dec-2024 00:08:08 Europe/Rome] Clienti totali: 2
[15-Dec-2024 00:08:08 Europe/Rome] Pratiche totali: 1
[15-Dec-2024 00:08:08 Europe/Rome] Pratiche attive: 1
[15-Dec-2024 00:08:08 Europe/Rome] Progetti totali: 1
[15-Dec-2024 00:08:08 Europe/Rome] Scadenze pendenti: 0
[15-Dec-2024 00:08:08 Europe/Rome] 
==================================================
[15-Dec-2024 00:08:08 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[15-Dec-2024 00:08:08 Europe/Rome] ==================================================

[15-Dec-2024 00:08:08 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[15-Dec-2024 00:08:08 Europe/Rome] Route dispatched
[15-Dec-2024 00:08:14 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:08:14 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:08:14 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:08:14 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:08:14 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:08:14 Europe/Rome] Router inizializzato
[15-Dec-2024 00:08:14 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:08:14 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:08:14 Europe/Rome] Route home definita
[15-Dec-2024 00:08:14 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:08:14 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:08:14 Europe/Rome] Route progetti definite
[15-Dec-2024 00:08:14 Europe/Rome] Route clienti definite
[15-Dec-2024 00:08:14 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:08:14 Europe/Rome] URL richiesto: logout
[15-Dec-2024 00:08:14 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:08:14 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:08:14 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:08:14 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:08:14 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:08:14 Europe/Rome] Router inizializzato
[15-Dec-2024 00:08:14 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:08:14 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:08:14 Europe/Rome] Route home definita
[15-Dec-2024 00:08:14 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:08:14 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:08:14 Europe/Rome] Route progetti definite
[15-Dec-2024 00:08:14 Europe/Rome] Route clienti definite
[15-Dec-2024 00:08:14 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:08:14 Europe/Rome] URL richiesto: login
[15-Dec-2024 00:08:14 Europe/Rome] Route dispatched
[15-Dec-2024 00:08:17 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:08:17 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:08:17 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:08:17 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:08:17 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:08:17 Europe/Rome] Router inizializzato
[15-Dec-2024 00:08:17 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:08:17 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:08:17 Europe/Rome] Route home definita
[15-Dec-2024 00:08:17 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:08:17 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:08:17 Europe/Rome] Route progetti definite
[15-Dec-2024 00:08:17 Europe/Rome] Route clienti definite
[15-Dec-2024 00:08:17 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:08:17 Europe/Rome] URL richiesto: login
[15-Dec-2024 00:08:17 Europe/Rome] Tentativo di login per username: admin
[15-Dec-2024 00:08:17 Europe/Rome] Tentativo di login per username: admin
[15-Dec-2024 00:08:17 Europe/Rome] Utente trovato, verifica della password...
[15-Dec-2024 00:08:18 Europe/Rome] Password verificata con successo
[15-Dec-2024 00:08:18 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[15-Dec-2024 00:08:18 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:08:18 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:08:18 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:08:18 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:08:18 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:08:18 Europe/Rome] Router inizializzato
[15-Dec-2024 00:08:18 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:08:18 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:08:18 Europe/Rome] Route home definita
[15-Dec-2024 00:08:18 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:08:18 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:08:18 Europe/Rome] Route progetti definite
[15-Dec-2024 00:08:18 Europe/Rome] Route clienti definite
[15-Dec-2024 00:08:18 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:08:18 Europe/Rome] URL richiesto: admin
[15-Dec-2024 00:08:18 Europe/Rome] Accesso admin verificato: admin
[2024-12-15 00:08:18][🔴 FATAL][admin@::1][error_log.php:105] Eccezione non catturata: Undefined constant "DB_PASSWORD"
Stack trace:
#0 C:\xampp\htdocs\studio_tecnico\app\controllers\AdminController.php(38): include()
#1 [internal function]: App\Controllers\AdminController->index()
#2 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#3 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('admin')
#4 {main}
[15-Dec-2024 00:08:57 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:08:57 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:08:57 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:08:57 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:08:57 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:08:57 Europe/Rome] Router inizializzato
[15-Dec-2024 00:08:57 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:08:57 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:08:57 Europe/Rome] Route home definita
[15-Dec-2024 00:08:57 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:08:57 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:08:57 Europe/Rome] Route progetti definite
[15-Dec-2024 00:08:57 Europe/Rome] Route clienti definite
[15-Dec-2024 00:08:57 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:08:57 Europe/Rome] URL richiesto: admin
[15-Dec-2024 00:08:57 Europe/Rome] Accesso admin verificato: admin
[15-Dec-2024 00:08:57 Europe/Rome] Route dispatched
[15-Dec-2024 00:10:01 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:10:01 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:10:01 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:10:01 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:10:01 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:10:01 Europe/Rome] Router inizializzato
[15-Dec-2024 00:10:01 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:10:01 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:10:01 Europe/Rome] Route home definita
[15-Dec-2024 00:10:01 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:10:01 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:10:01 Europe/Rome] Route progetti definite
[15-Dec-2024 00:10:01 Europe/Rome] Route clienti definite
[15-Dec-2024 00:10:01 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:10:01 Europe/Rome] URL richiesto: admin
[15-Dec-2024 00:10:01 Europe/Rome] Accesso admin verificato: admin
[2024-12-15 00:10:01][🔴 FATAL][admin@::1][error_log.php:105] Eccezione non catturata: Undefined constant "DB_PASSWORD"
Stack trace:
#0 C:\xampp\htdocs\studio_tecnico\app\controllers\AdminController.php(38): include()
#1 [internal function]: App\Controllers\AdminController->index()
#2 C:\xampp\htdocs\studio_tecnico\app\core\Router.php(77): call_user_func_array(Array, Array)
#3 C:\xampp\htdocs\studio_tecnico\index.php(152): App\Core\Router->dispatch('admin')
#4 {main}
[15-Dec-2024 00:11:14 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:11:14 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:11:14 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:11:14 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:11:14 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:11:14 Europe/Rome] Router inizializzato
[15-Dec-2024 00:11:14 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:11:14 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:11:14 Europe/Rome] Route home definita
[15-Dec-2024 00:11:14 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:11:14 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:11:14 Europe/Rome] Route progetti definite
[15-Dec-2024 00:11:14 Europe/Rome] Route clienti definite
[15-Dec-2024 00:11:14 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:11:14 Europe/Rome] URL richiesto: admin
[15-Dec-2024 00:11:14 Europe/Rome] Accesso admin verificato: admin
[15-Dec-2024 00:11:14 Europe/Rome] Route dispatched
[15-Dec-2024 00:17:20 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:17:20 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:17:20 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:17:20 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:17:20 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:17:20 Europe/Rome] Router inizializzato
[15-Dec-2024 00:17:20 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:17:20 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:17:20 Europe/Rome] Route home definita
[15-Dec-2024 00:17:20 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:17:20 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:17:20 Europe/Rome] Route progetti definite
[15-Dec-2024 00:17:20 Europe/Rome] Route clienti definite
[15-Dec-2024 00:17:20 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:17:20 Europe/Rome] URL richiesto: 
[15-Dec-2024 00:17:20 Europe/Rome] Utente autenticato: admin
[15-Dec-2024 00:17:20 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:17:20 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:17:20 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:17:20 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:17:20 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:17:20 Europe/Rome] Router inizializzato
[15-Dec-2024 00:17:20 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:17:20 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:17:20 Europe/Rome] Route home definita
[15-Dec-2024 00:17:20 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:17:20 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:17:20 Europe/Rome] Route progetti definite
[15-Dec-2024 00:17:20 Europe/Rome] Route clienti definite
[15-Dec-2024 00:17:20 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:17:20 Europe/Rome] URL richiesto: dashboard
[15-Dec-2024 00:17:20 Europe/Rome] Utente autenticato: admin
[15-Dec-2024 00:17:20 Europe/Rome] 
==================================================
[15-Dec-2024 00:17:20 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[15-Dec-2024 00:17:20 Europe/Rome] ==================================================

[15-Dec-2024 00:17:20 Europe/Rome] Clienti totali: 2
[15-Dec-2024 00:17:20 Europe/Rome] Pratiche totali: 1
[15-Dec-2024 00:17:20 Europe/Rome] Pratiche attive: 1
[15-Dec-2024 00:17:20 Europe/Rome] Progetti totali: 1
[15-Dec-2024 00:17:20 Europe/Rome] Scadenze pendenti: 0
[15-Dec-2024 00:17:20 Europe/Rome] 
==================================================
[15-Dec-2024 00:17:20 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[15-Dec-2024 00:17:20 Europe/Rome] ==================================================

[15-Dec-2024 00:17:20 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[15-Dec-2024 00:17:20 Europe/Rome] Route dispatched
[15-Dec-2024 00:17:29 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:17:29 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:17:29 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:17:29 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:17:29 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:17:29 Europe/Rome] Router inizializzato
[15-Dec-2024 00:17:29 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:17:29 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:17:29 Europe/Rome] Route home definita
[15-Dec-2024 00:17:29 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:17:29 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:17:29 Europe/Rome] Route progetti definite
[15-Dec-2024 00:17:29 Europe/Rome] Route clienti definite
[15-Dec-2024 00:17:29 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:17:29 Europe/Rome] URL richiesto: clienti
[15-Dec-2024 00:17:29 Europe/Rome] Utente autenticato: admin
[15-Dec-2024 00:17:29 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[15-Dec-2024 00:17:29 Europe/Rome] Route dispatched
[15-Dec-2024 00:17:34 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:17:34 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:17:34 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:17:34 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:17:34 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:17:34 Europe/Rome] Router inizializzato
[15-Dec-2024 00:17:34 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:17:34 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:17:34 Europe/Rome] Route home definita
[15-Dec-2024 00:17:34 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:17:34 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:17:34 Europe/Rome] Route progetti definite
[15-Dec-2024 00:17:34 Europe/Rome] Route clienti definite
[15-Dec-2024 00:17:34 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:17:34 Europe/Rome] URL richiesto: clienti/nuovo
[15-Dec-2024 00:17:34 Europe/Rome] Utente autenticato: admin
[15-Dec-2024 00:17:34 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[15-Dec-2024 00:17:34 Europe/Rome] Route dispatched
[15-Dec-2024 00:17:39 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:17:39 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:17:39 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:17:39 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:17:39 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:17:39 Europe/Rome] Router inizializzato
[15-Dec-2024 00:17:39 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:17:39 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:17:39 Europe/Rome] Route home definita
[15-Dec-2024 00:17:39 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:17:39 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:17:39 Europe/Rome] Route progetti definite
[15-Dec-2024 00:17:39 Europe/Rome] Route clienti definite
[15-Dec-2024 00:17:39 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:17:39 Europe/Rome] URL richiesto: clienti
[15-Dec-2024 00:17:39 Europe/Rome] Utente autenticato: admin
[15-Dec-2024 00:17:40 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[15-Dec-2024 00:17:40 Europe/Rome] Route dispatched
[15-Dec-2024 00:18:03 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:18:03 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:18:03 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:18:03 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:18:03 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:18:03 Europe/Rome] Router inizializzato
[15-Dec-2024 00:18:03 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:18:03 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:18:03 Europe/Rome] Route home definita
[15-Dec-2024 00:18:03 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:18:03 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:18:03 Europe/Rome] Route progetti definite
[15-Dec-2024 00:18:03 Europe/Rome] Route clienti definite
[15-Dec-2024 00:18:03 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:18:03 Europe/Rome] URL richiesto: 
[15-Dec-2024 00:18:03 Europe/Rome] Utente autenticato: admin
[15-Dec-2024 00:18:03 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:18:03 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:18:03 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:18:03 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:18:03 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:18:03 Europe/Rome] Router inizializzato
[15-Dec-2024 00:18:03 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:18:03 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:18:03 Europe/Rome] Route home definita
[15-Dec-2024 00:18:03 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:18:03 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:18:03 Europe/Rome] Route progetti definite
[15-Dec-2024 00:18:03 Europe/Rome] Route clienti definite
[15-Dec-2024 00:18:03 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:18:03 Europe/Rome] URL richiesto: dashboard
[15-Dec-2024 00:18:03 Europe/Rome] Utente autenticato: admin
[15-Dec-2024 00:18:03 Europe/Rome] 
==================================================
[15-Dec-2024 00:18:03 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[15-Dec-2024 00:18:03 Europe/Rome] ==================================================

[15-Dec-2024 00:18:03 Europe/Rome] Clienti totali: 2
[15-Dec-2024 00:18:03 Europe/Rome] Pratiche totali: 1
[15-Dec-2024 00:18:03 Europe/Rome] Pratiche attive: 1
[15-Dec-2024 00:18:03 Europe/Rome] Progetti totali: 1
[15-Dec-2024 00:18:03 Europe/Rome] Scadenze pendenti: 0
[15-Dec-2024 00:18:03 Europe/Rome] 
==================================================
[15-Dec-2024 00:18:03 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[15-Dec-2024 00:18:03 Europe/Rome] ==================================================

[15-Dec-2024 00:18:03 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[15-Dec-2024 00:18:03 Europe/Rome] Route dispatched
[15-Dec-2024 00:18:17 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:18:17 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:18:17 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:18:17 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:18:17 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:18:17 Europe/Rome] Router inizializzato
[15-Dec-2024 00:18:17 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:18:17 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:18:17 Europe/Rome] Route home definita
[15-Dec-2024 00:18:17 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:18:17 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:18:17 Europe/Rome] Route progetti definite
[15-Dec-2024 00:18:17 Europe/Rome] Route clienti definite
[15-Dec-2024 00:18:17 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:18:17 Europe/Rome] URL richiesto: 
[15-Dec-2024 00:18:17 Europe/Rome] Utente autenticato: admin
[15-Dec-2024 00:18:17 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:18:17 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:18:17 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:18:17 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:18:17 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:18:17 Europe/Rome] Router inizializzato
[15-Dec-2024 00:18:17 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:18:17 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:18:17 Europe/Rome] Route home definita
[15-Dec-2024 00:18:17 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:18:17 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:18:17 Europe/Rome] Route progetti definite
[15-Dec-2024 00:18:17 Europe/Rome] Route clienti definite
[15-Dec-2024 00:18:17 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:18:17 Europe/Rome] URL richiesto: dashboard
[15-Dec-2024 00:18:17 Europe/Rome] Utente autenticato: admin
[15-Dec-2024 00:18:17 Europe/Rome] 
==================================================
[15-Dec-2024 00:18:17 Europe/Rome] === INIZIO SESSIONE DASHBOARD ===
[15-Dec-2024 00:18:17 Europe/Rome] ==================================================

[15-Dec-2024 00:18:17 Europe/Rome] Clienti totali: 2
[15-Dec-2024 00:18:17 Europe/Rome] Pratiche totali: 1
[15-Dec-2024 00:18:17 Europe/Rome] Pratiche attive: 1
[15-Dec-2024 00:18:17 Europe/Rome] Progetti totali: 1
[15-Dec-2024 00:18:17 Europe/Rome] Scadenze pendenti: 0
[15-Dec-2024 00:18:17 Europe/Rome] 
==================================================
[15-Dec-2024 00:18:17 Europe/Rome] === FINE SESSIONE DASHBOARD ===
[15-Dec-2024 00:18:17 Europe/Rome] ==================================================

[15-Dec-2024 00:18:17 Europe/Rome] Dati utente nella sessione: Array
(
    [id] => 5
    [username] => admin
    [role] => admin
    [active] => 1
)

[15-Dec-2024 00:18:17 Europe/Rome] Route dispatched
[15-Dec-2024 00:18:23 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:18:23 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:18:23 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:18:23 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:18:23 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:18:23 Europe/Rome] Router inizializzato
[15-Dec-2024 00:18:23 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:18:23 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:18:23 Europe/Rome] Route home definita
[15-Dec-2024 00:18:23 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:18:23 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:18:23 Europe/Rome] Route progetti definite
[15-Dec-2024 00:18:23 Europe/Rome] Route clienti definite
[15-Dec-2024 00:18:23 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:18:23 Europe/Rome] URL richiesto: logout
[15-Dec-2024 00:18:23 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 00:18:23 Europe/Rome] Autoloader registrato
[15-Dec-2024 00:18:23 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 00:18:23 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 00:18:23 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 00:18:23 Europe/Rome] Router inizializzato
[15-Dec-2024 00:18:23 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 00:18:23 Europe/Rome] Route profilo admin definite
[15-Dec-2024 00:18:23 Europe/Rome] Route home definita
[15-Dec-2024 00:18:23 Europe/Rome] Route dashboard definita
[15-Dec-2024 00:18:23 Europe/Rome] Route pratiche definite
[15-Dec-2024 00:18:23 Europe/Rome] Route progetti definite
[15-Dec-2024 00:18:23 Europe/Rome] Route clienti definite
[15-Dec-2024 00:18:23 Europe/Rome] Route scadenze definite
[15-Dec-2024 00:18:23 Europe/Rome] URL richiesto: login
[15-Dec-2024 00:18:23 Europe/Rome] Route dispatched
[15-Dec-2024 08:59:15 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 08:59:15 Europe/Rome] Autoloader registrato
[15-Dec-2024 08:59:15 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 08:59:15 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 08:59:15 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 08:59:15 Europe/Rome] Router inizializzato
[15-Dec-2024 08:59:15 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 08:59:15 Europe/Rome] Route profilo admin definite
[15-Dec-2024 08:59:15 Europe/Rome] Route home definita
[15-Dec-2024 08:59:15 Europe/Rome] Route dashboard definita
[15-Dec-2024 08:59:15 Europe/Rome] Route pratiche definite
[15-Dec-2024 08:59:15 Europe/Rome] Route progetti definite
[15-Dec-2024 08:59:15 Europe/Rome] Route clienti definite
[15-Dec-2024 08:59:15 Europe/Rome] Route scadenze definite
[15-Dec-2024 08:59:15 Europe/Rome] URL richiesto: login
[15-Dec-2024 08:59:15 Europe/Rome] Route dispatched
[15-Dec-2024 08:59:40 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 08:59:40 Europe/Rome] Autoloader registrato
[15-Dec-2024 08:59:40 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 08:59:40 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 08:59:40 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 08:59:40 Europe/Rome] Router inizializzato
[15-Dec-2024 08:59:40 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 08:59:40 Europe/Rome] Route profilo admin definite
[15-Dec-2024 08:59:40 Europe/Rome] Route home definita
[15-Dec-2024 08:59:40 Europe/Rome] Route dashboard definita
[15-Dec-2024 08:59:40 Europe/Rome] Route pratiche definite
[15-Dec-2024 08:59:40 Europe/Rome] Route progetti definite
[15-Dec-2024 08:59:40 Europe/Rome] Route clienti definite
[15-Dec-2024 08:59:40 Europe/Rome] Route scadenze definite
[15-Dec-2024 08:59:40 Europe/Rome] URL richiesto: login
[15-Dec-2024 08:59:40 Europe/Rome] Tentativo di login per username: admin
[15-Dec-2024 08:59:40 Europe/Rome] Tentativo di login per username: admin
[15-Dec-2024 08:59:40 Europe/Rome] Utente trovato, verifica della password...
[15-Dec-2024 08:59:40 Europe/Rome] Password verificata con successo
[15-Dec-2024 08:59:40 Europe/Rome] Login riuscito per l'utente: admin con ruolo: admin
[15-Dec-2024 08:59:40 Europe/Rome] Bootstrap e Autoloader caricati
[15-Dec-2024 08:59:40 Europe/Rome] Autoloader registrato
[15-Dec-2024 08:59:40 Europe/Rome] Tentativo di connessione al database
[15-Dec-2024 08:59:40 Europe/Rome] Connessione al database stabilita con successo
[15-Dec-2024 08:59:40 Europe/Rome] Connessione al database ottenuta
[15-Dec-2024 08:59:40 Europe/Rome] Router inizializzato
[15-Dec-2024 08:59:40 Europe/Rome] Route di autenticazione definite
[15-Dec-2024 08:59:40 Europe/Rome] Route profilo admin definite
[15-Dec-2024 08:59:40 Europe/Rome] Route home definita
[15-Dec-2024 08:59:40 Europe/Rome] Route dashboard definita
[15-Dec-2024 08:59:40 Europe/Rome] Route pratiche definite
[15-Dec-2024 08:59:40 Europe/Rome] Route progetti definite
[15-Dec-2024 08:59:40 Europe/Rome] Route clienti definite
[15-Dec-2024 08:59:40 Europe/Rome] Route scadenze definite
[15-Dec-2024 08:59:40 Europe/Rome] URL richiesto: admin
[15-Dec-2024 08:59:40 Europe/Rome] Accesso admin verificato: admin
[15-Dec-2024 08:59:40 Europe/Rome] Route dispatched
