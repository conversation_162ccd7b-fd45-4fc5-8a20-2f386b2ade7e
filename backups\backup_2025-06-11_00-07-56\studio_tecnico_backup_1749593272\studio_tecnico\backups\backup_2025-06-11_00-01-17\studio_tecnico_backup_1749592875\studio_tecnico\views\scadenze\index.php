<?php
$pageTitle = 'Scadenze';
ob_start();
?>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2>
                <i class="fas fa-calendar-alt me-2"></i>
                Gestione Scadenze
            </h2>
        </div>
        <div class="col text-end">
            <a href="<?php echo BASE_URL; ?>scadenze/nuova" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nuova Scadenza
            </a>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>Titolo</th>
                    <th>Data Scadenza</th>
                    <th>Progetto</th>
                    <th>Priorità</th>
                    <th>Stato</th>
                    <th>Azioni</th>
                </tr>
            </thead>
            <tbody>
                <?php if (isset($scadenze) && !empty($scadenze)): ?>
                    <?php foreach ($scadenze as $scadenza): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($scadenza['titolo']); ?></td>
                            <td><?php echo date('d/m/Y', strtotime($scadenza['data_scadenza'])); ?></td>
                            <td><?php echo htmlspecialchars($scadenza['progetto']); ?></td>
                            <td>
                                <span class="badge bg-<?php echo $scadenza['priorita'] === 'alta' ? 'danger' : ($scadenza['priorita'] === 'media' ? 'warning' : 'info'); ?>">
                                    <?php echo ucfirst($scadenza['priorita']); ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $scadenza['completata'] ? 'success' : 'secondary'; ?>">
                                    <?php echo $scadenza['completata'] ? 'Completata' : 'In corso'; ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="<?php echo BASE_URL; ?>scadenze/modifica/<?php echo $scadenza['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger"
                                            onclick="confirmDelete(<?php echo $scadenza['id']; ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="6" class="text-center">Nessuna scadenza trovata</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<script>
function confirmDelete(id) {
    Swal.fire({
        title: 'Sei sicuro?',
        text: "Questa azione non può essere annullata!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Sì, elimina!',
        cancelButtonText: 'Annulla'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `${BASE_URL}scadenze/elimina/${id}`;
        }
    });
}
</script>

<?php
$content = ob_get_clean();
require_once VIEWS_DIR . '/layouts/base.php';
?>
