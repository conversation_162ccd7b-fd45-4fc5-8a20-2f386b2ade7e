<?php
header('Content-Type: application/json');
require_once '../config/database.php';

// Funzione per inviare risposte JSON
function sendResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit;
}

// Funzione per gestire gli errori
function sendError($message, $status = 400) {
    sendResponse([
        'success' => false,
        'message' => $message
    ], $status);
}

// Verifica che la richiesta sia POST quando necessario
function requirePost() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendError('Metodo non consentito', 405);
    }
} 