<?php
/**
 * Navbar specifica per l'area amministrativa
 */
?>
<nav class="navbar navbar-expand-lg bg-dark navbar-dark border-bottom">
    <div class="container">
        <a class="navbar-brand d-flex align-items-center gap-2" href="<?= htmlspecialchars(BASE_URL . 'admin') ?>">
            <?php
            $config = require_once ROOT_PATH . '/config/app.php';
            if (!empty($config['logo_path']) && file_exists(ROOT_PATH . '/public/' . $config['logo_path'])) {
                echo '<img src="' . htmlspecialchars(BASE_URL . $config['logo_path']) . '" alt="Logo Admin" height="40">';
            } else {
                require_once VIEWS_DIR . '/components/default-logo.php';
                echo getDefaultLogo();
            }
            ?>
            <span class="fw-bold">Admin Panel</span>
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#adminNavbar" 
                aria-controls="adminNavbar" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="adminNavbar">
            <ul class="navbar-nav ms-4">
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center gap-2 <?= isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], 'admin/users') !== false ? 'active' : '' ?>" 
                       href="<?= htmlspecialchars(BASE_URL . 'admin/users') ?>">
                       <i class="fas fa-users-cog"></i>
                       Gestione Utenti
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center gap-2 <?= isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], 'admin/config') !== false ? 'active' : '' ?>" 
                       href="<?= htmlspecialchars(BASE_URL . 'admin/config') ?>">
                       <i class="fas fa-cogs"></i>
                       Configurazione
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center gap-2" 
                       href="#" onclick="openBackupWindow(); return false;">
                       <i class="fas fa-database"></i>
                       Backup Completo
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center gap-2" 
                       href="<?= htmlspecialchars(BASE_URL) ?>" title="Vai alla Dashboard Utente">
                       <i class="fas fa-home"></i>
                       Dashboard Utente
                    </a>
                </li>
            </ul>
            
            <ul class="navbar-nav ms-auto">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center gap-2" href="#" 
                       role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-shield"></i>
                        <?= htmlspecialchars($_SESSION['user']['username'] ?? '') ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item d-flex align-items-center gap-2" href="<?= htmlspecialchars(BASE_URL . 'logout') ?>">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<script>
function openBackupWindow() {
    // Calcola le dimensioni del popup
    const width = 1000;
    const height = 800;
    const left = (window.screen.width - width) / 2;
    const top = (window.screen.height - height) / 2;

    // Apri il popup centrato
    window.open(
        '<?= BASE_URL ?>BackupCompleto.php',
        'Backup Completo',
        `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes,status=yes`
    );
}
</script>
