<?php
// app/controllers/NotificheController.php - Controller per la gestione delle notifiche
namespace App\Controllers;

use App\Core\Controller;
use App\Models\NotificheModel;
use App\Core\Security;

class NotificheController extends Controller {
    private NotificheModel $notificheModel;

    public function __construct($db = null) {
        try {
            $this->notificheModel = new NotificheModel();
        } catch (\Exception $e) {
            error_log("Errore nell'inizializzazione del NotificheController: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Dashboard notifiche - visualizza tutte le notifiche dell'utente
     */
    public function index() {
        try {
            $user_id = $_SESSION['user']['id'] ?? 1; // Fallback per compatibilità

            // Recupera filtri dalla query string
            $filters = [];
            if (!empty($_GET['letta'])) {
                $filters['letta'] = $_GET['letta'] === 'true';
            }
            if (!empty($_GET['tipo'])) {
                $filters['tipo'] = $_GET['tipo'];
            }
            if (!empty($_GET['priorita'])) {
                $filters['priorita'] = $_GET['priorita'];
            }
            $filters['limit'] = $_GET['limit'] ?? 50;

            // Recupera notifiche e statistiche
            $notifiche = $this->notificheModel->getAllNotificheUtente($user_id, $filters);
            $statistiche = $this->notificheModel->getStatisticheNotifiche($user_id);
            $preferenze = $this->notificheModel->getPreferenzeUtente($user_id);

            $this->view('notifiche/index', [
                'notifiche' => $notifiche,
                'statistiche' => $statistiche,
                'preferenze' => $preferenze,
                'filters' => $filters
            ]);

        } catch (\Exception $e) {
            error_log("Errore in NotificheController::index(): " . $e->getMessage());
            $this->view('notifiche/index', [
                'notifiche' => [],
                'statistiche' => ['conteggi' => ['non_lette' => 0, 'lette' => 0, 'totali' => 0]],
                'preferenze' => [],
                'filters' => [],
                'error' => 'Errore nel caricamento delle notifiche'
            ]);
        }
    }

    /**
     * API AJAX per recuperare notifiche in tempo reale
     */
    public function getNotificheAjax() {
        try {
            $user_id = $_SESSION['user']['id'] ?? 1;
            $limit = (int)($_GET['limit'] ?? 10);
            $solo_non_lette = isset($_GET['solo_non_lette']) && $_GET['solo_non_lette'] === 'true';

            if ($solo_non_lette) {
                $notifiche = $this->notificheModel->getNotificheNonLette($user_id, $limit);
            } else {
                $notifiche = $this->notificheModel->getAllNotificheUtente($user_id, ['limit' => $limit]);
            }

            // Conta notifiche non lette per badge
            $count_non_lette = $this->notificheModel->countNotificheNonLette($user_id);

            $this->jsonResponse([
                'success' => true,
                'notifiche' => array_map(function($n) {
                    return [
                        'id' => $n['id'],
                        'tipo' => $n['tipo'],
                        'titolo' => $n['titolo'],
                        'messaggio' => $n['messaggio'],
                        'data' => date('d/m/Y H:i', strtotime($n['data_creazione'])),
                        'priorita' => $n['priorita'],
                        'link_azione' => $n['link_azione'],
                        'letta' => (bool)$n['letta'],
                        'metadata' => $n['metadata'] ? json_decode($n['metadata'], true) : null
                    ];
                }, $notifiche),
                'count_non_lette' => $count_non_lette
            ]);

        } catch (\Exception $e) {
            error_log("Errore in NotificheController::getNotificheAjax(): " . $e->getMessage());
            $this->jsonResponse(['error' => 'Errore nel recupero delle notifiche'], 500);
        }
    }

    /**
     * API AJAX per recuperare solo le ultime notifiche (compatibilità)
     * @deprecated Usa getNotificheAjax() invece
     */
    public function getUltime() {
        $_GET['limit'] = 5;
        $_GET['solo_non_lette'] = 'true';
        $this->getNotificheAjax();
    }

    /**
     * Marca una notifica come letta
     */
    public function markAsRead($params) {
        try {
            // Verifica CSRF token
            $this->requireCSRF();

            $id = is_array($params) ? (int)($params['id'] ?? $params[0] ?? 0) : (int)$params;
            $user_id = $_SESSION['user']['id'] ?? 1;

            if (!$id) {
                throw new \Exception('ID notifica non valido');
            }

            if ($this->notificheModel->markAsRead($id, $user_id)) {
                $this->jsonResponse(['success' => true, 'message' => 'Notifica marcata come letta']);
            } else {
                throw new \Exception('Errore nell\'aggiornamento della notifica');
            }

        } catch (\Exception $e) {
            error_log("Errore in NotificheController::markAsRead(): " . $e->getMessage());
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Marca tutte le notifiche come lette
     */
    public function markAllAsRead() {
        try {
            // Verifica CSRF token
            $this->requireCSRF();

            $user_id = $_SESSION['user']['id'] ?? 1;

            if ($this->notificheModel->markAllAsRead($user_id)) {
                $count = $this->notificheModel->countNotificheNonLette($user_id);
                $this->jsonResponse([
                    'success' => true,
                    'message' => 'Tutte le notifiche sono state marcate come lette',
                    'count_non_lette' => $count
                ]);
            } else {
                throw new \Exception('Errore nell\'aggiornamento delle notifiche');
            }

        } catch (\Exception $e) {
            error_log("Errore in NotificheController::markAllAsRead(): " . $e->getMessage());
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Elimina una notifica specifica
     */
    public function deleteNotifica($params) {
        try {
            // Verifica CSRF token
            $this->requireCSRF();

            $id = is_array($params) ? (int)($params['id'] ?? $params[0] ?? 0) : (int)$params;
            $user_id = $_SESSION['user']['id'] ?? 1;

            if (!$id) {
                throw new \Exception('ID notifica non valido');
            }

            if ($this->notificheModel->deleteNotifica($id, $user_id)) {
                $this->jsonResponse(['success' => true, 'message' => 'Notifica eliminata']);
            } else {
                throw new \Exception('Errore nell\'eliminazione della notifica o notifica non trovata');
            }

        } catch (\Exception $e) {
            error_log("Errore in NotificheController::deleteNotifica(): " . $e->getMessage());
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Metodo di compatibilità
     * @deprecated Usa markAsRead() invece
     */
    public function segnaLetta($id) {
        $this->markAsRead($id);
    }

    /**
     * Gestione preferenze utente per le notifiche
     */
    public function preferenze() {
        $user_id = $_SESSION['user']['id'] ?? 1;

        if ($this->isPost()) {
            try {
                // Verifica CSRF token
                $this->requireCSRF();

                $preferenze_raw = $this->getPost('preferenze');
                if (!is_array($preferenze_raw)) {
                    throw new \Exception('Dati preferenze non validi');
                }

                // Sanifica e valida le preferenze
                $preferenze = [];
                foreach ($preferenze_raw as $tipo => $pref) {
                    $preferenze[Security::sanitizeInput($tipo)] = [
                        'email' => isset($pref['email']) && $pref['email'] === '1',
                        'push' => isset($pref['push']) && $pref['push'] === '1',
                        'soglia_giorni' => max(1, min(30, (int)($pref['soglia_giorni'] ?? 7)))
                    ];
                }

                if ($this->notificheModel->updatePreferenze($user_id, $preferenze)) {
                    if ($this->isAjax()) {
                        $this->jsonResponse(['success' => true, 'message' => 'Preferenze salvate con successo']);
                    } else {
                        $this->redirect('notifiche/preferenze?success=1');
                    }
                } else {
                    throw new \Exception('Errore nel salvataggio delle preferenze');
                }

            } catch (\Exception $e) {
                error_log("Errore in NotificheController::preferenze(): " . $e->getMessage());
                if ($this->isAjax()) {
                    $this->jsonResponse(['error' => $e->getMessage()], 400);
                } else {
                    $this->view('notifiche/preferenze', [
                        'preferenze' => $this->notificheModel->getPreferenzeUtente($user_id),
                        'error' => $e->getMessage()
                    ]);
                }
            }
        } else {
            // GET - Mostra form preferenze
            $this->view('notifiche/preferenze', [
                'preferenze' => $this->notificheModel->getPreferenzeUtente($user_id),
                'success' => isset($_GET['success'])
            ]);
        }
    }

    /**
     * Metodo di compatibilità per salvare preferenze
     * @deprecated Usa preferenze() invece
     */
    public function salvaPreferenze() {
        $this->preferenze();
    }

    /**
     * Verifica scadenze e crea notifiche automatiche
     */
    public function verificaScadenze() {
        try {
            $user_id = $_SESSION['user']['id'] ?? 1;
            $giorni_anticipo = (int)($_GET['giorni'] ?? 7);

            $notifiche_create = 0;

            // Verifica scadenze generali
            $scadenze = $this->notificheModel->getScadenzeImminenti($giorni_anticipo);
            foreach ($scadenze as $scadenza) {
                // Evita duplicati controllando se esiste già una notifica recente
                if (!$this->notificheModel->esisteNotifica('scadenza', $scadenza['scadenza_id'], $user_id)) {
                    if ($this->notificheModel->createNotificaScadenza($scadenza['scadenza_id'], $user_id)) {
                        $notifiche_create++;
                    }
                }
            }

            // Verifica pratiche in scadenza
            $pratiche = $this->notificheModel->getPraticheInScadenza($giorni_anticipo);
            foreach ($pratiche as $pratica) {
                // Evita duplicati controllando se esiste già una notifica recente
                if (!$this->notificheModel->esisteNotifica('pratica', $pratica['pratica_id'], $user_id)) {
                    if ($this->notificheModel->createNotificaPratica($pratica['pratica_id'], 'scadenza_vicina', $user_id)) {
                        $notifiche_create++;
                    }
                }
            }

            $this->jsonResponse([
                'success' => true,
                'message' => 'Verifica scadenze completata',
                'scadenze_trovate' => count($scadenze),
                'pratiche_trovate' => count($pratiche),
                'notifiche_create' => $notifiche_create
            ]);

        } catch (\Exception $e) {
            error_log("Errore in NotificheController::verificaScadenze(): " . $e->getMessage());
            $this->jsonResponse(['error' => 'Errore nella verifica delle scadenze'], 500);
        }
    }

    /**
     * Pulizia automatica notifiche vecchie
     */
    public function pulisciVecchie() {
        try {
            $giorni = (int)($_GET['giorni'] ?? 30);
            $deleted = $this->notificheModel->deleteOldNotifiche($giorni);

            $this->jsonResponse([
                'success' => true,
                'message' => "Pulizia completata: {$deleted} notifiche eliminate",
                'deleted_count' => $deleted
            ]);

        } catch (\Exception $e) {
            error_log("Errore in NotificheController::pulisciVecchie(): " . $e->getMessage());
            $this->jsonResponse(['error' => 'Errore nella pulizia delle notifiche'], 500);
        }
    }

    /**
     * Metodi di utilità
     */

    /**
     * Risposta JSON per API
     */
    private function jsonResponse(array $data, int $statusCode = 200): void {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Metodi di compatibilità per il codice esistente
     */

    /**
     * @deprecated Usa deleteNotifica() invece
     */
    public function archivia($id) {
        $this->deleteNotifica($id);
    }
}
