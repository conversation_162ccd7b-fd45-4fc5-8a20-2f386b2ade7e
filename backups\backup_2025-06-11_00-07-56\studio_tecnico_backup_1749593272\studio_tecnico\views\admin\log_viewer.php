<?php
$pageTitle = "Visualizzatore Log";
include VIEWS_DIR . '/layouts/header.php';
?>

<div class="container-fluid py-4">
    <div class="page-header">
        <h2 class="page-title">
            <i class="fas fa-file-alt"></i>
            Visualizzatore Log
        </h2>
        <div class="page-actions">
            <button onclick="refreshLogs()" class="btn btn-neutral">
                <i class="fas fa-sync"></i>
                Aggiorna
            </button>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="log-container" style="max-height: 600px; overflow-y: auto;">
                <pre id="logContent" class="bg-light p-3" style="font-size: 0.9em;"><?php
                    $logFile = ROOT_PATH . '/logs/error.log';
                    if (file_exists($logFile)) {
                        $logs = file_get_contents($logFile);
                        echo htmlspecialchars($logs);
                    } else {
                        echo "File di log non trovato.";
                    }
                ?></pre>
            </div>
        </div>
    </div>
</div>

<script>
function refreshLogs() {
    fetch('<?= BASE_URL ?>admin/refresh-logs')
        .then(response => response.text())
        .then(data => {
            document.getElementById('logContent').innerHTML = data;
        })
        .catch(error => {
            console.error('Errore nel refresh dei log:', error);
        });
}

// Aggiorna i log ogni 30 secondi
setInterval(refreshLogs, 30000);
</script>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>
