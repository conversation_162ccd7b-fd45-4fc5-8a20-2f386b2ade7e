# Struttura Dettagliata della WebApp Studio Tecnico
Ultimo aggiornamento: 25 Maggio 2025

## 1. Sistema di Gestione (Core)

### 1.1 File di Inizializzazione e Configurazione Base
- **`index.php`** (Root): Entry point principale.
  - Gestisce l'avvio dell'applicazione.
  - Include `bootstrap.php`.
- **`bootstrap.php`** (Root): Setup iniziale.
  - Include `config/config.php` (per costanti globali, sessioni, credenziali DB).
  - Inizializza `app/core/Autoloader.php`.
  - Istanzia e avvia `app/core/Router.php`.
- **`config/config.php`**: File di configurazione principale (BASE_URL, percorsi, credenziali DB, timezone, ecc.).
- **`config/app.php`**: Configurazioni specifiche dell'applicazione (nome app, logo, ecc.).
- **`config/database.php`**: Contien<PERSON> le credenziali e i parametri di connessione al database.
- **`config/routes.php`**: Definisce le rotte dell'applicazione che vengono caricate dal Router.
- **`app/Config/Database.php`**: Gestisce la connessione al database (Singleton PDO), utilizzando le configurazioni da `config/database.php`.

### 1.2 Componenti Core (`/app/core`)
- **`Autoloader.php`**: Gestisce l'autoloading delle classi (PSR-4).
- **`Router.php`**: Analizza l'URL e instrada la richiesta al controller e metodo appropriati, basandosi sulle definizioni in `config/routes.php`.
- **`Controller.php`**: Classe base per tutti i controller. Fornisce metodi helper comuni (es. caricamento viste, redirect, gestione input, CSRF token).
- **`Security.php`**: Fornisce utility per la sicurezza (es. generazione e validazione token CSRF, sanificazione input).

## 2. Controllori (`/app/controllers`)

*Tutti i controller ereditano da `App\Core\Controller`.*

### 2.1 Gestione Utenti e Autenticazione
- **`AuthController.php`**: Gestisce login, logout, registrazione (se presente) e processi di autenticazione.
- **`AdminController.php`**: Gestisce le funzionalità del pannello di amministrazione generale.
- **`AdminProfileController.php`**: Gestisce la modifica del profilo per gli utenti amministratori.

### 2.2 Gestione Entità Principali
- **`ClientiController.php`**: Gestisce le operazioni CRUD (Create, Read, Update, Delete) per i clienti.
- **`ProgettiController.php`**: Gestisce le operazioni CRUD per i progetti.
- **`PraticheController.php`**: Gestisce le operazioni CRUD per le pratiche.

### 2.3 Gestione Funzionalità Specifiche
- **`AllegatiController.php`**: Gestisce l'upload, il download e la gestione dei file allegati.
- **`NotificheController.php`**: Gestisce la creazione e la visualizzazione delle notifiche per gli utenti.
- **`ScadenzeController.php`**: Gestisce la visualizzazione e la gestione delle scadenze.

### 2.4 Gestione Interfaccia e Sistema
- **`DashboardController.php`**: Gestisce la logica per la visualizzazione della dashboard principale.
- **`HomeController.php`**: Gestisce la pagina principale o home page dell'applicazione.
- **`ConfigController.php`**: (Se utilizzato) Potrebbe gestire la visualizzazione o la modifica di alcune configurazioni dell'applicazione tramite interfaccia utente.

## 3. Modelli (`/app/models`)

*I modelli sono responsabili dell'interazione con il database. Non sembra esserci un Model base in `app/core`, quindi ogni modello potrebbe implementare la propria logica di accesso ai dati o utilizzare direttamente l'istanza PDO fornita da `App\Config\Database`.*

- **`User.php`**: Gestisce i dati relativi agli utenti (es. recupero utente per login, gestione profili).
- **`ClientiModel.php`**: Gestisce le operazioni di accesso ai dati per l'entità Clienti (recupero, inserimento, aggiornamento, eliminazione clienti).
- **`Allegato.php`**: Gestisce i dati relativi ai file allegati (metadati, percorsi, ecc.).
- **`Notifica.php`**: Gestisce i dati relativi alle notifiche (salvataggio, recupero, marcatura come lette).

## 4. Viste (`/views`)

### 4.1 Layouts (`/views/layouts`)
*Contengono la struttura HTML principale delle pagine.*
- **`base.php`**: Template HTML di base per tutte le pagine del frontend. Include `<head>` con CSS/JS globali (da CDN e locali), e struttura `<body>` che carica la navbar e il contenuto specifico della pagina. Sostituisce il precedente `main.php`.
- **`admin.php`**: Layout specifico per le sezioni del pannello di amministrazione.
- **`dashboard.php`**: Layout specifico per la dashboard principale.
- **`header.php`**: (Incluso nei layout) Parte superiore della pagina HTML, potrebbe contenere il doctype, head, e l'inizio del body. *Nota: `base.php` sembra gestire la maggior parte del contenuto di `<head>`.*
- **`footer.php`**: (Incluso nei layout) Parte inferiore della pagina HTML, chiusura tag, script JS finali.

### 4.2 Componenti UI Riusabili (`/views/components`)
*Pezzi di interfaccia utente inclusi in diverse viste o layout.*
- **`admin-navbar.php`**: Barra di navigazione specifica per il pannello di amministrazione.
- **`allegati.php`**: Componente per la visualizzazione o gestione degli allegati.
- **`default-logo.php`**: Componente per visualizzare il logo di default.
- **`navbar.php`**: Barra di navigazione principale del sito.
- **`notifiche.php`**: Componente per la visualizzazione delle notifiche.
- **`quick_guide.php`**: Componente per una guida rapida o tutorial.

### 4.3 Viste Specifiche per Modulo (sottocartelle di `/views`)
- **`/views/admin/`**: Viste per il pannello di amministrazione.
- **`/views/auth/`**: Viste per l'autenticazione (login, registrazione, ecc.).
- **`/views/clienti/`**: Viste per la gestione dei clienti (lista, form creazione/modifica, dettagli).
- **`/views/dashboard/`**: Vista per la dashboard principale.
- **`/views/errors/`**: Viste per la visualizzazione di pagine di errore (es. 404, 500).
- **`/views/home/<USER>
- **`/views/login/`**: (Potrebbe essere obsoleta o specifica se diversa da `auth`).
- **`/views/notifiche/`**: Viste per la visualizzazione dettagliata delle notifiche.
- **`/views/pratiche/`**: Viste per la gestione delle pratiche.
- **`/views/progetti/`**: Viste per la gestione dei progetti.
- **`/views/scadenze/`**: Viste per la gestione delle scadenze.

## 5. Assets (Risorse Statiche)

### 5.1 Risorse Locali Personalizzate (nella cartella `assets/` alla root del progetto)
*File CSS e JavaScript specifici dell'applicazione.*
- **`assets/css/`**:
  - `login.css`: Stili per la pagina di login.
  - `management.css`: Stili per le interfacce di gestione.
  - `navbar.css`: Stili per la barra di navigazione.
  - `style.css`: Foglio di stile principale dell'applicazione.
  - `theme.css`, `themes.css`: Stili per la gestione dei temi (light/dark).
- **`assets/js/`**:
  - `main.js`: Script JavaScript principale con funzionalità globali.
  - `clienti.js`: Script specifici per la gestione dei clienti.
  - `pratiche.js`: Script specifici per la gestione delle pratiche.
  - `progetti.js`: Script specifici per la gestione dei progetti.

### 5.2 Risorse Pubbliche (`public/`)
*Accessibili direttamente via URL; usata per file caricati e potenzialmente CSS specifici se non gestiti centralmente da `assets/`.*
- **`public/css/dashboard.css`**: (Potrebbe essere un file specifico per una vecchia versione della dashboard o un modulo isolato. La stilizzazione principale sembra gestita da `assets/css/`).
- **`public/uploads/`**: Cartella designata per i file caricati dagli utenti (es. allegati a pratiche, immagini profilo).

### 5.3 Librerie Esterne (caricate tramite CDN)
*L'applicazione utilizza diverse librerie JavaScript e CSS caricate da Content Delivery Network (CDN), come specificato in `views/layouts/base.php`.*
- **jQuery**: Libreria JavaScript.
- **Bootstrap 5.3**: Framework CSS e JS per l'interfaccia utente.
- **DataTables**: Per la creazione di tabelle dati interattive.
- **Select2**: Per migliorare i box di selezione.
- **Flatpickr**: Per selettori di data e ora.
- **SweetAlert2**: Per notifiche e modali personalizzati.
- **Font Awesome 5.15.4**: Per le icone.

## 6. Configurazione

### 6.1 File di Configurazione Principali (nella cartella `config/` alla root del progetto)
- **`config.php`**: File centrale per le costanti globali (es. `BASE_URL`, `VIEWS_DIR`, `LOGS_DIR`), configurazione delle sessioni, reporting degli errori, e timezone. Potrebbe anche contenere un array di configurazione generale.
- **`app.php`**: Contiene configurazioni specifiche dell'applicazione come il nome dell'applicazione, il percorso del logo, versione, e altre personalizzazioni a livello di applicazione.
- **`database.php`**: Definisce i parametri di connessione al database (host, nome database, utente, password, charset) utilizzati da `app/Config/Database.php`.
- **`routes.php`**: Mappa gli URL ai controller e ai metodi. Contiene le definizioni delle rotte utilizzate da `app/core/Router.php`.

### 6.2 Configurazione Logica Applicativa
- **`app/Config/Database.php`**: Classe Singleton che gestisce la creazione e la restituzione dell'istanza PDO per la connessione al database, leggendo i parametri da `config/database.php`.
- **`app/core/Autoloader.php`**: Configura l'autoloader PSR-4 per caricare automaticamente le classi.
- **Variabili d'ambiente (.env)**: (Non verificato se presente) Se esistesse un file `.env`, conterrebbe configurazioni sensibili o specifiche dell'ambiente (sviluppo, produzione) caricate all'avvio.

### 6.3 Gestione Errori e Logging
- Il reporting degli errori è configurato in `bootstrap.php` o `config/config.php`.
- I log degli errori PHP sono tipicamente salvati in `logs/error.log` (la posizione esatta può essere definita in `config.php`).
- **`logs/error_log.php`**: Script per la configurazione avanzata del logging (se utilizzato, altrimenti il logging è gestito da PHP stesso).

## 7. Database

*La struttura del database è centrale per l'applicazione. La connessione è gestita da `App\Config\Database.php` utilizzando le credenziali da `config/database.php`. I modelli in `/app/models` interagiscono con queste tabelle.*

### 7.1 Tabelle Principali (ipotizzate sulla base dei modelli e funzionalità)
- **`utenti`**: Memorizza le informazioni degli utenti del sistema.
  - `id`, `username`, `password_hash`, `email`, `nome`, `cognome`, `ruolo` (es. admin, utente), `data_creazione`, `ultimo_login`, `attivo` (boolean).
  - Corrisponde al modello `User.php`.
- **`clienti`**: Anagrafica dei clienti dello studio.
  - `id`, `tipo_cliente` (privato, azienda), `nome`, `cognome`, `codice_fiscale`, `ragione_sociale`, `partita_iva`, `email`, `telefono`, `indirizzo`, `cap_cliente`, `citta`, `provincia`, `note`, `data_inserimento`.
  - Corrisponde al modello `ClientiModel.php`.
- **`progetti`**: Informazioni sui progetti gestiti.
  - `id`, `id_cliente` (FK a clienti), `nome_progetto`, `descrizione`, `data_inizio`, `data_fine_prevista`, `data_fine_effettiva`, `stato` (es. in corso, completato, sospeso), `budget`.
  - Interagisce con `ProgettiController.php`.
- **`pratiche`**: Dettagli sulle pratiche edilizie o altri tipi di pratiche.
  - `id`, `id_progetto` (FK a progetti), `codice_pratica`, `tipo_pratica` (es. CILA, SCIA), `descrizione`, `data_presentazione`, `data_approvazione`, `stato_pratica` (es. presentata, approvata, respinta).
  - Interagisce con `PraticheController.php`.
- **`allegati`**: Metadati dei file allegati a clienti, progetti o pratiche.
  - `id`, `id_entita_riferimento` (es. id_pratica, id_progetto), `tipo_entita_riferimento` (es. 'pratica', 'progetto'), `nome_file_originale`, `nome_file_memorizzato`, `percorso_file`, `tipo_mime`, `dimensione_file`, `data_caricamento`, `id_utente_caricamento`.
  - Corrisponde al modello `Allegato.php`.
- **`notifiche`**: Notifiche per gli utenti (es. scadenze, aggiornamenti pratiche).
  - `id`, `id_utente` (FK a utenti), `messaggio`, `tipo_notifica` (es. info, warning, error), `letta` (boolean), `data_creazione`, `link_riferimento`.
  - Corrisponde al modello `Notifica.php`.
- **`scadenze`**: Gestione delle scadenze relative a pratiche o pagamenti.
  - `id`, `id_entita_riferimento` (es. id_pratica, id_progetto), `tipo_entita_riferimento`, `descrizione_scadenza`, `data_scadenza`, `stato` (es. aperta, completata, scaduta), `priorita`.
  - Interagisce con `ScadenzeController.php`.
- **`configurazioni_app`**: (Se presente) Tabella per memorizzare impostazioni dinamiche dell'applicazione.
  - `id`, `chiave_config`, `valore_config`.

### 7.2 Relazioni Chiave
- Un `cliente` può avere molti `progetti`.
- Un `progetto` appartiene a un `cliente` e può avere molte `pratiche`.
- Una `pratica` appartiene a un `progetto`.
- `Allegati` possono essere associati a `clienti`, `progetti`, o `pratiche` (relazione polimorfica o tabelle di join separate).
- `Notifiche` e `Scadenze` sono generalmente associate a `utenti` e/o a entità come `pratiche` o `progetti`.

### 7.3 Considerazioni sul Database
- **Backup**: È fondamentale avere una strategia di backup regolare del database. La presenza di `BACKUP_REPORT.md` suggerisce che potrebbe esserci una funzionalità o procedura in atto.
- **Sicurezza**: Protezione da SQL injection (uso di prepared statements, che `App\Config\Database` dovrebbe facilitare tramite PDO), gestione sicura delle credenziali.
- **Ottimizzazione**: Indicizzazione appropriata delle colonne usate frequentemente nelle clausole WHERE e JOIN per performance ottimali.

## 8. Sicurezza

*La sicurezza è un aspetto cruciale dell'applicazione, gestita attraverso vari meccanismi e buone pratiche.*

### 8.1 Autenticazione e Autorizzazione
- **Gestione Utenti**: Sistema di login (`AuthController.php`) con hashing delle password (si presume l'uso di `password_hash()` e `password_verify()`).
- **Ruoli Utente**: (Ipotizzato) Differenziazione tra utenti standard e amministratori, con permessi diversi per l'accesso a funzionalità e sezioni dell'applicazione.
- **Protezione delle Rotte**: Meccanismi per assicurare che solo utenti autenticati (e autorizzati in base al ruolo) possano accedere a determinate pagine/controller.

### 8.2 Protezione dalle Vulnerabilità Comuni
- **SQL Injection**: Prevenzione tramite l'uso di prepared statements con PDO, facilitato da `App\Config\Database.php`.
- **Cross-Site Scripting (XSS)**: Sanificazione dell'input utente prima di visualizzarlo nelle pagine HTML (es. uso di `htmlspecialchars()` o librerie di templating che effettuano escaping automatico). `App\Core\Security.php` potrebbe fornire utility in tal senso.
- **Cross-Site Request Forgery (CSRF)**: Implementazione di token CSRF per proteggere i form da richieste malevole. `App\Core\Security.php` e `App\Core\Controller.php` probabilmente gestiscono la generazione e validazione di questi token.
- **File Upload Sicuri**: Validazione del tipo e della dimensione dei file caricati (`AllegatiController.php`), memorizzazione dei file in percorsi non direttamente accessibili via web se contengono dati sensibili, o con nomi file randomizzati per prevenire l'accesso diretto se in `public/uploads/`.

### 8.3 Gestione Sessioni
- Utilizzo sicuro delle sessioni PHP, con configurazioni appropriate per `session_start()` (es. `httponly`, `secure` per cookie di sessione se HTTPS è attivo).
- Meccanismi di timeout della sessione e invalidazione alla logout.

### 8.4 Altre Misure
- **HTTPS**: (Raccomandato) Utilizzo di HTTPS per criptare tutte le comunicazioni tra client e server.
- **Logging degli Eventi di Sicurezza**: Registrazione di tentativi di login falliti, accessi non autorizzati, o altre attività sospette.
- **Aggiornamenti Regolari**: Mantenimento aggiornato del server, PHP, e di tutte le librerie esterne per proteggersi da vulnerabilità note.

## 9. Funzionalità Chiave

*L'applicazione offre una suite di funzionalità per la gestione completa delle attività di uno studio tecnico.*

### 9.1 Gestione Anagrafiche
- **Clienti**: Creazione, modifica, visualizzazione, e ricerca di clienti (privati e aziende). Gestione contatti, indirizzi, e note (`ClientiController.php`, `ClientiModel.php`).
- **Utenti**: Gestione degli account utente per l'accesso al sistema (`AuthController.php`, `AdminController.php`, `User.php`).

### 9.2 Gestione Operativa
- **Progetti**: Creazione e gestione di progetti, associazione a clienti, definizione di dettagli, stato avanzamento (`ProgettiController.php`).
- **Pratiche**: Gestione delle pratiche (edilizie, catastali, ecc.) associate ai progetti, tracciamento dello stato, date chiave (`PraticheController.php`).
- **Allegati**: Upload, download e organizzazione di documenti e file relativi a clienti, progetti, e pratiche (`AllegatiController.php`, `Allegato.php`).
- **Scadenze**: Monitoraggio e gestione di scadenze importanti relative a pratiche, pagamenti, o altre attività (`ScadenzeController.php`).

### 9.3 Comunicazione e Notifiche
- **Sistema di Notifiche**: Avvisi automatici o manuali per scadenze imminenti, aggiornamenti di pratiche, nuovi allegati, ecc. (`NotificheController.php`, `Notifica.php`).

### 9.4 Interfaccia Utente e UX
- **Dashboard Personalizzate**: Pannelli di controllo per amministratori e utenti con riepiloghi e accessi rapidi (`DashboardController.php`).
- **Ricerca Avanzata**: Funzionalità di ricerca per trovare rapidamente clienti, progetti, pratiche.
- **Interfaccia Responsive**: Adattabilità dell'interfaccia a diversi dispositivi (desktop, tablet, mobile), grazie all'uso di Bootstrap.
- **Temi Personalizzabili**: Possibilità di cambiare tema (es. light/dark) come indicato in `views/layouts/base.php`.

### 9.5 Amministrazione e Configurazione
- **Gestione Utenti Admin**: Creazione e gestione degli account utente da parte degli amministratori.
- **Configurazioni di Sistema**: (Potenziale) Interfaccia per modificare alcune impostazioni dell'applicazione (`ConfigController.php`).
- **Backup**: (Ipotizzato) Funzionalità o procedure per il backup dei dati dell'applicazione.

## 10. Documentazione (nella cartella `docs/`)

- **`app_map.md`**: Mappa generale del progetto, elenca la struttura delle cartelle principali, i file di documentazione e la funzione generale dei componenti chiave. Serve come indice della documentazione.
- **`aggiornamento.md`**: Documento principale che delinea il piano di ammodernamento della webapp. Include:
    - Aree chiave di aggiornamento (architettura, qualità codice, sicurezza, test, UI/UX, ecc.).
    - Miglioramenti funzionali specifici (es. gestione clienti).
    - Piano d'azione suggerito per fasi.
    - Pianificazione operativa e note di sessione (include resoconti di sessioni passate e pianificazione per le prossime).
- **`webapp_structure.md`**: (Questo file) Descrizione dettagliata della struttura dell'applicazione, dei suoi componenti, file, e della loro interazione.
- **`BACKUP_REPORT.md`**: Report o documentazione relativa alla funzionalità di backup dell'applicazione.
- **`README.md`**: File principale per la presentazione del progetto (es. su GitHub), contiene una descrizione generale, istruzioni di installazione base, e link alla documentazione più dettagliata.
- **Commenti nel codice (PHPDoc)**: Le classi e i metodi dovrebbero essere documentati usando blocchi PHPDoc per descrivere il loro scopo, parametri, e valori di ritorno. Questo facilita la comprensione e la manutenzione del codice.

## 11. Dashboard Amministrativa

*La Dashboard Amministrativa (`AdminController.php`, `/views/admin/`) è il centro di controllo per gli utenti con privilegi elevati. Fornisce una panoramica dell'attività dello studio e accesso a funzionalità di gestione globale.*

### 11.1 Panoramica e Statistiche
- **Statistiche Chiave**: Visualizzazione di dati aggregati come numero totale di clienti, progetti in corso, pratiche aperte, scadenze imminenti.
- **Attività Recenti**: Log delle operazioni recenti più importanti effettuate nel sistema.
- **Grafici e Report**: (Potenziale) Rappresentazioni grafiche dell'andamento dei progetti, carico di lavoro, ecc.

### 11.2 Gestione Utenti
- **Elenco Utenti**: Visualizzazione di tutti gli utenti registrati nel sistema.
- **Creazione/Modifica Utenti**: Form per aggiungere nuovi utenti o modificare i dati e i ruoli degli utenti esistenti.
- **Assegnazione Ruoli/Permessi**: Definizione dei livelli di accesso per ciascun utente.

### 11.3 Gestione Contenuti e Configurazione
- **Gestione Anagrafiche Centralizzata**: Accesso completo a clienti, progetti, pratiche con possibilità di modifica e supervisione.
- **Configurazioni dell'Applicazione**: (Se `ConfigController.php` è attivo per l'UI) Interfaccia per modificare impostazioni globali dell'applicazione (es. nome studio, parametri email, template).
- **Gestione Backup**: (Se presente un'interfaccia) Avvio o pianificazione di backup del database e dei file.

### 11.4 Strumenti e Utility
- **Visualizzatore Log**: Accesso ai log di sistema per il troubleshooting.
- **Manutenzione Sistema**: Eventuali strumenti per la pulizia del database, ottimizzazioni, ecc.

## 12. Dashboard Utente

*La Dashboard Utente (`DashboardController.php`, `/views/dashboard/`) è la pagina principale visualizzata dagli utenti standard dopo il login. Fornisce una vista personalizzata delle informazioni e delle attività rilevanti per l'utente.*

### 12.1 Riepilogo Personale
- **Progetti Assegnati**: Elenco dei progetti a cui l'utente sta lavorando o di cui è responsabile.
- **Pratiche in Carico**: Le pratiche specifiche che richiedono l'attenzione dell'utente.
- **Scadenze Personali/Rilevanti**: Visualizzazione delle prossime scadenze che coinvolgono l'utente.
- **Notifiche Recenti**: Ultime notifiche non lette o importanti.

### 12.2 Accesso Rapido alle Funzionalità
- **Collegamenti Veloci**: Pulsanti o link per accedere rapidamente alle sezioni più utilizzate (es. crea nuovo cliente, avvia nuova pratica).
- **Ricerca Globale**: Barra di ricerca per trovare rapidamente informazioni nel sistema.

### 12.3 Personalizzazione (Potenziale)
- **Widget Configurabili**: Possibilità per l'utente di personalizzare quali informazioni visualizzare sulla dashboard.
- **Preferenze Utente**: Accesso alla modifica del proprio profilo (es. `AdminProfileController.php` gestisce il profilo admin, potrebbe esserci un `UserProfileController.php` o funzionalità simili per utenti standard).

---
*Questo documento mira a fornire una comprensione chiara e aggiornata della struttura e del funzionamento della webapp Studio Tecnico. Sarà aggiornato man mano che il progetto evolve.*
