<?php
// Recupera il conteggio delle notifiche non lette
$notificheNonLette = array_filter($notifiche ?? [], function($n) {
    return !$n['letta'];
});
$conteggio = count($notificheNonLette);
?>

<!-- Dropdown Notifiche -->
<div class="dropdown">
    <button class="btn btn-link position-relative" type="button" id="dropdownNotifiche" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-bell"></i>
        <?php if ($conteggio > 0): ?>
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                <?php echo $conteggio; ?>
            </span>
        <?php endif; ?>
    </button>
    
    <div class="dropdown-menu dropdown-menu-end p-0" aria-labelledby="dropdownNotifiche" style="width: 320px; max-height: 480px;">
        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
            <h6 class="m-0">Notifiche</h6>
            <a href="<?php echo BASE_URL; ?>notifiche" class="text-decoration-none">
                Vedi tutte
            </a>
        </div>
        
        <div class="notifiche-list overflow-auto" style="max-height: 400px;">
            <!-- Le notifiche verranno caricate qui via AJAX -->
            <div class="text-center p-3">
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">Caricamento...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template per le notifiche -->
<template id="templateNotifica">
    <div class="notifica p-3 border-bottom" data-id="">
        <div class="d-flex justify-content-between align-items-start mb-1">
            <h6 class="notifica-titolo m-0"></h6>
            <small class="text-muted notifica-data"></small>
        </div>
        <p class="notifica-messaggio mb-2 small"></p>
        <div class="d-flex justify-content-between align-items-center">
            <a href="#" class="notifica-link small text-decoration-none">
                Visualizza
                <i class="fas fa-chevron-right ms-1"></i>
            </a>
            <button type="button" class="btn btn-sm btn-light" onclick="segnaNotificaLetta(this)">
                <i class="fas fa-check"></i>
            </button>
        </div>
    </div>
</template>

<script>
document.addEventListener('DOMContentLoaded', function() {
    caricaNotifiche();
    
    // Aggiorna le notifiche ogni 5 minuti
    setInterval(caricaNotifiche, 300000);
});

function caricaNotifiche() {
    fetch('<?php echo BASE_URL; ?>notifiche/getUltime')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const container = document.querySelector('.notifiche-list');
                const template = document.getElementById('templateNotifica');
                
                if (data.notifiche.length === 0) {
                    container.innerHTML = '<div class="text-center p-3 text-muted">Nessuna nuova notifica</div>';
                    return;
                }
                
                container.innerHTML = '';
                data.notifiche.forEach(notifica => {
                    const clone = template.content.cloneNode(true);
                    
                    const div = clone.querySelector('.notifica');
                    div.dataset.id = notifica.id;
                    
                    if (notifica.priorita === 'alta') {
                        div.classList.add('bg-danger-subtle');
                    } else if (notifica.priorita === 'media') {
                        div.classList.add('bg-warning-subtle');
                    }
                    
                    clone.querySelector('.notifica-titolo').textContent = notifica.titolo;
                    clone.querySelector('.notifica-data').textContent = notifica.data;
                    clone.querySelector('.notifica-messaggio').textContent = notifica.messaggio;
                    
                    const link = clone.querySelector('.notifica-link');
                    if (notifica.link) {
                        link.href = '<?php echo BASE_URL; ?>' + notifica.link;
                    } else {
                        link.style.display = 'none';
                    }
                    
                    container.appendChild(clone);
                });
                
                // Aggiorna il badge
                const badge = document.querySelector('#dropdownNotifiche .badge');
                if (data.notifiche.length > 0) {
                    if (!badge) {
                        const newBadge = document.createElement('span');
                        newBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                        newBadge.textContent = data.notifiche.length;
                        document.querySelector('#dropdownNotifiche').appendChild(newBadge);
                    } else {
                        badge.textContent = data.notifiche.length;
                    }
                } else if (badge) {
                    badge.remove();
                }
            }
        })
        .catch(error => console.error('Errore nel caricamento delle notifiche:', error));
}

function segnaNotificaLetta(button) {
    const notifica = button.closest('.notifica');
    const id = notifica.dataset.id;
    
    const formData = new FormData();
    formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);
    
    fetch(`<?php echo BASE_URL; ?>notifiche/segnaLetta/${id}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            notifica.remove();
            caricaNotifiche(); // Aggiorna la lista e il badge
        }
    })
    .catch(error => console.error('Errore nell\'aggiornamento della notifica:', error));
}</script>
