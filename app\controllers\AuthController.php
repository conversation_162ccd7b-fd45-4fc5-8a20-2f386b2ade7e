<?php
namespace App\Controllers;

use App\Models\User;

class AuthController {
    private $user;
    
    public function __construct($db) {
        $this->user = new User($db);
    }
    
    public function showLoginForm() {
        // Se l'utente è già loggato, redirect alla dashboard
        if (isset($_SESSION['user'])) {
            header('Location: ' . BASE_URL . 'dashboard');
            exit;
        }
        
        $pageTitle = 'Login';
        require_once VIEWS_DIR . '/auth/login.php';
    }

    public function login() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verifica CSRF token
            try {
                $this->requireCSRF();
            } catch (\Exception $e) {
                error_log("AuthController::login() - " . $e->getMessage());
                $_SESSION['error'] = 'Token di sicurezza non valido. Riprova.';
                $this->redirect('login');
                return;
            }

            // Utilizziamo sanitizeInput invece di htmlspecialchars manuale
            $username = isset($_POST['username']) ? Security::sanitizeInput(trim($_POST['username'])) : '';
            $password = $_POST['password'] ?? '';
            
            error_log("Tentativo di login per username: " . $username);
            
            if (empty($username) || empty($password)) {
                $_SESSION['error'] = 'Inserisci username e password';
                error_log("Login fallito: campi vuoti");
                include VIEWS_DIR . '/auth/login.php';
                return;
            }
            
            $user = $this->user->login($username, $password);
            if ($user) {
                $_SESSION['user'] = $user;
                $_SESSION['success'] = 'Login effettuato con successo';
                error_log("Login riuscito per l'utente: " . $username . " con ruolo: " . $user['role']);
                
                // Reindirizza in base al ruolo
                if ($user['role'] === 'admin') {
                    header('Location: ' . BASE_URL . 'admin');
                } else {
                    header('Location: ' . BASE_URL . 'dashboard');
                }
                exit;
            }
            
            error_log("Login fallito: credenziali non valide per username: " . $username);
            $_SESSION['error'] = 'Credenziali non valide';
            include VIEWS_DIR . '/auth/login.php';
            return;
        }
        
        include VIEWS_DIR . '/auth/login.php';
    }
    
    public function logout() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        session_unset();
        session_destroy();
        header('Location: ' . BASE_URL . 'login');
        exit;
    }
    
    public function isAuthenticated() {
        return isset($_SESSION['user']) && !empty($_SESSION['user']['role']);
    }
    
    public function isAdmin() {
        return $this->isAuthenticated() && $_SESSION['user']['role'] === 'admin';
    }
    
    // Middleware per proteggere le rotte
    public function requireAuth() {
        if (!$this->isAuthenticated()) {
            $_SESSION['error'] = 'Accesso negato. Effettua il login.';
            header('Location: ' . BASE_URL . 'login');
            exit;
        }
    }
    
    public function requireAdmin() {
        if (!$this->isAdmin()) {
            $_SESSION['error'] = 'Accesso negato. Richiesti privilegi di amministratore.';
            header('Location: ' . BASE_URL . 'dashboard');
            exit;
        }
    }
    
    public function profile() {
        if (!isset($_SESSION['user'])) {
            header('Location: ' . BASE_URL . 'login');
            exit;
        }

        $pageTitle = 'Profilo Utente';
        require_once VIEWS_DIR . '/auth/profile.php';
    }
}
