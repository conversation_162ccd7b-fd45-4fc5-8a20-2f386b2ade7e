# Abilita il modulo rewrite
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /progetti/studio_tecnico/
    
    # Permetti l'accesso diretto alle risorse statiche
    RewriteCond %{REQUEST_URI} !\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ [NC]
    
    # Permetti l'accesso diretto ai file esistenti
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    
    # Altrimenti reindirizza all'index.php
    RewriteRule . index.php [L]
</IfModule>

# Proteggi la directory views
<IfModule mod_rewrite.c>
    RewriteRule ^views/ - [F,L]
</IfModule>

# Proteggi i file di configurazione
<FilesMatch "^(config\.php|database\.php)$">
    Order deny,allow
    Deny from all
</FilesMatch>

# Imposta il charset predefinito
AddDefaultCharset UTF-8