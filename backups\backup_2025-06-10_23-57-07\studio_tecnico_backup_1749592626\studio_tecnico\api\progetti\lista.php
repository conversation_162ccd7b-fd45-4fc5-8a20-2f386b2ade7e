<?php
require_once '../config.php';

try {
    $where = '';
    $params = [];

    // Filtra per cliente se specificato
    if (isset($_GET['cliente_id'])) {
        $where = 'WHERE cliente_id = ?';
        $params[] = $_GET['cliente_id'];
    }

    $sql = "SELECT p.*, 
            CASE 
                WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                ELSE c.ragione_sociale 
            END as cliente_nome
            FROM progetti p 
            LEFT JOIN clienti c ON p.cliente_id = c.id 
            $where
            ORDER BY p.data_inizio DESC";

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $progetti = $stmt->fetchAll(PDO::FETCH_ASSOC);

    sendResponse([
        'success' => true,
        'progetti' => $progetti
    ]);
} catch (PDOException $e) {
    sendError('Errore nel recupero dei progetti: ' . $e->getMessage());
} 