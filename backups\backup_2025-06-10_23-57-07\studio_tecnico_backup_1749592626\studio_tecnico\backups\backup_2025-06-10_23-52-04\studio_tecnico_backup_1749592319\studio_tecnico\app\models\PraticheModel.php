<?php
// app/models/PraticheModel.php - Model per la gestione delle pratiche edilizie
namespace App\Models;

use App\Config\Database;
use PDO;
use PDOException;

class PraticheModel {
    private PDO $db;

    // Stati workflow pratiche
    public const STATI_PRATICHE = [
        'in_attesa' => 'In Attesa',
        'in_revisione' => 'In Revisione', 
        'approvata' => 'Approvata',
        'completata' => 'Completata',
        'sospesa' => 'Sospesa',
        'respinta' => 'Respinta'
    ];

    // Transizioni consentite tra stati
    public const TRANSIZIONI_CONSENTITE = [
        'in_attesa' => ['in_revisione', 'sospesa', 'respinta'],
        'in_revisione' => ['approvata', 'in_attesa', 'sospesa', 'respinta'],
        'approvata' => ['completata', 'sospesa'],
        'completata' => [], // Stato finale
        'sospesa' => ['in_attesa', 'in_revisione', 'respinta'],
        'respinta' => ['in_attesa'] // Possibilità di riaprire
    ];

    public function __construct() {
        try {
            $this->db = Database::getInstance();
            if (!$this->db instanceof PDO) {
                throw new PDOException("Connessione al database non valida nel PraticheModel.");
            }
        } catch (PDOException $e) {
            error_log("Errore connessione DB in PraticheModel: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Recupera tutte le pratiche con informazioni progetto e cliente
     */
    public function getAllPratiche(array $filters = []): array {
        try {
            $query = "
                SELECT p.*, 
                       pr.nome_progetto,
                       pr.stato as stato_progetto,
                       c.tipo_cliente,
                       c.nome as cliente_nome,
                       c.cognome as cliente_cognome,
                       c.ragione_sociale as cliente_ragione_sociale,
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome_completo,
                       COALESCE(p.tipo_documento, p.tipo_pratica) as tipo_documento_display
                FROM pratiche p 
                LEFT JOIN progetti pr ON p.progetto_id = pr.id 
                LEFT JOIN clienti c ON pr.cliente_id = c.id
            ";

            $whereConditions = [];
            $params = [];

            // Filtri opzionali
            if (!empty($filters['stato'])) {
                $whereConditions[] = "p.stato = :stato";
                $params[':stato'] = $filters['stato'];
            }

            if (!empty($filters['progetto_id'])) {
                $whereConditions[] = "p.progetto_id = :progetto_id";
                $params[':progetto_id'] = $filters['progetto_id'];
            }

            if (!empty($filters['tipo_documento'])) {
                $whereConditions[] = "p.tipo_documento = :tipo_documento";
                $params[':tipo_documento'] = $filters['tipo_documento'];
            }

            if (!empty($filters['scadenza_da'])) {
                $whereConditions[] = "p.data_scadenza >= :scadenza_da";
                $params[':scadenza_da'] = $filters['scadenza_da'];
            }

            if (!empty($filters['scadenza_a'])) {
                $whereConditions[] = "p.data_scadenza <= :scadenza_a";
                $params[':scadenza_a'] = $filters['scadenza_a'];
            }

            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(" AND ", $whereConditions);
            }

            $query .= " ORDER BY p.data_apertura DESC";

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            
            if ($stmt === false) {
                throw new PDOException("Errore nell'esecuzione della query per getAllPratiche");
            }
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::getAllPratiche(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Recupera una pratica specifica per ID con informazioni complete
     */
    public function getPraticaById(int $id): ?array {
        try {
            $query = "
                SELECT p.*, 
                       pr.nome_progetto,
                       pr.stato as stato_progetto,
                       pr.cliente_id,
                       c.nome as cliente_nome,
                       c.cognome as cliente_cognome,
                       c.ragione_sociale as cliente_ragione_sociale,
                       c.tipo_cliente,
                       c.email as cliente_email,
                       c.telefono as cliente_telefono,
                       CASE 
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale 
                       END as cliente_nome_completo
                FROM pratiche p
                LEFT JOIN progetti pr ON p.progetto_id = pr.id
                LEFT JOIN clienti c ON pr.cliente_id = c.id
                WHERE p.id = :id
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([':id' => $id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ?: null;

        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::getPraticaById(): " . $e->getMessage());
            return null;
        }
    }

    /**
     * Inserisce una nuova pratica
     */
    public function insertPratica(array $data): bool {
        try {
            error_log("PraticheModel::insertPratica() - Dati ricevuti: " . print_r($data, true));
            
            $query = "INSERT INTO pratiche (
                progetto_id, tipo_pratica, tipo_documento, stato, 
                data_apertura, data_scadenza, note, numero_pratica,
                ente_riferimento, protocollo, data_protocollo, 
                data_scadenza_integrazione, importo_diritti, 
                note_interne, documenti_richiesti, responsabile
            ) VALUES (
                :progetto_id, :tipo_pratica, :tipo_documento, :stato,
                :data_apertura, :data_scadenza, :note, :numero_pratica,
                :ente_riferimento, :protocollo, :data_protocollo,
                :data_scadenza_integrazione, :importo_diritti,
                :note_interne, :documenti_richiesti, :responsabile
            )";

            $stmt = $this->db->prepare($query);
            $allowed_keys = [
                'progetto_id', 'tipo_pratica', 'tipo_documento', 'stato',
                'data_apertura', 'data_scadenza', 'note', 'numero_pratica',
                'ente_riferimento', 'protocollo', 'data_protocollo',
                'data_scadenza_integrazione', 'importo_diritti',
                'note_interne', 'documenti_richiesti', 'responsabile'
            ];
            
            $filtered_data = array_intersect_key($data, array_flip($allowed_keys));
            
            // Imposta valori di default
            $filtered_data['stato'] = $filtered_data['stato'] ?? 'in_attesa';
            $filtered_data['data_apertura'] = $filtered_data['data_apertura'] ?? date('Y-m-d H:i:s');
            
            // Gestione valori null per campi numerici
            if (isset($filtered_data['importo_diritti']) && empty($filtered_data['importo_diritti'])) {
                $filtered_data['importo_diritti'] = null;
            }
            
            error_log("PraticheModel::insertPratica() - Dati filtrati per execute: " . print_r($filtered_data, true));
            
            $success = $stmt->execute($filtered_data);
            if (!$success) {
                error_log("PraticheModel::insertPratica() - Errore SQL: " . print_r($stmt->errorInfo(), true));
            }
            
            error_log("PraticheModel::insertPratica() - Esito: " . ($success ? 'Successo' : 'Fallimento'));
            return $success;

        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::insertPratica(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Aggiorna una pratica esistente
     */
    public function updatePratica(array $data): bool {
        try {
            error_log("PraticheModel::updatePratica() - Dati ricevuti: " . print_r($data, true));
            
            if (!isset($data['id'])) {
                error_log("ID pratica mancante per l'aggiornamento in PraticheModel");
                return false;
            }

            $query = "UPDATE pratiche SET 
                progetto_id = :progetto_id,
                tipo_pratica = :tipo_pratica,
                tipo_documento = :tipo_documento,
                stato = :stato,
                data_scadenza = :data_scadenza,
                note = :note,
                numero_pratica = :numero_pratica,
                ente_riferimento = :ente_riferimento,
                protocollo = :protocollo,
                data_protocollo = :data_protocollo,
                data_scadenza_integrazione = :data_scadenza_integrazione,
                importo_diritti = :importo_diritti,
                note_interne = :note_interne,
                documenti_richiesti = :documenti_richiesti,
                responsabile = :responsabile
                WHERE id = :id";

            $stmt = $this->db->prepare($query);
            $allowed_keys = [
                'progetto_id', 'tipo_pratica', 'tipo_documento', 'stato',
                'data_scadenza', 'note', 'numero_pratica', 'ente_riferimento',
                'protocollo', 'data_protocollo', 'data_scadenza_integrazione',
                'importo_diritti', 'note_interne', 'documenti_richiesti',
                'responsabile', 'id'
            ];
            
            $filtered_data = array_intersect_key($data, array_flip($allowed_keys));
            
            // Gestione valori null per campi numerici
            if (isset($filtered_data['importo_diritti']) && empty($filtered_data['importo_diritti'])) {
                $filtered_data['importo_diritti'] = null;
            }
            
            error_log("PraticheModel::updatePratica() - Dati filtrati per execute: " . print_r($filtered_data, true));
            
            $success = $stmt->execute($filtered_data);
            if (!$success) {
                error_log("PraticheModel::updatePratica() - Errore SQL: " . print_r($stmt->errorInfo(), true));
            }
            
            error_log("PraticheModel::updatePratica() - Esito: " . ($success ? 'Successo' : 'Fallimento'));
            return $success;

        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::updatePratica(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Elimina una pratica (solo se non ha allegati collegati)
     */
    public function deletePratica(int $id): bool {
        try {
            // Controlla se esistono allegati collegati
            $stmt = $this->db->prepare("SELECT COUNT(*) as num_allegati FROM allegati WHERE pratica_id = :id");
            $stmt->execute([':id' => $id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result && $result['num_allegati'] > 0) {
                error_log("PraticheModel::deletePratica() - Impossibile eliminare pratica con allegati collegati");
                return false;
            }

            // Elimina la pratica
            $stmt = $this->db->prepare("DELETE FROM pratiche WHERE id = :id");
            $success = $stmt->execute([':id' => $id]);

            error_log("PraticheModel::deletePratica() - Esito: " . ($success ? 'Successo' : 'Fallimento'));
            return $success;

        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::deletePratica(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Aggiorna solo lo stato di una pratica con controllo workflow
     */
    public function updateStatoPratica(int $id, string $nuovo_stato, int $user_id = 1): bool {
        try {
            // Recupera lo stato attuale
            $stmt = $this->db->prepare("SELECT stato FROM pratiche WHERE id = :id");
            $stmt->execute([':id' => $id]);
            $pratica = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$pratica) {
                error_log("PraticheModel::updateStatoPratica() - Pratica non trovata: {$id}");
                return false;
            }

            $stato_attuale = $pratica['stato'];

            // Verifica se la transizione è consentita
            if (!$this->canTransition($stato_attuale, $nuovo_stato)) {
                error_log("PraticheModel::updateStatoPratica() - Transizione non consentita da '{$stato_attuale}' a '{$nuovo_stato}'");
                return false;
            }

            // Aggiorna lo stato
            $stmt = $this->db->prepare("UPDATE pratiche SET stato = :nuovo_stato WHERE id = :id");
            $success = $stmt->execute([
                ':nuovo_stato' => $nuovo_stato,
                ':id' => $id
            ]);

            if ($success) {
                error_log("PraticheModel::updateStatoPratica() - Stato aggiornato da '{$stato_attuale}' a '{$nuovo_stato}' per pratica {$id}");

                // Crea notifica per il cambio stato
                $this->createNotificaCambioStato($id, $stato_attuale, $nuovo_stato, $user_id);
            }

            return $success;

        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::updateStatoPratica(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Recupera le transizioni disponibili per uno stato
     */
    public function getAvailableTransitions(string $stato_attuale): array {
        return self::TRANSIZIONI_CONSENTITE[$stato_attuale] ?? [];
    }

    /**
     * Verifica se una transizione di stato è consentita
     */
    public function canTransition(string $stato_attuale, string $nuovo_stato): bool {
        $transizioni_disponibili = $this->getAvailableTransitions($stato_attuale);
        return in_array($nuovo_stato, $transizioni_disponibili);
    }

    /**
     * Recupera pratiche per progetto specifico
     */
    public function getPraticheByProgettoId(int $progetto_id): array {
        try {
            $query = "
                SELECT p.*,
                       pr.nome_progetto,
                       c.nome as cliente_nome,
                       c.cognome as cliente_cognome,
                       c.ragione_sociale as cliente_ragione_sociale,
                       c.tipo_cliente,
                       CASE
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale
                       END as cliente_nome_completo
                FROM pratiche p
                LEFT JOIN progetti pr ON p.progetto_id = pr.id
                LEFT JOIN clienti c ON pr.cliente_id = c.id
                WHERE p.progetto_id = :progetto_id
                ORDER BY p.data_apertura DESC";

            $stmt = $this->db->prepare($query);
            $stmt->execute([':progetto_id' => $progetto_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::getPraticheByProgettoId(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Recupera pratiche per stato
     */
    public function getPraticheByStato(string $stato): array {
        try {
            return $this->getAllPratiche(['stato' => $stato]);
        } catch (Exception $e) {
            error_log("Errore in PraticheModel::getPraticheByStato(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Recupera pratiche in scadenza
     */
    public function getPraticheInScadenza(int $giorni_anticipo = 7): array {
        try {
            $query = "
                SELECT p.*,
                       pr.nome_progetto,
                       c.nome as cliente_nome,
                       c.cognome as cliente_cognome,
                       c.ragione_sociale as cliente_ragione_sociale,
                       c.tipo_cliente,
                       CASE
                           WHEN c.tipo_cliente = 'privato' THEN CONCAT(c.nome, ' ', c.cognome)
                           ELSE c.ragione_sociale
                       END as cliente_nome_completo,
                       LEAST(
                           IFNULL(DATEDIFF(p.data_scadenza, CURDATE()), 999),
                           IFNULL(DATEDIFF(p.data_scadenza_integrazione, CURDATE()), 999)
                       ) as giorni_rimanenti
                FROM pratiche p
                LEFT JOIN progetti pr ON p.progetto_id = pr.id
                LEFT JOIN clienti c ON pr.cliente_id = c.id
                WHERE (
                    (p.data_scadenza IS NOT NULL AND
                     p.data_scadenza >= CURDATE() AND
                     p.data_scadenza <= DATE_ADD(CURDATE(), INTERVAL :giorni_anticipo DAY))
                    OR
                    (p.data_scadenza_integrazione IS NOT NULL AND
                     p.data_scadenza_integrazione >= CURDATE() AND
                     p.data_scadenza_integrazione <= DATE_ADD(CURDATE(), INTERVAL :giorni_anticipo DAY))
                )
                AND p.stato NOT IN ('completata', 'respinta')
                ORDER BY giorni_rimanenti ASC
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([':giorni_anticipo' => $giorni_anticipo]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::getPraticheInScadenza(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Recupera statistiche sulle pratiche
     */
    public function getStatistichePratiche(): array {
        try {
            $stats = [];

            // Pratiche per stato
            $stmt = $this->db->query("
                SELECT stato, COUNT(*) as count
                FROM pratiche
                GROUP BY stato
            ");
            $stats['per_stato'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // Pratiche per tipo documento
            $stmt = $this->db->query("
                SELECT tipo_documento, COUNT(*) as count
                FROM pratiche
                WHERE tipo_documento IS NOT NULL
                GROUP BY tipo_documento
            ");
            $stats['per_tipo_documento'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // Pratiche per ente
            $stmt = $this->db->query("
                SELECT ente_riferimento, COUNT(*) as count
                FROM pratiche
                WHERE ente_riferimento IS NOT NULL AND ente_riferimento != ''
                GROUP BY ente_riferimento
                ORDER BY count DESC
                LIMIT 10
            ");
            $stats['per_ente'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // Statistiche temporali
            $stmt = $this->db->query("
                SELECT
                    COUNT(*) as totale_pratiche,
                    SUM(CASE WHEN stato = 'completata' THEN 1 ELSE 0 END) as completate,
                    SUM(CASE WHEN data_scadenza < CURDATE() AND stato NOT IN ('completata', 'respinta') THEN 1 ELSE 0 END) as scadute,
                    SUM(CASE WHEN data_scadenza BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) AND stato NOT IN ('completata', 'respinta') THEN 1 ELSE 0 END) as in_scadenza_7gg,
                    AVG(CASE WHEN importo_diritti IS NOT NULL THEN importo_diritti ELSE 0 END) as importo_medio_diritti,
                    SUM(CASE WHEN importo_diritti IS NOT NULL THEN importo_diritti ELSE 0 END) as totale_diritti
                FROM pratiche
            ");
            $stats['generali'] = $stmt->fetch(PDO::FETCH_ASSOC);

            return $stats;

        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::getStatistichePratiche(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Conta pratiche per stato
     */
    public function countPraticheByStato(string $stato): int {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM pratiche WHERE stato = :stato");
            $stmt->execute([':stato' => $stato]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? (int)$result['count'] : 0;
        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::countPraticheByStato(): " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Verifica se una pratica può essere eliminata
     */
    public function canDeletePratica(int $id): bool {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) as num_allegati FROM allegati WHERE pratica_id = :id");
            $stmt->execute([':id' => $id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? $result['num_allegati'] == 0 : true;
        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::canDeletePratica(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Crea notifica per cambio stato pratica
     */
    private function createNotificaCambioStato(int $pratica_id, string $stato_precedente, string $nuovo_stato, int $user_id): void {
        try {
            // Usa il NotificationService se disponibile
            if (class_exists('App\Services\NotificationService')) {
                $notificationService = new \App\Services\NotificationService();
                $notificationService->createNotificaPratica($pratica_id, 'stato_cambiato', $user_id);
            } else {
                // Fallback: crea notifica direttamente
                $stmt = $this->db->prepare("
                    INSERT INTO notifiche (user_id, tipo, titolo, messaggio, priorita, link_azione, metadata)
                    VALUES (:user_id, 'pratica', :titolo, :messaggio, 'media', :link_azione, :metadata)
                ");

                $stmt->execute([
                    ':user_id' => $user_id,
                    ':titolo' => 'Cambio stato pratica',
                    ':messaggio' => "La pratica ID {$pratica_id} è passata da '{$stato_precedente}' a '{$nuovo_stato}'",
                    ':link_azione' => "/pratiche/dettagli/{$pratica_id}",
                    ':metadata' => json_encode([
                        'pratica_id' => $pratica_id,
                        'stato_precedente' => $stato_precedente,
                        'nuovo_stato' => $nuovo_stato
                    ])
                ]);
            }
        } catch (\Exception $e) {
            error_log("Errore in PraticheModel::createNotificaCambioStato(): " . $e->getMessage());
        }
    }

    /**
     * Recupera pratiche con scadenze critiche (entro 3 giorni)
     */
    public function getPraticheCritiche(): array {
        return $this->getPraticheInScadenza(3);
    }

    /**
     * Recupera pratiche per responsabile
     */
    public function getPraticheByResponsabile(string $responsabile): array {
        try {
            return $this->getAllPratiche(['responsabile' => $responsabile]);
        } catch (Exception $e) {
            error_log("Errore in PraticheModel::getPraticheByResponsabile(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Recupera pratiche per ente di riferimento
     */
    public function getPraticheByEnte(string $ente): array {
        try {
            $query = "
                SELECT p.*,
                       pr.nome_progetto,
                       c.nome as cliente_nome,
                       c.cognome as cliente_cognome,
                       c.ragione_sociale as cliente_ragione_sociale,
                       c.tipo_cliente
                FROM pratiche p
                LEFT JOIN progetti pr ON p.progetto_id = pr.id
                LEFT JOIN clienti c ON pr.cliente_id = c.id
                WHERE p.ente_riferimento = :ente
                ORDER BY p.data_apertura DESC
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([':ente' => $ente]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::getPraticheByEnte(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Aggiorna data scadenza di una pratica
     */
    public function updateDataScadenza(int $id, string $nuova_data_scadenza, string $tipo = 'principale'): bool {
        try {
            $campo = ($tipo === 'integrazione') ? 'data_scadenza_integrazione' : 'data_scadenza';

            $stmt = $this->db->prepare("UPDATE pratiche SET {$campo} = :nuova_data WHERE id = :id");
            $success = $stmt->execute([
                ':nuova_data' => $nuova_data_scadenza,
                ':id' => $id
            ]);

            if ($success) {
                error_log("PraticheModel::updateDataScadenza() - Data scadenza {$tipo} aggiornata per pratica {$id}");
            }

            return $success;

        } catch (PDOException $e) {
            error_log("Errore in PraticheModel::updateDataScadenza(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Recupera storico stati di una pratica (se implementato)
     */
    public function getStoricoStati(int $pratica_id): array {
        // TODO: Implementare tabella storico_stati_pratiche per tracking completo
        // Per ora restituisce array vuoto
        return [];
    }

    /**
     * Metodi di utilità per validazione
     */
    public function isValidStato(string $stato): bool {
        return array_key_exists($stato, self::STATI_PRATICHE);
    }

    public function getStatiDisponibili(): array {
        return self::STATI_PRATICHE;
    }

    public function getTransizioniConsentite(): array {
        return self::TRANSIZIONI_CONSENTITE;
    }
}
