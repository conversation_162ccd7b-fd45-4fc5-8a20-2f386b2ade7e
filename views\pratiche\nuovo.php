<?php include VIEWS_DIR . '/layouts/header.php'; ?>
<link rel="stylesheet" href="<?= BASE_URL ?>assets/css/management.css">

<div class="container-fluid py-4">
    <div class="page-header">
        <h2 class="page-title">
            <i class="fas fa-file-plus"></i>
            Nuova Pratica
        </h2>
        <div class="page-actions">
            <a href="<?= BASE_URL ?>pratiche" class="btn btn-outline-neutral">
                <i class="fas fa-arrow-left"></i>
                Torna alla lista
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="form-card">
                <form action="<?= BASE_URL ?>pratiche/salva" method="POST" id="praticaForm">
                    <?php echo $csrf_field; ?>
                    
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-info-circle"></i>
                            Informazioni Base
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="numero_pratica" class="form-label">Numero Pratica *</label>
                                <input type="text" class="form-control" id="numero_pratica" name="numero_pratica" 
                                       value="<?php echo isset($_POST['numero_pratica']) ? htmlspecialchars($_POST['numero_pratica']) : ''; ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="tipo_documento" class="form-label">Tipo Documento *</label>
                                <select class="form-select" id="tipo_documento" name="tipo_documento" required>
                                    <option value="">Seleziona tipo...</option>
                                    <option value="permesso_costruire" <?php echo (isset($_POST['tipo_documento']) && $_POST['tipo_documento'] === 'permesso_costruire') ? 'selected' : ''; ?>>Permesso di Costruire</option>
                                    <option value="scia" <?php echo (isset($_POST['tipo_documento']) && $_POST['tipo_documento'] === 'scia') ? 'selected' : ''; ?>>SCIA</option>
                                    <option value="cila" <?php echo (isset($_POST['tipo_documento']) && $_POST['tipo_documento'] === 'cila') ? 'selected' : ''; ?>>CILA</option>
                                    <option value="dia" <?php echo (isset($_POST['tipo_documento']) && $_POST['tipo_documento'] === 'dia') ? 'selected' : ''; ?>>DIA</option>
                                    <option value="altro" <?php echo (isset($_POST['tipo_documento']) && $_POST['tipo_documento'] === 'altro') ? 'selected' : ''; ?>>Altro</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-project-diagram"></i>
                            Progetto e Cliente
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="progetto_id" class="form-label">Progetto *</label>
                                <select class="form-select" id="progetto_id" name="progetto_id" required>
                                    <option value="">Seleziona progetto...</option>
                                    <?php foreach ($progetti as $progetto): ?>
                                        <option value="<?= $progetto['id'] ?>" 
                                            <?php echo (isset($_POST['progetto_id']) && $_POST['progetto_id'] == $progetto['id']) ? 'selected' : ''; ?>>
                                            <?= htmlspecialchars($progetto['nome_progetto']) ?> - 
                                            <?= htmlspecialchars($progetto['tipo_cliente'] === 'privato' ? $progetto['nome'] . ' ' . $progetto['cognome'] : $progetto['ragione_sociale']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="cliente" class="form-label">Cliente</label>
                                <input type="text" class="form-control" id="cliente" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-calendar-alt"></i>
                            Date e Scadenze
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="data_apertura" class="form-label">Data Apertura *</label>
                                <input type="date" class="form-control" id="data_apertura" name="data_apertura" 
                                       value="<?php echo isset($_POST['data_apertura']) ? $_POST['data_apertura'] : date('Y-m-d'); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="data_scadenza" class="form-label">Data Scadenza</label>
                                <input type="date" class="form-control" id="data_scadenza" name="data_scadenza" 
                                       value="<?php echo isset($_POST['data_scadenza']) ? $_POST['data_scadenza'] : ''; ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-tasks"></i>
                            Stato e Priorità
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="stato" class="form-label">Stato *</label>
                                <select class="form-select" id="stato" name="stato" required>
                                    <option value="">Seleziona stato</option>
                                    <option value="in_corso" <?php echo (isset($_POST['stato']) && $_POST['stato'] === 'in_corso') ? 'selected' : ''; ?>>In Corso</option>
                                    <option value="completata" <?php echo (isset($_POST['stato']) && $_POST['stato'] === 'completata') ? 'selected' : ''; ?>>Completata</option>
                                    <option value="sospesa" <?php echo (isset($_POST['stato']) && $_POST['stato'] === 'sospesa') ? 'selected' : ''; ?>>Sospesa</option>
                                    <option value="annullata" <?php echo (isset($_POST['stato']) && $_POST['stato'] === 'annullata') ? 'selected' : ''; ?>>Annullata</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="priorita" class="form-label">Priorità *</label>
                                <select class="form-select" id="priorita" name="priorita" required>
                                    <option value="bassa">Bassa</option>
                                    <option value="media">Media</option>
                                    <option value="alta">Alta</option>
                                    <option value="urgente">Urgente</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-file-upload"></i>
                            Documenti
                        </h3>
                        <div class="row g-3">
                            <div class="col-12">
                                <label for="documenti" class="form-label">Allega Documenti</label>
                                <input type="file" class="form-control" id="documenti" name="documenti[]" multiple>
                                <div class="form-text">Puoi selezionare più file. Formati supportati: PDF, DOC, DOCX, JPG, PNG</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-sticky-note"></i>
                            Note e Descrizione
                        </h3>
                        <div class="row g-3">
                            <div class="col-12">
                                <label for="descrizione" class="form-label">Descrizione</label>
                                <textarea class="form-control" id="descrizione" name="descrizione" rows="4"><?php echo isset($_POST['descrizione']) ? htmlspecialchars($_POST['descrizione']) : ''; ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="text-end mt-4">
                        <button type="submit" class="btn btn-neutral">
                            <i class="fas fa-save"></i>
                            Salva Pratica
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const progettoSelect = document.getElementById('progetto_id');
    const clienteInput = document.getElementById('cliente');
    const progetti = <?= json_encode($progetti) ?>;

    progettoSelect.addEventListener('change', function() {
        const progettoId = this.value;
        const progetto = progetti.find(p => p.id === progettoId);
        clienteInput.value = progetto ? progetto.tipo_cliente === 'privato' ? progetto.nome + ' ' + progetto.cognome : progetto.ragione_sociale : '';
    });

    // Imposta la data di apertura al giorno corrente
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('data_apertura').value = today;
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('praticaForm');
    const dataApertura = document.getElementById('data_apertura');
    const dataScadenza = document.getElementById('data_scadenza');
    
    form.addEventListener('submit', function(e) {
        if (dataScadenza.value && dataScadenza.value < dataApertura.value) {
            e.preventDefault();
            alert('La data di scadenza non può essere precedente alla data di apertura');
        }
    });
});
</script>
<?php include VIEWS_DIR . '/layouts/footer.php'; ?>