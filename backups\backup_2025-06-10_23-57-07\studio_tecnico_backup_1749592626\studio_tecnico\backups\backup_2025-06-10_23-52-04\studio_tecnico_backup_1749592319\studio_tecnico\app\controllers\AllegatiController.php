<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\Allegato;

class AllegatiController extends Controller {
    private $allegato;
    private $uploadDir;
    private $maxFileSize = 10485760; // 10MB
    private $allowedTypes = [
        'image/jpeg' => 'jpg',
        'image/png' => 'png',
        'application/pdf' => 'pdf',
        'application/msword' => 'doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
        'application/vnd.ms-excel' => 'xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx'
    ];

    public function __construct() {
        parent::__construct();
        $this->allegato = new Allegato();
        $this->uploadDir = ROOT_PATH . '/uploads/allegati/';
        
        if (!file_exists($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }

    public function upload() {
        if (!$this->security->validateCSRFToken()) {
            $this->jsonResponse(['error' => 'Token CSRF non valido'], 403);
            return;
        }

        try {
            if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
                throw new \Exception('Errore nel caricamento del file');
            }

            $file = $_FILES['file'];
            $praticaId = filter_input(INPUT_POST, 'pratica_id', FILTER_VALIDATE_INT);
            $descrizione = filter_input(INPUT_POST, 'descrizione', FILTER_SANITIZE_STRING);
            $categoria = filter_input(INPUT_POST, 'categoria', FILTER_SANITIZE_STRING);

            // Validazione base
            if (!$praticaId) {
                throw new \Exception('ID pratica non valido');
            }

            // Validazione tipo file
            if (!isset($this->allowedTypes[$file['type']])) {
                throw new \Exception('Tipo di file non supportato');
            }

            // Validazione dimensione
            if ($file['size'] > $this->maxFileSize) {
                throw new \Exception('Il file supera la dimensione massima consentita (10MB)');
            }

            // Generazione nome file univoco
            $extension = $this->allowedTypes[$file['type']];
            $fileName = uniqid() . '.' . $extension;
            $filePath = $this->uploadDir . $fileName;

            // Spostamento file
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                throw new \Exception('Errore nel salvataggio del file');
            }

            // Salvataggio nel database
            $data = [
                'pratica_id' => $praticaId,
                'nome_file' => $file['name'],
                'percorso_file' => $filePath,
                'tipo_file' => $file['type'],
                'dimensione' => $file['size'],
                'data_caricamento' => date('Y-m-d H:i:s'),
                'descrizione' => $descrizione,
                'categoria' => $categoria ?: 'generale',
                'utente_id' => $_SESSION['utente_id'] ?? null
            ];

            if (!$this->allegato->create($data)) {
                unlink($filePath);
                throw new \Exception('Errore nel salvataggio dei dati del file');
            }

            $this->jsonResponse([
                'success' => true,
                'message' => 'File caricato con successo',
                'file' => [
                    'name' => $file['name'],
                    'size' => $this->formatFileSize($file['size']),
                    'type' => $extension
                ]
            ]);
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    public function delete($id) {
        if (!$this->security->validateCSRFToken()) {
            $this->jsonResponse(['error' => 'Token CSRF non valido'], 403);
            return;
        }

        try {
            if (!$this->allegato->delete($id)) {
                throw new \Exception('Errore nella cancellazione del file');
            }

            $this->jsonResponse([
                'success' => true,
                'message' => 'File eliminato con successo'
            ]);
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    public function download($id) {
        try {
            $allegato = $this->allegato->findById($id);
            if (!$allegato || !file_exists($allegato['percorso_file'])) {
                throw new \Exception('File non trovato');
            }

            $fileName = basename($allegato['nome_file']);
            $fileSize = filesize($allegato['percorso_file']);

            header('Content-Type: ' . $allegato['tipo_file']);
            header('Content-Disposition: attachment; filename="' . $fileName . '"');
            header('Content-Length: ' . $fileSize);
            header('Cache-Control: no-cache, must-revalidate');
            header('Pragma: no-cache');
            header('Expires: 0');

            readfile($allegato['percorso_file']);
            exit;
        } catch (\Exception $e) {
            $this->redirect('errors/404');
        }
    }

    public function getByPratica($praticaId) {
        try {
            $allegati = $this->allegato->findByPraticaId($praticaId);
            foreach ($allegati as &$allegato) {
                $allegato['dimensione_formattata'] = $this->formatFileSize($allegato['dimensione']);
                $allegato['data_caricamento_formattata'] = date('d/m/Y H:i', strtotime($allegato['data_caricamento']));
            }

            $this->jsonResponse([
                'success' => true,
                'allegati' => $allegati
            ]);
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    private function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}
