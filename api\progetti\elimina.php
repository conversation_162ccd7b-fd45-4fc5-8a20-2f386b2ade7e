<?php
require_once '../config.php';
requirePost();

try {
    if (empty($_POST['id'])) {
        sendError('ID progetto non specificato');
    }

    $conn->beginTransaction();

    // Elimina prima tutte le pratiche associate
    $stmt = $conn->prepare("DELETE FROM pratiche WHERE progetto_id = ?");
    $stmt->execute([$_POST['id']]);

    // Poi elimina il progetto
    $stmt = $conn->prepare("DELETE FROM progetti WHERE id = ?");
    $stmt->execute([$_POST['id']]);

    $conn->commit();

    sendResponse([
        'success' => true,
        'message' => 'Progetto e pratiche associate eliminati con successo'
    ]);
} catch (PDOException $e) {
    $conn->rollBack();
    sendError('Errore nell\'eliminazione del progetto: ' . $e->getMessage());
} 