<?php
require_once dirname(dirname(__DIR__)) . '/config/config.php';
require_once ROOT_PATH . '/config/database.php';

if (isset($_GET['id'])) {
    try {
        // Prima verifichiamo se ci sono pratiche associate
        $stmt = $conn->prepare("SELECT COUNT(*) FROM pratiche WHERE progetto_id = ?");
        $stmt->execute([$_GET['id']]);
        $pratiche_associate = $stmt->fetchColumn();
        
        if ($pratiche_associate > 0) {
            header("Location: index.php?error=progetto_con_pratiche");
            exit;
        }
        
        // Se non ci sono pratiche, procediamo con l'eliminazione
        $stmt = $conn->prepare("DELETE FROM progetti WHERE id = ?");
        $stmt->execute([$_GET['id']]);
        
        header("Location: index.php?msg=delete_success");
    } catch (PDOException $e) {
        header("Location: index.php?error=delete_error");
    }
} else {
    header("Location: index.php");
}
exit;