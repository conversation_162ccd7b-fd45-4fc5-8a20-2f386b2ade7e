<?php
$pageTitle = "Dettagli Pratica";
include VIEWS_DIR . '/layouts/header.php';
?>

<div class="container-fluid py-4">
    <!-- Intestazione -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="page-title">
            <i class="fas fa-folder-open"></i>
            Pratica: <?= htmlspecialchars($pratica['numero_pratica']) ?>
        </h2>
        <div>
            <a href="<?= BASE_URL ?>pratiche" class="btn btn-outline-primary me-2">
                <i class="fas fa-arrow-left"></i> Torna alla Lista
            </a>
            <a href="<?= BASE_URL ?>pratiche/modifica/<?= $pratica['id'] ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Modifica
            </a>
        </div>
    </div>

    <!-- Informazioni Pratica -->
    <div class="row">
        <!-- Col<PERSON>na sinistra -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h4><i class="fas fa-info-circle"></i> Informazioni Pratica</h4>
                </div>
                <div class="card-body">
                    <p><strong>Numero Pratica:</strong> <?= htmlspecialchars($pratica['numero_pratica']) ?></p>
                    <p><strong>Tipo Documento:</strong> <?= htmlspecialchars(ucfirst(str_replace('_', ' ', $pratica['tipo_documento'] ?? 'Non specificato'))) ?></p>
                    <p><strong>Stato:</strong> 
                        <span class="badge bg-<?= $pratica['stato'] === 'completata' ? 'success' : 
                                            ($pratica['stato'] === 'in_corso' ? 'warning' : 'secondary') ?>">
                            <?= ucfirst(str_replace('_', ' ', $pratica['stato'])) ?>
                        </span>
                    </p>
                    <p><strong>Data Apertura:</strong> <?= date('d/m/Y', strtotime($pratica['data_apertura'])) ?></p>
                    <?php if (!empty($pratica['data_chiusura'])): ?>
                        <p><strong>Data Chiusura:</strong> <?= date('d/m/Y', strtotime($pratica['data_chiusura'])) ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Colonna destra -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h4><i class="fas fa-project-diagram"></i> Informazioni Progetto</h4>
                </div>
                <div class="card-body">
                    <p><strong>Nome Progetto:</strong> <?= htmlspecialchars($pratica['nome_progetto']) ?></p>
                    <p><strong>Cliente:</strong> 
                        <?php if ($pratica['tipo_cliente'] === 'privato'): ?>
                            <?= htmlspecialchars($pratica['cliente_nome'] . ' ' . $pratica['cliente_cognome']) ?>
                        <?php else: ?>
                            <?= htmlspecialchars($pratica['cliente_ragione_sociale']) ?>
                        <?php endif; ?>
                    </p>
                    <p>
                        <a href="<?= BASE_URL ?>progetti/dettagli/<?= $pratica['progetto_id'] ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-external-link-alt"></i> Vai al Progetto
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Note -->
    <?php if (!empty($pratica['note'])): ?>
    <div class="card mb-4">
        <div class="card-header">
            <h4><i class="fas fa-sticky-note"></i> Note</h4>
        </div>
        <div class="card-body">
            <?= nl2br(htmlspecialchars($pratica['note'])) ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Documenti allegati (se implementati) -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4><i class="fas fa-file"></i> Documenti</h4>
            <button class="btn btn-primary btn-sm" disabled>
                <i class="fas fa-plus"></i> Aggiungi Documento
            </button>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Funzionalità in sviluppo
            </div>
        </div>
    </div>
</div>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>
