const PraticheManager = {
    init() {
        this.bindEvents();
        this.initFormValidation();
    },

    bindEvents() {
        // Gestione eliminazione pratica
        $('.delete-pratica').on('click', this.handleDelete.bind(this));
        
        // Aggiornamento stato pratica
        $('.update-stato').on('change', this.handleStatoUpdate.bind(this));
        
        // Filtro progetti in base al cliente selezionato
        $('#cliente_id').on('change', this.filterProgetti.bind(this));
        
        // Generazione automatica numero pratica
        $('#tipo_documento').on('change', this.generateNumeroPratica.bind(this));
    },

    initFormValidation() {
        const form = document.querySelector('#praticaForm');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    },

    async handleDelete(e) {
        e.preventDefault();
        const id = $(e.currentTarget).data('id');
        
        try {
            const result = await StudioApp.confirm({
                text: 'Questa pratica verrà eliminata definitivamente.',
                confirmText: 'Sì, elimina'
            });

            if (result.isConfirmed) {
                const response = await $.ajax({
                    url: `/api/pratiche/elimina.php`,
                    method: 'POST',
                    data: { id }
                });

                if (response.success) {
                    StudioApp.notify('Pratica eliminata con successo');
                    window.location.reload();
                }
            }
        } catch (error) {
            StudioApp.handleError(error);
        }
    },

    async handleStatoUpdate(e) {
        const id = $(e.currentTarget).data('id');
        const stato = $(e.currentTarget).val();
        
        try {
            const response = await $.ajax({
                url: `/api/pratiche/aggiorna-stato.php`,
                method: 'POST',
                data: { id, stato }
            });

            if (response.success) {
                StudioApp.notify('Stato aggiornato con successo');
            }
        } catch (error) {
            StudioApp.handleError(error);
        }
    },

    async filterProgetti(e) {
        const clienteId = $(e.currentTarget).val();
        const progettiSelect = $('#progetto_id');
        
        try {
            const response = await $.ajax({
                url: `/api/progetti/lista.php`,
                data: { cliente_id: clienteId }
            });

            progettiSelect.empty();
            progettiSelect.append('<option value="">Seleziona progetto...</option>');
            
            response.progetti.forEach(progetto => {
                progettiSelect.append(`
                    <option value="${progetto.id}">
                        ${progetto.nome_progetto}
                    </option>
                `);
            });

            progettiSelect.trigger('change');
        } catch (error) {
            StudioApp.handleError(error);
        }
    },

    generateNumeroPratica(e) {
        const tipo = $(e.currentTarget).val();
        const anno = new Date().getFullYear();
        const prefix = tipo.toUpperCase();
        
        $.get(`/api/pratiche/ultimo-numero.php?tipo=${tipo}&anno=${anno}`)
            .then(response => {
                const numero = (response.ultimo_numero || 0) + 1;
                const numeroPratica = `${prefix}${anno}/${numero.toString().padStart(3, '0')}`;
                $('#numero_pratica').val(numeroPratica);
            })
            .catch(StudioApp.handleError);
    }
};

$(document).ready(() => {
    PraticheManager.init();
}); 